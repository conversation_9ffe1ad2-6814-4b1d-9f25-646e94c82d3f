<?php

namespace App\Http\Controllers\Backend;

use App\Models\Current;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Stock;
use App\Models\StockReservation;
use App\Models\StockReservationReason;
use App\Models\StockReservationType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class StockReservationController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Stok Rezervasyonları';
        $this->page = 'stock_reservation';
        $this->model = new StockReservation();
        $this->relation = ['stock', 'product', 'variant', 'current', 'reservationReason', 'reservationType'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Rezervasyonları' => route('backend.stock_reservation_list'),
            ),
        );

        view()->share('stocks', Stock::with(['product', 'variant'])->active()->get());
        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('current', Current::active()->get());
        view()->share('reservationReasons', StockReservationReason::with('stockReservationType')->active()->get());
        view()->share('reservationTypes', StockReservationType::active()->get());

        parent::__construct();
    }

    public function save(Request $request, $unique = null)
    {
        if (isset($this->validation) && is_array($this->validation) && count($this->validation) > 0) {
            $validator = Validator::make($request->all(), $this->validation[0], $this->validation[1] ?? []);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }
        }

        $params = $this->saveHook($request, $unique);

        if (is_null($unique)) {
            $obj = $this->model::create($params);
        } else {
            $obj = $this->model::find((int)$unique);
            $obj->update($params);
        }

        Cache::flush();
        return redirect()->route("backend." . $this->page . "_list")->with('success', 'Kayıt başarılı şekilde işlendi');
    }

    public function saveHook($request, $unique = null)
    {
        $params = $request->all();

        // Tarih validasyonu
        if (isset($params['end_date']) && isset($params['start_date'])) {
            if ($params['end_date'] < $params['start_date']) {
                throw new \Exception('Bitiş tarihi başlangıç tarihinden önce olamaz');
            }
        }

        // Miktar validasyonu
        if (isset($params['quantity']) && $params['quantity'] <= 0) {
            throw new \Exception('Rezerve miktarı 0\'dan büyük olmalıdır');
        }

        // Öncelik seviyesi validasyonu
        if (isset($params['priority_level'])) {
            if ($params['priority_level'] < 1 || $params['priority_level'] > 10) {
                throw new \Exception('Öncelik seviyesi 1-10 arasında olmalıdır');
            }
        }

        if (!isset($params['start_date'])) {
            $params['start_date'] = now();
        }

        if (isset($params['stock_id'])) {
            $stock = Stock::find($params['stock_id']);

            if (!$stock) {
                throw new \Exception('Stok kaydı bulunamadı');
            }

            $params['product_id'] = $stock->product_id;
            $params['variant_id'] = $stock->variant_id;

            $reservationType = $params['reservation_type_id'] ?? null;
            if ($reservationType == 1) {
                $excludeReservationId = $unique ? (int)$unique : null;
                $availableQuantity = $this->calculateAvailableQuantity($stock, $excludeReservationId);

                if ($availableQuantity < $params['quantity']) {
                    throw new \Exception('Satış rezervasyonu için yetersiz kullanılabilir stok. Mevcut: ' . $availableQuantity . ', İstenen: ' . $params['quantity']);
                }
            }
        }

        return $params;
    }

    private function calculateAvailableQuantity($stock, $excludeReservationId = null)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        if ($excludeReservationId) {
            $baseQuery->where('id', '!=', $excludeReservationId);
        }

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');

        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $stock->quantity - $salesReservations + $purchaseReservations;
    }

    public function status(Request $request)
    {
        $reservation = StockReservation::find($request->id);

        if (!$reservation) {
            return response()->json(['status' => false, 'message' => 'Rezervasyon bulunamadı']);
        }

        DB::beginTransaction();

        try {
            if ($request->status == 0) {
                $reservation->is_active = 0;
                $reservation->end_date = now();
                $reservation->notes = $reservation->notes . "\nRezervasyon iptal edildi: " . now();
            }
            else if ($request->status == 1) {
                if ($reservation->reservation_type_id == 1) {
                    $availableQuantity = $this->calculateAvailableQuantity($reservation->stock, $reservation->id);

                    if ($availableQuantity < $reservation->quantity) {
                        throw new \Exception('Satış rezervasyonu için yetersiz kullanılabilir stok. Mevcut: ' . $availableQuantity . ', İstenen: ' . $reservation->quantity);
                    }
                }

                $reservation->is_active = 1;
                $reservation->end_date = null;
                $reservation->notes = $reservation->notes . "\nRezervasyon tekrar aktif edildi: " . now();
            }

            $reservation->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Rezervasyon durumu güncellendi']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Rezervasyon kaydedilirken bir hata oluştu']);
        }
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('available_quantity', function ($item) {
            if ($item->stock) {
                return $this->calculateAvailableQuantity($item->stock);
            }
            return '-';
        });
    }
}
