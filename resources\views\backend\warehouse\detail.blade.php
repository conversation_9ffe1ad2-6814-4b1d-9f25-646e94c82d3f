@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = $warehouse->name . ' - Detay';
@endphp

@section('content')
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">De<PERSON>i</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.warehouse_form', $warehouse->id) }}"
                   class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                    Düzenle
                </a>
                <a href="{{ route('backend.warehouse_list') }}"
                   class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                    Geri
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="30%"><strong>Depo <PERSON>:</strong></td>
                            <td>{{ $warehouse->code }}</td>
                        </tr>
                        <tr>
                            <td><strong>Depo Adı:</strong></td>
                            <td>{{ $warehouse->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Lokasyon Sayısı:</strong></td>
                            <td>{{ $warehouse->locations->count() }}</td>
                        </tr>
                        <tr>
                            <td width="30%"><strong>Adres:</strong></td>
                            <td>{{ $warehouse->address ?: '-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td><strong>Mevcut Ağırlık:</strong></td>
                            <td>{{ number_format($warehouse->current_weight ?? 0, 2, ',', '.') }} kg</td>
                        </tr>
                        <tr>
                            <td><strong>Maksimum Ağırlık:</strong></td>
                            <td>{{ number_format($warehouse->max_weight_capacity ?? 0, 2, ',', '.') }} kg</td>
                        </tr>
                        <tr>
                            <td><strong>Mevcut Hacim:</strong></td>
                            <td>{{ number_format($warehouse->current_volume ?? 0, 2, ',', '.') }} m³</td>
                        </tr>
                        <tr>
                            <td><strong>Maksimum Hacim:</strong></td>
                            <td>{{ number_format($warehouse->max_volume_capacity ?? 0, 2, ',', '.') }} m³</td>
                        </tr>
                        <tr>
                            <td><strong>Durum:</strong></td>
                            <td>
                                @if($warehouse->is_active)
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>
                                @else
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @if($warehouse->locations->count() > 0)
    <div class="card basic-data-table mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Depo Lokasyonları</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="locationsTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Lokasyon Kodu</th>
                            <th scope="col">Lokasyon Adı</th>
                            <th scope="col">Üst Lokasyon</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if($warehouse->stocks->count() > 0)
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Stok Özeti</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="stocksTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Ürün</th>
                            <th scope="col">Varyant</th>
                            <th scope="col">Lokasyon</th>
                            <th scope="col" class="text-center">Miktar</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "#locationsTable";
            var locationsTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.warehouse_detail', $warehouse->id) }}?datatable=locations",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'code',
                        name: 'code'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'parent.name',
                        name: 'parent.name',
                        defaultContent: '-',
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return `
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.warehouse_location_detail', '') }}/${row.id}" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.warehouse_location_form', '') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                            </div>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });

            BaseCRUD.selector = "#stocksTable";
            var stocksTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.warehouse_detail', $warehouse->id) }}?datatable=stocks",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'product.name',
                        name: 'product.name',
                        className: 'text-center',
                    },
                    {
                        data: 'variant.name',
                        name: 'variant.name',
                        defaultContent: '-',
                        className: 'text-center',
                    },
                    {
                        data: 'location_name',
                        name: 'location_name',
                        defaultContent: 'Ana Lokasyon',
                        className: 'text-center',
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center',
                    },
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });

            setTimeout(function() {
                $('#locationsTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    locationsTable.search(this.value).draw();
                });

                $('#stocksTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    stocksTable.search(this.value).draw();
                });
            }, 300);
        });
    </script>
@endsection
