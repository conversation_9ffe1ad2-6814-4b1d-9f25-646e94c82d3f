<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Production'da Telescope'u aktifleştir
        Telescope::night();

        $this->hideSensitiveRequestDetails();

        // TÜM ortamlarda sadece hataları logla
        Telescope::filter(function (IncomingEntry $entry) {
            return $entry->isReportableException() ||
                $entry->isFailedRequest() ||
                $entry->isFailedJob();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        parent::boot();

        // Telescope'u sadece belirli kullanıcılara göster
        $this->gate();
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     */
    protected function hideSensitiveRequestDetails(): void
    {
        // Tüm ortamlarda hassas verileri gizle (local dahil)
        Telescope::hideRequestParameters([
            '_token',
            'password',
            'password_confirmation',
            'credit_card',
        ]);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
            'authorization', // OAuth token'larını gizler
        ]);
    }

    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     */
    protected function gate(): void
    {
        Gate::define('viewTelescope', function ($user = null) {
            // Kullanıcı giriş yapmamışsa erişim engelle
            if (!$user) {
                return false;
            }

            // İzinli email'ler listesi
            $allowedEmails = [
                '<EMAIL>',
            ];

            // Kullanıcının email'i izinli listesinde mi kontrol et
            return in_array($user->email, $allowedEmails);
        });
    }

    /**
     * Determine if Telescope should register its migrations.
     */
    public function shouldRegisterMigrations(): bool
    {
        // Production'da migration'ların çalışmasını engelle
        return $this->app->environment('local', 'staging');
    }
}
