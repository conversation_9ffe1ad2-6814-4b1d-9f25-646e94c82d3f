<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_locations', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->string('code', 50)->nullable();
            $table->longText('description')->nullable();
            $table->integer('warehouse_id');
            $table->integer('parent_id')->nullable();
            $table->decimal('max_weight_capacity', 12, 3)->default(0)->nullable();
            $table->decimal('max_volume_capacity', 12, 4)->default(0)->nullable();
            $table->decimal('current_weight', 12, 3)->default(0)->nullable();
            $table->decimal('current_volume', 12, 4)->default(0)->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_locations');
    }
};
