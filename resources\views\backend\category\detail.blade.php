@extends('layout.layout')

@php
    $title = $container->title ?? 'Kategori';
    $subTitle = $container->title . ' Detayı';
@endphp

@section('content')
    <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $item->name }} - Detay Bilgileri</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.' . $container->page . '_form', $item->id) }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                    Düzenle
                </a>
                <a href="{{ route('backend.' . $container->page . '_list') }}"
                    class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                    Geri
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="40%"><strong>Kate<PERSON><PERSON> Adı:</strong></td>
                            <td>{{ $item->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Üst Kategori:</strong></td>
                            <td>{{ $item->parent ? $item->parent->name : 'Ana Kategori' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Açıklama:</strong></td>
                            <td>{{ $item->description ?? '-' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="40%"><strong>Alt Kategori Sayısı:</strong></td>
                            <td>{{ $item->children()->count() }}</td>
                        </tr>
                        <tr>
                            <td><strong>Ürün Sayısı:</strong></td>
                            <td>{{ $item->products()->count() }}</td>
                        </tr>
                        <tr>
                            <td><strong>Durum:</strong></td>
                            <td>
                                @if ($item->is_active)
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>
                                @else
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Oluşturma Tarihi:</strong></td>
                            <td>{{ $item->created_at ? $item->created_at->format('d.m.Y H:i') : '-' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @if ($item->children()->count() > 0)
    <div class="card basic-data-table mb-3">
        <div class="card-header">
            <h5 class="card-title mb-0">Alt Kategoriler</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="subcategoriesTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Kategori Adı</th>
                            <th scope="col" class="text-center">Alt Kategori Sayısı</th>
                            <th scope="col" class="text-center">Ürün Sayısı</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if ($item->products()->count() > 0)
    <div class="card basic-data-table">
        <div class="card-header">
            <h5 class="card-title mb-0">Bu Kategorideki Ürünler</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="productsTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Ürün Adı</th>
                            <th scope="col">SKU</th>
                            <th scope="col">Marka</th>
                            <th scope="col" class="text-center">Birim</th>
                            <th scope="col" class="text-center">Satış Fiyatı</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "#subcategoriesTable";
            var subcategoriesTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=subcategories",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-center',
                    },
                    {
                        data: 'children_count',
                        name: 'children_count',
                        className: 'text-center',
                        searchable: false
                    },
                    {
                        data: 'products_count',
                        name: 'products_count',
                        className: 'text-center',
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return `
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.category_detail', '') }}/${row.id}" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.category_form', '') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                            </div>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });

            BaseCRUD.selector = "#productsTable";
            var productsTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=products",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-center',
                    },
                    {
                        data: 'sku',
                        name: 'sku',
                        className: 'text-center',
                    },
                    {
                        data: 'brand.name',
                        name: 'brand.name',
                        className: 'text-center',
                        defaultContent: '-'
                    },
                    {
                        data: 'unit.symbol',
                        name: 'unit.symbol',
                        className: 'text-center',
                        defaultContent: '-'
                    },
                    {
                        data: 'sale_price',
                        name: 'sale_price',
                        className: 'text-center',
                        render: function(data, type, row) {
                            return parseFloat(data).toFixed(2).replace('.', ',') + ' ' + (row.sale_currency_code || 'TRY');
                        }
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return `
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.product_detail', '') }}/${row.id}" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.product_form', '') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                            </div>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });
        });
    </script>
@endsection
