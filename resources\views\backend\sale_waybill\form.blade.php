@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <form id="orderForm" action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="waybill_type" value="2">

                <!-- Üst Bilgiler -->
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <!-- İrsaliye No -->
                            <div class="col-md-4">
                                <label class="form-label">İrsaliye No:</label>
                                <input type="text" class="form-control" id="waybill_no" name="waybill_no"
                                    value="{{ old('waybill_no', $item->id ? $item->waybill_no : \App\Models\Waybill::generatePreviewSaleWaybillNumber()) }}"
                                    {{ $item->id ? 'readonly' : '' }} readonly>
                            </div>

                            <!-- İrsaliye Tarihi -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="waybill_date" class="form-label">İrsaliye Tarihi <span
                                            class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="waybill_date" id="waybill_date"
                                        value="{{ old('waybill_date', $item->waybill_date ? $item->waybill_date->format('Y-m-d') : '') }}">
                                    <x-form-error field="waybill_date" />
                                </div>
                            </div>

                            <!-- Cari Hesap -->
                            <div class="col-md-4">
                                <label class="form-label">Cari Hesap:</label>
                                <input type="text" class="form-control" id="current_title" value="{{ $item && $item->current ? $item->current->title : '' }}" readonly>
                            </div>
                        </div>

                        <!-- Fatura ve Durum -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="invoice_id" class="form-label">Fatura Seç</label>
                                    <div class="input-group">
                                        <select class="form-control select2" id="invoice_id" name="invoice_id">
                                            <option value="">Fatura Seçiniz</option>
                                            @foreach ($invoice as $inv)
                                                <option value="{{ $inv->id }}" data-current-id="{{ $inv->current_id }}"
                                                    data-items='@json($inv->items_for_js)'
                                                    data-net-amount="{{ $inv->net_amount }}"
                                                    data-tax-amount="{{ $inv->tax_amount }}"
                                                    data-total-amount="{{ $inv->total_amount }}"
                                                    {{ old('invoice_id', $item ? $item->invoice_id : '') == $inv->id ? 'selected' : '' }}>
                                                    {{ $inv->invoice_no }} - {{ $inv->current->title }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <x-form-error field="invoice_id" />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Durum</label>
                                    <select class="form-select" id="status" name="status">
                                        @foreach ($waybillStatu as $status)
                                            <option value="{{ $status->id }}"
                                                {{ $item->waybill_statu_id == $status->id ? 'selected' : '' }}>
                                                {{ $status->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Seçili Fatura Bilgisi -->
                        <div class="row mb-3" id="selected_invoice_info"
                            style="{{ old('invoice_id', $item ? $item->invoice_id : '') ? '' : 'display: none;' }}">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    Seçili Fatura: <strong
                                        id="selected_invoice_no">{{ old('invoice_no', $item ? $item->invoice_no : '') }}</strong>
                                </div>
                            </div>
                        </div>

                        <!-- Not -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="note" class="form-label">Not</label>
                                    <textarea class="form-control" id="note" name="note" rows="3">{{ old('note', $item ? $item->note : '') }}</textarea>
                                    <x-form-error field="note" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Kalemler -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0 fs-6">İrsaliye Kalemleri</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table bordered-table mb-0" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th>Ürün</th>
                                        <th>Miktar</th>
                                        <th>Birim Fiyat</th>
                                        <th>KDV Oranı (%)</th>
                                        <th>KDV Tutarı</th>
                                        <th>Toplam</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- JS ile doldurulacak -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Toplam -->
                        <div class="row mt-3">
                            <div class="col-md-6 ms-auto">
                                <div class="mb-3 text-end">
                                    <label for="net_amount" class="form-label">Ara Toplam</label>
                                    <input type="number" class="form-control" id="net_amount" name="net_amount"
                                        value="{{ old('net_amount', $item ? $item->net_amount : 0 ) }}" step="0.01"
                                        min="0" readonly>
                                </div>
                                <div class="mb-3 text-end">
                                    <label for="tax_amount" class="form-label">KDV </label>
                                    <input type="number" class="form-control" id="tax_amount" name="tax_amount"
                                        value="{{ old('tax_amount', $item ? $item->tax_amount : 0) }}" step="0.01"
                                        min="0" readonly>
                                </div>
                                <div class="mb-3 text-end">
                                    <label for="total_amount" class="form-label">Toplam Tutar</label>
                                    <input type="number" class="form-control" id="total_amount" name="total_amount"
                                        value="{{ old('total_amount', $item ? $item->total_amount : 0) }}" step="0.01"
                                        min="0" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 text-end">
                            <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">İptal</a>
                            <button type="submit" class="btn btn-primary">Kaydet</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            function updateInvoiceDetails() {
                const selectedInvoice = $('#invoice_id option:selected');
                const currentId = selectedInvoice.data('current-id');
                const items = selectedInvoice.data('items');
                let totalAmount = 0;
                let currentTitle = selectedInvoice.text().split(' - ').slice(1).join(' - ');

                // Cari Güncelle
                $('#current_title').val(currentTitle);

                const itemsTable = $('#itemsTable tbody');
                itemsTable.empty();

                if (items) {
                    items.forEach((item, index) => {
                        const quantity = parseFloat(item.quantity);
                        const unitPrice = parseFloat(item.unit_price);
                        const vatRate = parseFloat(item.vat_rate);
                        const vatAmount = (unitPrice * quantity * vatRate) / 100;
                        const total = (unitPrice * quantity) + vatAmount;
                        totalAmount += total;

                        // Ürün adı ve varyantı
                        let productName = item.product_name;
                        if (item.product_variant_name && item.product_variant_name !== '') {
                            productName += ' / ' + item.product_variant_name;
                        }

                        const row = `
                        <tr>
                            <td>
                                <input type="text" class="form-control" value="${productName}" readonly>
                                <input type="hidden" name="items[${index}][stock_id]" value="${item.stock_id || ''}">
                                <input type="hidden" name="items[${index}][product_id]" value="${item.product_id}">
                                <input type="hidden" name="items[${index}][product_variant_id]" value="${item.product_variant_id || ''}">
                                <input type="hidden" name="items[${index}][product_name]" value="${item.product_name}">
                                <input type="hidden" name="items[${index}][product_variant_name]" value="${item.product_variant_name || ''}">
                            </td>
                            <td>
                                <input type="number" class="form-control" name="items[${index}][quantity]" value="${quantity.toFixed(2)}" readonly>
                            </td>
                            <td>
                                <input type="number" class="form-control" name="items[${index}][price]" value="${unitPrice.toFixed(2)}" readonly>
                            </td>
                            <td>
                                <input type="number" class="form-control" name="items[${index}][vat_rate]" value="${vatRate.toFixed(2)}" readonly>
                            </td>
                            <td>
                                <input type="number" class="form-control" name="items[${index}][vat_amount]" value="${vatAmount.toFixed(2)}" readonly>
                            </td>
                            <td>
                                <input type="number" class="form-control" name="items[${index}][total]" value="${total.toFixed(2)}" readonly>
                            </td>
                        </tr>`;
                        itemsTable.append(row);
                    });
                }

                // Faturadan gelen değerleri inputlara yaz
                const netAmount = parseFloat(selectedInvoice.data('net-amount')) || 0;
                const taxAmount = parseFloat(selectedInvoice.data('tax-amount')) || 0;
                const totalAmountFromInvoice = parseFloat(selectedInvoice.data('total-amount')) || 0;

                $('#net_amount').val(netAmount.toFixed(2));
                $('#tax_amount').val(taxAmount.toFixed(2));
                $('#total_amount').val(totalAmountFromInvoice.toFixed(2));

                $('#selected_invoice_no').text(selectedInvoice.text());
                $('#selected_invoice_info').show();
            }

            function toggleCancellationReason() {
                const selectedStatus = $('#status').val();
                const cancellationRow = $('#cancellation_reason_row');
                const reasonField = $('#reason_for_cancellation');
                
                if (selectedStatus == 3) { // İptal Edildi durumu
                    cancellationRow.show();
                    reasonField.prop('required', true);
                } else {
                    cancellationRow.hide();
                    reasonField.prop('required', false);
                    reasonField.val(''); // İptal sebebini temizle
                }
            }

            $('#invoice_id').on('change', updateInvoiceDetails);
            $('#status').on('change', toggleCancellationReason);

            if ($('#invoice_id').val()) {
                updateInvoiceDetails();
            }
            
            // Sayfa yüklendiğinde durum kontrolü yap
            toggleCancellationReason();
        });
    </script>
@endsection
