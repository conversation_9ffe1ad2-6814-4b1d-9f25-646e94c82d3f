<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentType extends BaseModel
{
    use SoftDeletes;

    protected $table = 'payment_types';
    
    protected $guarded = [];

    public function order()
    {
        return $this->hasMany(Order::class);
    }
    public function offers()
    {
        return $this->hasMany(Offer::class);
    }
    public function invoice()
    {
        return $this->hasMany(Invoice::class);
    }
}
