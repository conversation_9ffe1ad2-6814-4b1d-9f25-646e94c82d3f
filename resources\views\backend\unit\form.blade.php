@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? $item->name . ' Düzenle' : 'Yeni ' . $container->title . ' Ekle';
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                @csrf

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Birim Adı <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" value="{{ old('name', $item->name) }}"
                            required>
                        @error('name')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Birim Kodu <span class="text-danger">*</span></label>
                        <input type="text" name="code" class="form-control" value="{{ old('code', $item->code) }}"
                            required maxlength="20">
                        @error('code')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Sembol <span class="text-danger">*</span></label>
                        <input type="text" name="symbol" class="form-control" value="{{ old('symbol', $item->symbol) }}"
                            required maxlength="10">
                        @error('symbol')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Birim Tipi <span class="text-danger">*</span></label>
                        <select name="unit_type_id" class="form-select select2" required>
                            <option value="">Seçiniz</option>
                            @foreach ($unitTypes as $unitType)
                                <option value="{{ $unitType->id }}"
                                    {{ old('unit_type_id', $item->unit_type_id) == $unitType->id ? 'selected' : '' }}>
                                    {{ $unitType->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('unit_type_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Ondalık Değer Kabul Eder mi?</label>
                        <select name="allow_decimal" class="form-select">
                            <option value="1"
                                {{ old('allow_decimal', $item->allow_decimal ?? 1) == 1 ? 'selected' : '' }}>Evet</option>
                            <option value="0"
                                {{ old('allow_decimal', $item->allow_decimal ?? 1) == 0 ? 'selected' : '' }}>Hayır</option>
                        </select>
                        @error('allow_decimal')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6" id="decimal-places-container">
                        <label class="form-label">Ondalık Basamak Sayısı</label>
                        <input type="number" name="decimal_places" class="form-control"
                            value="{{ old('decimal_places', $item->decimal_places ?? 4) }}" min="0" max="10">
                        @error('decimal_places')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-12">
                        <label class="form-label">Açıklama</label>
                        <textarea name="description" class="form-control" rows="3">{{ old('description', $item->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-12">
                    <label class="form-label">Durum</label>
                    <select name="is_active" class="form-select">
                        <option value="1" {{ old('is_active', $item->is_active) == 1 ? 'selected' : '' }}>Aktif
                        </option>
                        <option value="0" {{ old('is_active', $item->is_active) == 0 ? 'selected' : '' }}>Pasif
                        </option>
                    </select>
                    @error('is_active')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                    <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">İptal</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const allowDecimalSelect = document.querySelector('select[name="allow_decimal"]');
            const decimalPlacesContainer = document.getElementById('decimal-places-container');

            function toggleDecimalPlaces() {
                if (allowDecimalSelect.value === '1') {
                    decimalPlacesContainer.style.display = 'block';
                } else {
                    decimalPlacesContainer.style.display = 'none';
                }
            }

            // İlk yükleme sırasında durumu kontrol et
            toggleDecimalPlaces();

            // Seçim değiştiğinde durumu güncelle
            allowDecimalSelect.addEventListener('change', toggleDecimalPlaces);
        });
    </script>
@endsection
