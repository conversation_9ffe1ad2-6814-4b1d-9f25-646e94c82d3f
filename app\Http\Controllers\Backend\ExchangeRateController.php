<?php

namespace App\Http\Controllers\Backend;

use App\Models\ExchangeRate;
use App\Services\ExchangeRateService;
use Illuminate\Http\Request;
use App\Models\Role;

class ExchangeRateController extends BaseController
{
    use BasePattern;

    protected $exchangeRateService;

    public function __construct(ExchangeRateService $exchangeRateService)
    {
        $this->title = 'Döviz Kurları';
        $this->page = 'exchange_rate';
        $this->model = new ExchangeRate();
        $this->exchangeRateService = $exchangeRateService;

        $this->view = (object)[
            'breadcrumb' => [
                'Ayarlar' => '#',
                'Döviz Kurları' => route('backend.exchange_rate_list'),
            ],
        ];

        // Veritabanında hiç kur yoksa ilk kez yükleme yapalım
        if (ExchangeRate::count() === 0) {
            $this->exchangeRateService->refreshRates();
        }
        view()->share('roles', Role::get());
        parent::__construct();
    }

    public function list(Request $request)
    {
        if ($request->ajax() && $request->has('toggle_status')) {
            try {
                $id = $request->input('id');
                $is_active = $request->input('is_active');

                $exchangeRate = ExchangeRate::findOrFail($id);
                $exchangeRate->is_active = $is_active;
                $exchangeRate->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Döviz kuru durumu başarıyla güncellendi.'
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Döviz kuru durumu güncellenirken hata oluştu'
                ]);
            }
        }

        if ($request->has('datatable')) {
            $date = $request->input('date', now()->format('Y-m-d'));

            $select = ExchangeRate::select()
                ->whereDate('rate_date', $date);

            $obj = datatables()->of($select);

            $obj = $obj
                ->editColumn('created_at', function ($item) {
                    return (!is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-');
                })
                ->editColumn('updated_at', function ($item) {
                    return (!is_null($item->updated_at) ? $item->updated_at->format('d.m.Y H:i') : '-');
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active == 1 ? 'Aktif' : 'Pasif';
                });

            $obj = $obj->addIndexColumn()->make(true);

            return $obj;
        }

        // Mevcut tüm tarihleri al (benzersiz)
        $availableDates = ExchangeRate::select('rate_date')
            ->distinct()
            ->orderBy('rate_date', 'desc')
            ->pluck('rate_date')
            ->map(function ($date) {
                return $date->format('Y-m-d');
            });

        return view("backend.$this->page.list", [
            'availableDates' => $availableDates
        ]);
    }

    public function refresh()
    {
        try {
            $this->exchangeRateService->refreshRates();
            return redirect()->route('backend.exchange_rate_list')->with('success', 'Döviz kurları başarıyla güncellendi.');
        } catch (\Exception $e) {
            return redirect()->route('backend.exchange_rate_list')->with('error', 'Döviz kurları güncellenirken bir hata oluştu');
        }
    }

    /**
     * Belirli bir tarihe ait TCMB döviz kurlarını XML'den çeker
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExternalRates(Request $request)
    {
        $date = $request->input('date', now()->format('Y-m-d'));

        try {
            $result = $this->exchangeRateService->getExternalRatesForDate($date);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'] ?? 'Döviz kurları çekilirken bir hata oluştu'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $result['data'],
                'date' => $result['date']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Döviz kurları çekilirken bir hata oluştu'
            ], 500);
        }
    }
}
