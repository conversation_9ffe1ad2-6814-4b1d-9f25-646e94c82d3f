<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockMovement extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_movements';

    protected $guarded = [];

    protected $casts = ['approval_date' => 'datetime', 'movement_date' => 'datetime'];

    public static function bootHook()
    {
        static::updating(function ($model) {
            $existingStatus = $model->getOriginal('status_id');
            if ($existingStatus == 2) {
                throw new \Exception("Onaylanmış stok hareketleri üzerinde işlem yapılamaz.");
            }
        });
    }

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function starter()
    {
        return $this->belongsTo(User::class, 'starter_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function location()
    {
        return $this->belongsTo(WarehouseLocation::class, 'location_id');
    }

    public function targetWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'target_warehouse_id');
    }

    public function targetLocation()
    {
        return $this->belongsTo(WarehouseLocation::class, 'target_location_id');
    }

    public function items()
    {
        return $this->hasMany(StockMovementItem::class, 'stock_movement_id');
    }


        public function getTotalPriceAttribute()
    {
        return $this->items()->sum('total_price');
    }

    public function stockMovementReason()
    {
        return $this->belongsTo(StockMovementReason::class, 'stock_movement_reason_id');
    }

    public function stockMovementType()
    {
        return $this->belongsTo(StockMovementType::class, 'stock_movement_type_id');
    }

    public static function generateMovementNo()
    {
        $datePrefix = Carbon::now()->format('Ymd');
        $lastMovement = self::withTrashed()->where('movement_no', 'like', "$datePrefix%")->latest('movement_no')->first();

        if ($lastMovement) {
            $lastSequence = (int) substr($lastMovement->movement_no, -4);
            $nextSequence = str_pad($lastSequence + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '0001';
        }

        return $datePrefix . $nextSequence;
    }
}
