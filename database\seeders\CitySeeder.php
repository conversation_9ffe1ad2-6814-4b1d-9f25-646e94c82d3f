<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Türkiye'nin ülke ID'sini al
        $turkeyId = DB::table('countries')->where('ubl_code', 'TR')->first()->id;

        $cities = [
            ['country_id' => $turkeyId, 'name' => 'Adana', 'plate_code' => 1, 'ubl_code' => '01', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Adıyaman', 'plate_code' => 2, 'ubl_code' => '02', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Afyonkarahisar', 'plate_code' => 3, 'ubl_code' => '03', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Ağrı', 'plate_code' => 4, 'ubl_code' => '04', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Amasya', 'plate_code' => 5, 'ubl_code' => '05', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Ankara', 'plate_code' => 6, 'ubl_code' => '06', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Antalya', 'plate_code' => 7, 'ubl_code' => '07', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Artvin', 'plate_code' => 8, 'ubl_code' => '08', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Aydın', 'plate_code' => 9, 'ubl_code' => '09', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Balıkesir', 'plate_code' => 10, 'ubl_code' => '10', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bilecik', 'plate_code' => 11, 'ubl_code' => '11', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bingöl', 'plate_code' => 12, 'ubl_code' => '12', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bitlis', 'plate_code' => 13, 'ubl_code' => '13', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bolu', 'plate_code' => 14, 'ubl_code' => '14', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Burdur', 'plate_code' => 15, 'ubl_code' => '15', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bursa', 'plate_code' => 16, 'ubl_code' => '16', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Çanakkale', 'plate_code' => 17, 'ubl_code' => '17', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Çankırı', 'plate_code' => 18, 'ubl_code' => '18', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Çorum', 'plate_code' => 19, 'ubl_code' => '19', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Denizli', 'plate_code' => 20, 'ubl_code' => '20', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Diyarbakır', 'plate_code' => 21, 'ubl_code' => '21', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Edirne', 'plate_code' => 22, 'ubl_code' => '22', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Elazığ', 'plate_code' => 23, 'ubl_code' => '23', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Erzincan', 'plate_code' => 24, 'ubl_code' => '24', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Erzurum', 'plate_code' => 25, 'ubl_code' => '25', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Eskişehir', 'plate_code' => 26, 'ubl_code' => '26', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Gaziantep', 'plate_code' => 27, 'ubl_code' => '27', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Giresun', 'plate_code' => 28, 'ubl_code' => '28', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Gümüşhane', 'plate_code' => 29, 'ubl_code' => '29', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Hakkari', 'plate_code' => 30, 'ubl_code' => '30', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Hatay', 'plate_code' => 31, 'ubl_code' => '31', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Isparta', 'plate_code' => 32, 'ubl_code' => '32', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Mersin', 'plate_code' => 33, 'ubl_code' => '33', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'İstanbul', 'plate_code' => 34, 'ubl_code' => '34', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'İzmir', 'plate_code' => 35, 'ubl_code' => '35', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kars', 'plate_code' => 36, 'ubl_code' => '36', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kastamonu', 'plate_code' => 37, 'ubl_code' => '37', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kayseri', 'plate_code' => 38, 'ubl_code' => '38', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kırklareli', 'plate_code' => 39, 'ubl_code' => '39', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kırşehir', 'plate_code' => 40, 'ubl_code' => '40', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kocaeli', 'plate_code' => 41, 'ubl_code' => '41', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Konya', 'plate_code' => 42, 'ubl_code' => '42', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kütahya', 'plate_code' => 43, 'ubl_code' => '43', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Malatya', 'plate_code' => 44, 'ubl_code' => '44', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Manisa', 'plate_code' => 45, 'ubl_code' => '45', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kahramanmaraş', 'plate_code' => 46, 'ubl_code' => '46', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Mardin', 'plate_code' => 47, 'ubl_code' => '47', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Muğla', 'plate_code' => 48, 'ubl_code' => '48', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Muş', 'plate_code' => 49, 'ubl_code' => '49', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Nevşehir', 'plate_code' => 50, 'ubl_code' => '50', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Niğde', 'plate_code' => 51, 'ubl_code' => '51', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Ordu', 'plate_code' => 52, 'ubl_code' => '52', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Rize', 'plate_code' => 53, 'ubl_code' => '53', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Sakarya', 'plate_code' => 54, 'ubl_code' => '54', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Samsun', 'plate_code' => 55, 'ubl_code' => '55', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Siirt', 'plate_code' => 56, 'ubl_code' => '56', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Sinop', 'plate_code' => 57, 'ubl_code' => '57', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Sivas', 'plate_code' => 58, 'ubl_code' => '58', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Tekirdağ', 'plate_code' => 59, 'ubl_code' => '59', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Tokat', 'plate_code' => 60, 'ubl_code' => '60', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Trabzon', 'plate_code' => 61, 'ubl_code' => '61', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Tunceli', 'plate_code' => 62, 'ubl_code' => '62', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Şanlıurfa', 'plate_code' => 63, 'ubl_code' => '63', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Uşak', 'plate_code' => 64, 'ubl_code' => '64', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Van', 'plate_code' => 65, 'ubl_code' => '65', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Yozgat', 'plate_code' => 66, 'ubl_code' => '66', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Zonguldak', 'plate_code' => 67, 'ubl_code' => '67', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Aksaray', 'plate_code' => 68, 'ubl_code' => '68', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bayburt', 'plate_code' => 69, 'ubl_code' => '69', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Karaman', 'plate_code' => 70, 'ubl_code' => '70', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kırıkkale', 'plate_code' => 71, 'ubl_code' => '71', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Batman', 'plate_code' => 72, 'ubl_code' => '72', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Şırnak', 'plate_code' => 73, 'ubl_code' => '73', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Bartın', 'plate_code' => 74, 'ubl_code' => '74', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Ardahan', 'plate_code' => 75, 'ubl_code' => '75', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Iğdır', 'plate_code' => 76, 'ubl_code' => '76', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Yalova', 'plate_code' => 77, 'ubl_code' => '77', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Karabük', 'plate_code' => 78, 'ubl_code' => '78', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Kilis', 'plate_code' => 79, 'ubl_code' => '79', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Osmaniye', 'plate_code' => 80, 'ubl_code' => '80', 'is_active' => 1],
            ['country_id' => $turkeyId, 'name' => 'Düzce', 'plate_code' => 81, 'ubl_code' => '81', 'is_active' => 1],
        ];

        foreach ($cities as &$city) {
            $city['created_at'] = now();
            $city['updated_at'] = now();
        }

        DB::table('cities')->insert($cities);
    }
}
