<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->string('movement_no', 50)->unique()->comment('Hareket numarası');
            $table->integer('invoice_id')->nullable()->comment('Fatura ID');
            $table->integer('current_id')->nullable()->comment('Cari hesap ID');
            $table->integer('starter_id')->nullable()->comment('Başlatan kullanıcı ID');
            $table->integer('approver_id')->nullable()->comment('Onaylayan kullanıcı ID');
            $table->dateTime('approval_date')->nullable()->comment('Onay tarihi');
            $table->dateTime('movement_date')->comment('Hareket tarihi');
            $table->integer('status_id')->default(1)->comment('Durum ID (1: Taslak, 2: Onaylandı, 3: İ<PERSON>l)');
            $table->integer('stock_movement_reason_id')->nullable()->comment('Hareket sebebi ID');
            $table->integer('stock_movement_type_id')->comment('Hareket tipi ID');
            $table->integer('warehouse_id')->nullable()->comment('Kaynak depo ID');
            $table->integer('location_id')->nullable()->comment('Kaynak lokasyon ID');
            $table->integer('target_warehouse_id')->nullable()->comment('Hedef depo ID');
            $table->integer('target_location_id')->nullable()->comment('Hedef lokasyon ID');
            $table->integer('related_document_type')->nullable()->comment('İlişkili belge tipi (1: Fatura, 2: İrsaliye, 3: Sipariş)');
            $table->decimal('total_amount', 15, 2)->default(0)->comment('Toplam tutar');
            $table->string('currency_code', 3)->nullable()->comment('Para birimi');
            $table->text('notes')->nullable()->comment('Notlar');
            $table->tinyInteger('is_active')->default(1)->comment('Aktif mi?');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
