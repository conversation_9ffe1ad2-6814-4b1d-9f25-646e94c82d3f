<?php

namespace Database\Seeders;

use App\Models\ProductType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $productTypes = [
                [
                    'name' => 'Ticari Mal',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Mamül',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Yarı Mamül',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Hammadde',
                    'is_active' => 1,
                ],
            ];

            foreach ($productTypes as $productType) {
                ProductType::updateOrCreate(
                    ['name' => $productType['name']],
                    $productType
                );
            }
        });

        $this->command->info('Ürün tipleri başarıyla oluşturuldu.');
    }
}
