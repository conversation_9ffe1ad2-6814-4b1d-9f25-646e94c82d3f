<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class ExportAccountVoucher
{
    protected $items; // Dikkat: çoğul!

    public function __construct($items)
    {
        // Burada artık birden fazla AccountVoucher nesnesi bekleniyor
        $this->items = $items;
    }

    public function export(): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Fiş Listesi');

        // Say<PERSON> ayarları (isteğe bağlı)
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(0);

        $sheet->getPageMargins()
            ->setTop(0.5)
            ->setRight(0.5)
            ->setBottom(0.5)
            ->setLeft(0.5)
            ->setHeader(0.2)
            ->setFooter(0.3);
        $sheet->getPageSetup()->setHorizontalCentered(true); // Yatayda ortala
        // Sütun genişliği ayarları
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Başlık stili
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Veri stili
        $valueStyle = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Başlıklar
        $headers = [
            'Fiş No',
            'Cari',
            'Fiş Tarihi',
            'Fiş Türü',
            'Tutar',
        ];

        // Başlıkları yaz
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue("{$col}1", $header);
            $sheet->getStyle("{$col}1")->applyFromArray($headerStyle);
            $col++;
        }

        // Satırları yaz
        $rowNum = 2;
        foreach ($this->items as $item) {
            $col = 'A';
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->voucher_no ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->current->title ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->voucher_date ? date('d.m.Y', strtotime($item->voucher_date)) : '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->voucher_type_name ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $sheet->setCellValueExplicit("{$col}{$rowNum}", number_format($item->amount ?? 0, 2, ',', '.'), DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $rowNum++;
        }

        return $spreadsheet;
    }
}
