<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;


class WaybillType extends BaseModel
{
    use SoftDeletes;

    protected $table = 'waybill_types';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }
    public function waybill()
    {
        return $this->hasMany(Waybill::class);
    }
}
