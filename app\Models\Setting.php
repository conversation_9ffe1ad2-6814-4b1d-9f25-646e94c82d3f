<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;

class Setting extends BaseModel
{
    use SoftDeletes;

    protected $table = 'settings';
    protected $guarded = [];

    protected $casts = [
        'options' => 'array'
    ];

    /**
     * <PERSON>yar değerini getir
     */
    public static function get($key, $default = null)
    {
        return Cache::remember("setting_{$key}", 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->where('is_active', 1)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Ayar değerini güncelle
     */
    public static function set($key, $value)
    {
        $setting = self::where('key', $key)->first();
        if ($setting) {
            $setting->update(['value' => $value]);
        }
        Cache::forget("setting_{$key}");
        return $setting;
    }

    /**
     * Para formatını ayara göre getir
     */
    public static function getDecimalPlaces()
    {
        return (int) self::get('decimal_places', 2);
    }


}
