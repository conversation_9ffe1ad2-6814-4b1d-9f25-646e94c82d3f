<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class WaybillTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                [
                    'name' => 'Alış İrsaliyesi',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Satış İrsaliyesi',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
            ];

            DB::table('waybill_types')->upsert(
                $types,
                ['name'],
                ['created_at', 'updated_at']
            );
        });

        $this->command->info('WaybillType verileri başarıyla oluşturuldu.');
    }
}
