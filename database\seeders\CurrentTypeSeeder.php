<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                [
                    'name' => '<PERSON><PERSON><PERSON><PERSON>i',
                    'is_active' => true,
                ],
                [
                    'name' => 'Tedarikçi',
                    'is_active' => true,
                ],
                [
                    'name' => 'Müşteri & Tedarikçi',
                    'is_active' => true,
                ],
            ];

            DB::table('current_types')->upsert(
                $types,
                ['name'],
                ['is_active']
            );
        });

        $this->command->info('CurrentType verileri başarıyla oluşturuldu.');
    }
}
