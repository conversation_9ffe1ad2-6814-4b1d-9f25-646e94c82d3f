<?php

namespace App\Http\Controllers\Backend;

use App\Models\VatRate;

class VatRateController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'KDV Oranları';
        $this->page = 'vat_rate';
        $this->model = new VatRate();
        $this->relation = [];

        $this->view = (object)array(
            'breadcrumb' => array(
                'KDV Oranları' => route('backend.vat_rate_list'),
            ),
        );
        $this->validation = array(
            [
                'rate' => 'required|numeric|min:0|max:100',
                'is_active' => 'required|boolean',
            ],
            [
                'rate.required' => 'KDV oranı zorunludur.',
                'rate.numeric' => 'KDV oranı sayısal olmalıdır.',
                'rate.min' => 'KDV oranı en az 0 olmalıdır.',
                'rate.max' => 'KDV oranı en fazla 100 olmalıdır.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
            ]);
        view()->share('vatRates', VatRate::active()->get());
        
        parent::__construct();
    }
}
