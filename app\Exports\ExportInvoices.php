<?php

namespace App\Exports;

use App\Models\ExchangeRate;
use App\Models\Product;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class ExportInvoices
{
    protected $items;
    protected $title;

    public function __construct($items, $title = 'Faturalar')
    {
        $this->items = $items;
        $this->title = $title;
    }

    public function export(): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($this->title);

        // Sayfa ayarları - A4 portrait
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(0);

        $sheet->getPageMargins()
            ->setTop(0.5)
            ->setRight(0.5)
            ->setBottom(0.5)
            ->setLeft(0.5)
            ->setHeader(0.2)
            ->setFooter(0.3);
        $sheet->getPageSetup()->setHorizontalCentered(true);

        // Sütun genişliği ayarları - 6 sütun için
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Başlık stili
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Veri stili
        $valueStyle = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Başlıklar - Satış faturası listesi sütunları
        $headers = [
            'ID',
            'Fatura No',
            'Cari Adı',
            'Fatura Tarihi',
            'Ödeme Durumu',
        ];

        // Başlıkları yaz
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue("{$col}1", $header);
            $sheet->getStyle("{$col}1")->applyFromArray($headerStyle);
            $col++;
        }

        // Satırları yaz
        $rowNum = 2;
        foreach ($this->items as $item) {
            $col = 'A';

            // ID
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->id ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Fatura No
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item->invoice_no ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Cari Adı
            $currentName = '';
            if (isset($item->current) && $item->current) {
                $currentName = $item->current->title ?? '';
            }
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $currentName, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Fatura Tarihi
            $dateFormatted = '';
            $invoiceDate = null;
            if (is_array($item) && isset($item['invoice_date'])) {
                $invoiceDate = $item['invoice_date'];
            } elseif (is_object($item) && isset($item->invoice_date)) {
                $invoiceDate = $item->invoice_date;
            }
            if ($invoiceDate) {
                try {
                    if (is_string($invoiceDate)) {
                        $dateFormatted = date('d.m.Y H:i', strtotime($invoiceDate));
                    } else {
                        $dateFormatted = $invoiceDate->format('d.m.Y H:i');
                    }
                } catch (\Exception $e) {
                    $dateFormatted = '';
                }
            }
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $dateFormatted, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Durum
            $status = '';
            if (isset($item->invoiceStatus) && $item->invoiceStatus) {
                $status = $item->invoiceStatus->name ?? '';
            }
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $status, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            $rowNum++;
        }

        return $spreadsheet;
    }
    public function exportToHtml($invoice): string
    {
        $current = $invoice->current;
        $address = '';
        if ($current->address) $address .= $current->address;
        if ($current->city?->name) $address .= ', ' . $current->city->name;
        if ($current->country?->name) $address .= ', ' . $current->country->name;
        $address = trim($address, ', ') ?: '-';

        // Döviz hesaplamaları
        $exchangeRateModel = null;
        $fxRate = 1;
        $fxSymbol = '₺';
        $currencyCode = 'TRY';
        if ($invoice->exchange_rate_id) {
            $exchangeRateModel = ExchangeRate::find($invoice->exchange_rate_id);
            if ($exchangeRateModel) {
                $fxRate = $exchangeRateModel->selling_rate;
                $fxSymbol = $exchangeRateModel->symbol;
                $currencyCode = $exchangeRateModel->code;
            }
        }
        // Dövizli tutarları hesapla
        $netAmountFX = $fxRate != 0 ? $invoice->net_amount / $fxRate : 0;
        $taxAmountFX = $fxRate != 0 ? $invoice->tax_amount / $fxRate : 0;
        $totalAmountFX = $fxRate != 0 ? $invoice->total_amount / $fxRate : 0;

        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <style>
                body { font-family: "DejaVu Sans", sans-serif; font-size: 12px; margin: 0; padding: 0; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #000; padding: 8px; text-align: left; vertical-align: top; }
                th { background-color: #4E4E4E; color: white; text-align: center; }
                .header { background-color: #F8F8F8; font-weight: bold; text-align: center; color: #333333; }
                .info-table td.label { text-align: right; background-color: #F8F8F8; font-weight: bold; color: #333333; width: 15%; }
                .info-table td { background-color: #FFFFFF; color: #333333; }
                .info-table td.address { word-wrap: break-word; word-break: break-word; max-width: 200px; line-height: 1.4; }
                .total-row { font-weight: bold; background-color: #F8F8F8; text-align: right; color: #333333; }
                .center { text-align: center; }
                .right { text-align: right; }
                .product-table { margin-top: 20px; }
            </style>
        </head>
        <body>
            <table>
                <tr>
                    <td colspan="4" class="header">FATURA BİLGİLERİ</td>
                    <td colspan="5" class="header">CARİ BİLGİLERİ</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Fatura No:</td>
                    <td colspan="3" style="word-break: break-all;">' . htmlspecialchars($invoice->invoice_no ?? '-') . '</td>
                    <td class="label">Cari Adı:</td>
                    <td colspan="4">' . htmlspecialchars(($current->title ?? '-') . ' ' . ($current->short_description ?? '')) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Fatura Tarihi:</td>
                    <td colspan="3">' . ($invoice->invoice_date ? $invoice->invoice_date->format('d.m.Y H:i') : '-') . '</td>
                    <td class="label">Telefon:</td>
                    <td colspan="4">' . htmlspecialchars($current->phone ?? '-') . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Vade Tarihi:</td>
                    <td colspan="3">' . ($invoice->due_date ? $invoice->due_date->format('d.m.Y H:i') : '-') . ($invoice->payment_term_id ? ' (' . $invoice->paymentTerm->day_count . ' Gün)' : '') . '</td>
                    <td class="label">Adres:</td>
                    <td colspan="4" class="address">' . htmlspecialchars($address) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Depo:</td>
                    <td colspan="3">' . htmlspecialchars($invoice->warehouse?->name ?? '-') . '</td>
                    <td class="label">Döviz Kuru:</td>
                    <td colspan="4">' . number_format((float)$fxRate, 4, ',',',') . ' (' . htmlspecialchars($fxSymbol) . ')</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Açıklama:</td>
                    <td colspan="8">' . htmlspecialchars($invoice->description ?? '-') . '</td>
                </tr>
            </table>
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                <tr>
                    <td colspan="8" style="background-color: #F8F8F8; font-weight: bold; text-align: center; color: #333333; padding: 8px; border: 1px solid #000;">ÜRÜN LİSTESİ</td>
                </tr>
                <tr>
                    <th style="width: 8%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Sıra No</th>
                    <th style="width: 12%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Ürün Kodu</th>
                    <th style="width: 30%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Ürün Adı</th>
                    <th style="width: 10%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Birim</th>
                    <th style="width: 10%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Miktar</th>
                    <th style="width: 15%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Birim Fiyat</th>
                    <th style="width: 10%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">KDV Oranı</th>
                    <th style="width: 15%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Toplam</th>
                </tr>';

        foreach ($this->items as $product) {
            $productCurrency = $product->currency_type ?? $product->currency_code ?? $product->currency_symbol ?? $fxSymbol;

            // Ürün kodu - varyant varsa varyant SKU, yoksa ürün SKU
            $productCode = '';
            if ($product->stock && $product->stock->variant) {
                $productCode = $product->stock->variant->sku;
            } elseif ($product->stock && $product->stock->product) {
                $productCode = $product->stock->product->sku;
            } else {
                $productCode = $product->product_code ?? '-';
            }

            // Ürün adı - varyant varsa product->name / variant->name
            $productName = '';
            if ($product->stock && $product->stock->product) {
                $productName = $product->stock->product->name;
                if ($product->stock->variant) {
                    $productName .= ' / ' . $product->stock->variant->name;
                }
            } else {
                $productName = $product->product_name ?? '-';
            }

            // Birim - product->unit->name
            $unit = '';
            if ($product->stock && $product->stock->product && $product->stock->product->unit) {
                $unit = $product->stock->product->unit->name;
            } else {
                $unit = $product->unit ?? '-';
            }

            $html .= '
                <tr>
                    <td style="width: 8%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($product->item_no ?? '-') . '</td>
                    <td style="width: 12%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($productCode) . '</td>
                    <td style="width: 30%; text-align: left; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($productName) . '</td>
                    <td style="width: 10%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($unit) . '</td>
                    <td style="width: 10%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' .($product->quantity ?? 0) . '</td>
                    <td style="width: 15%; text-align: right; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' .  number_format((float) ($product->unit_price ?? 0), 2, ',', '.') . ' ' . $productCurrency .  '</td>
                    <td style="width: 10%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">%' .  htmlspecialchars('' . ($product->vat_rate ?? 0) . ' (' . ($product->vat_status == 0 ? 'Hariç' : 'Dahil') . ')') . '</td>
                    <td style="width: 15%; text-align: right; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . number_format((float) ($product->total_price ?? 0), 2, ',', '.') . '</td>
                </tr>';
        }

        $html .= '
                <tr>
                    <td colspan="6" style="border: 1px solid #000; padding: 6px;"></td>
                    <td style="border: 1px solid #000; padding: 6px; background-color: #F8F8F8; font-weight: bold; text-align: right; font-size: 10px;">Ara Toplam:</td>
                    <td style="border: 1px solid #000; padding: 6px; text-align: right; font-weight: bold; font-size: 10px;">' . number_format((float) ($invoice->net_amount ?? 0), 2, ',', '.') . ' ₺' . ($currencyCode !== 'TRY' ? '<br><small style="color:#888;">' . number_format($netAmountFX, 2, ',', '.') . ' ' . htmlspecialchars($fxSymbol) . '</small>' : '') . '</td>
                </tr>
                <tr>
                    <td colspan="6" style="border: 1px solid #000; padding: 6px;"></td>
                    <td style="border: 1px solid #000; padding: 6px; background-color: #F8F8F8; font-weight: bold; text-align: right; font-size: 10px;">KDV:</td>
                    <td style="border: 1px solid #000; padding: 6px; text-align: right; font-weight: bold; font-size: 10px;">' . number_format((float) ($invoice->tax_amount ?? 0), 2, ',', '.') . ' ₺' . ($currencyCode !== 'TRY' ? '<br><small style="color:#888;">' . number_format($taxAmountFX, 2, ',', '.') . ' ' . htmlspecialchars($fxSymbol) . '</small>' : '') . '</td>
                </tr>
                <tr>
                    <td colspan="6" style="border: 1px solid #000; padding: 6px;"></td>
                    <td style="border: 1px solid #000; padding: 6px; background-color: #4E4E4E; color: white; font-weight: bold; text-align: right; font-size: 10px;">Genel Toplam:</td>
                    <td style="border: 1px solid #000; padding: 6px; text-align: right; font-weight: bold; background-color: #F8F8F8; font-size: 10px;">' . number_format((float) ($invoice->total_amount ?? 0), 2, ',', '.') . ' ₺' . ($currencyCode !== 'TRY' ? '<br><small style="color:#888;">' . number_format($totalAmountFX, 2, ',', '.') . ' ' . htmlspecialchars($fxSymbol) . '</small>' : '') . '</td>
                </tr>
            </table>
        </body>
        </html>';

        return $html;
    }
}
