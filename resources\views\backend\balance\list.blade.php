@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . $container->page . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Cari <PERSON>n<PERSON></th>
                            <th scope="col" class="text-center">
                                <PERSON>rç <PERSON>
                                <span class="info-icon" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="<PERSON><PERSON><PERSON>, carinin firmamıza olan borcunu gösterir. B<PERSON>, satış faturaları oluşturulduğunda artar ve cari hesap fişleri ile ödeme yapıldığında azalır.">
                                    <iconify-icon icon="mdi:information-outline" width="18"
                                        height="18"></iconify-icon>
                                </span>
                            </th>
                            <th scope="col" class="text-center">
                                Alacak Bakiyesi
                                <span class="info-icon" data-bs-toggle="tooltip" data-bs-placement="top"
                                    title="Alacak Bakiyesi, carinin firmamızdan alacağı miktarı gösterir. Bu değer, alış faturaları oluşturulduğunda artar.">
                                    <iconify-icon icon="mdi:information-outline" width="18"
                                        height="18"></iconify-icon>
                                </span>
                            </th>
                            <th scope="col" class="text-center">Toplam</th>
                            <th scope="col" class="text-center">Bakiye Durumu</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            // Tooltip'leri aktifleştir
            $('[data-bs-toggle="tooltip"]').tooltip();

            BaseCRUD.selector = "[datatable]";

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . ($container->page ?? 'balance') . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {}
                },
                columns: [
                    // Cari Ünvanı
                    {
                        data: 'current.title',
                        name: 'current.title',
                        className: 'text-center',
                        orderable: false,
                    },
                    {
                        data: 'debit_balance',
                        name: 'debit_balance',
                        className: 'text-center',
                        render: function(data, type, row) {
                            return data ? parseFloat(data).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }) + ' ₺' : '0.00 ₺';
                        },
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'credit_balance',
                        name: 'credit_balance',
                        className: 'text-center',
                        render: function(data, type, row) {
                            return data ? parseFloat(data).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }) + ' ₺' : '0.00 ₺';
                        },
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: null,
                        name: 'total',
                        className: 'text-center',
                        render: function(data, type, row) {
                            const debit = parseFloat(row.debit_balance || 0);
                            const credit = parseFloat(row.credit_balance || 0);
                            const total = debit - credit;

                            let formattedTotal = Math.abs(total).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }) + ' ₺';
                            return formattedTotal;
                        },
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: null,
                        name: 'balance_status',
                        className: 'text-center',
                        render: function(data, type, row) {
                            const debit = parseFloat(row.debit_balance || 0);
                            const credit = parseFloat(row.credit_balance || 0);
                            const total = debit - credit;

                            if (total > 0) {
                                return '<span class="badge" style="background-color: #FECACA; color: #7F1D1D;">Borç</span>';
                            } else if (total < 0) {
                                return '<span class="badge" style="background-color: #BBF7D0; color: #14532D;">Alacak</span>';
                            } else {
                                return '<span class="badge bg-secondary">Eşit</span>';
                            }
                        },
                        orderable: false,
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.' . $container->page . '_detail', ['unique' => '']) }}/${row.id}" class="bg-primary-light text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" title="Detay">
                                    <iconify-icon icon="iconamoon:eye-light" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    },
                ],
                order: [
                    [2, 'desc']
                ],
                pageLength: 15,
            });

            $('[filter-name]').change(function() {
                table.ajax.reload();
            });
            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

        });
    </script>
@endsection
