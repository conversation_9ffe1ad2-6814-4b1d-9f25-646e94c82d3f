<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Product extends BaseModel
{
    use SoftDeletes;
    protected $table = 'products';
    protected $guarded = [];
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class, 'brand_id');
    }

    public function unitType()
    {
        return $this->belongsTo(UnitType::class, );
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, );
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'product_id');
    }

    public function productTypes()
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    public function costHistories()
    {
        return $this->hasMany(ProductCostHistory::class, 'product_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovementItem::class, 'product_id');
    }

    public function getTotalStockQuantityAttribute()
    {
        return $this->stocks()->sum('quantity');
    }

    public function getTotalReservedQuantityAttribute()
    {
        return $this->stocks()
            ->join('stock_reservations', 'stocks.id', '=', 'stock_reservations.stock_id')
            ->where('stock_reservations.is_active', 1)
            ->sum('stock_reservations.quantity') ?? 0;
    }

    public function getAvailableStockQuantityAttribute()
    {
        return $this->total_stock_quantity - $this->total_reserved_quantity;
    }

    public static function generateSku($categoryId = null, $brandId = null, $productName = null)
    {
        if (!$categoryId || !$productName) {
            return null;
        }

        $category = \App\Models\Category::find($categoryId);
        $brand = $brandId ? \App\Models\Brand::find($brandId) : null;

        if (!$category) {
            return null;
        }

        $categorySlug = strSlugTr($category->name);
        $productSlug = strSlugTr($productName);

        $categoryCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $categorySlug), 0, 3));
        $brandCode = '';
        if ($brand) {
            $brandSlug = strSlugTr($brand->name);
            $brandCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $brandSlug), 0, 3));
        } else {
            $brandCode = 'NOB'; // No Brand
        }
        $productCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $productSlug), 0, 3));

        $prefix = $categoryCode . $brandCode . $productCode;

        $lastProduct = self::withTrashed()->where('sku', 'like', "$prefix%")->latest('sku')->first();

        if ($lastProduct) {
            $lastSequence = (int) substr($lastProduct->sku, -4);
            $nextSequence = str_pad($lastSequence + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '0001';
        }

        return $prefix . $nextSequence;
    }
}
