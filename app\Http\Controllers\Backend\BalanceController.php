<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Models\Balance;
use App\Models\Current;

class BalanceController extends BaseController
{
    use BasePattern;
    public function __construct()
    {
        $this->title = 'Bakiyeler';
        $this->page = 'balance';
        $this->model = new Balance();
        $this->relation = ['current'];
        $this->view = (object)[
            'breadcrumb' => [
                'Bakiyeler' => route('backend.balance_list'),
            ],
        ];
        $this->validation = array(
            [
                'current_id' => 'required|integer|exists:currents,id',
                'debit_balance' => 'required|numeric|min:0',
                'credit_balance' => 'required|numeric|min:0',
                'is_active' => 'required|in:0,1',
            ],
            [
                'current_id.required' => 'Lütfen bir cari seçiniz.',
                'current_id.exists' => 'Seçilen cari bulunamadı.',
                'debit_balance.required' => '<PERSON>rç bakiyesi alanı zorunludur.',
                'debit_balance.numeric' => 'Borç bakiyesi sayısal bir değer olmalıdır.',
                'debit_balance.min' => 'Borç bakiyesi negatif olamaz.',
                'credit_balance.required' => 'Alacak bakiyesi alanı zorunludur.',
                'credit_balance.numeric' => 'Alacak bakiyesi sayısal bir değer olmalıdır.',
                'credit_balance.min' => 'Alacak bakiyesi negatif olamaz.',
                'is_active.required' => 'Durum alanı zorunludur.',
                'is_active.in' => 'Durum değeri geçersiz.',
            ]);

        view()->share('currents', Current::get());
        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->addColumn('balance_status', function ($item) {
                $debit = (float)$item->debit_balance;
                $credit = (float)$item->credit_balance;
                $total = $debit - $credit;
                return $total;
            });
    }

    public function detail($unique)
    {
        $item = $this->model::with('current')->find((int)$unique);
        if (!$item) {
            return redirect()->route('backend.' . $this->page . '_list')->with('error', 'Cari bakiye kaydı bulunamadı');
        }
        // Döviz bakiyelerini çek
        $currencies = $item->balanceCurrencies()->get();
        return view("backend.$this->page.detail", compact('item', 'currencies'));
    }
}
