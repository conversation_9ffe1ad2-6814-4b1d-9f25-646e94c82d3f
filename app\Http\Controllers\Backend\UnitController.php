<?php

namespace App\Http\Controllers\Backend;

use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Http\Request;

class UnitController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Birimler';
        $this->page = 'unit';
        $this->model = new Unit();
        $this->relation = ['unitType'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Birimler' => route('backend.unit_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|min:2|max:255',
                'code' => 'required|string|min:2|max:20',
                'symbol' => 'required|string|min:2|max:10',
                'allow_decimal' => 'required|boolean',
                'decimal_places' => 'required|integer|min:0|max:4',
                'is_active' => 'required|boolean',
                'unit_type_id' => 'required|integer|exists:unit_types,id',
                'description' => 'nullable|string',
            ],
            [
                'name.required' => 'Birim adı zorunludur.',
                'name.min' => 'Birim adı en az 2 karakter olmalıdır.',
                'name.max' => 'Birim adı en fazla 255 karakter olabilir.',
                'code.required' => 'Birim kodu zorunludur.',
                'code.min' => 'Birim kodu en az 2 karakter olmalıdır.',
                'code.max' => 'Birim kodu en fazla 20 karakter olabilir.',
                'symbol.required' => 'Birim sembolü zorunludur.',
                'symbol.min' => 'Birim sembolü en az 2 karakter olmalıdır.',
                'symbol.max' => 'Birim sembolü en fazla 10 karakter olabilir.',
                'allow_decimal.required' => 'Ondalık değer kabul eder mi?',
                'allow_decimal.boolean' => 'Ondalık değer kabul eder mi? metin formatında olmalıdır.',
                'decimal_places.required' => 'Ondalık basamak sayısı zorunludur.',
                'decimal_places.integer' => 'Ondalık basamak sayısı sayı olmalıdır.',
                'decimal_places.min' => 'Ondalık basamak sayısı 0\'dan büyük olmalıdır.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
                'unit_type_id.required' => 'Birim tipi seçilmedi.',
                'unit_type_id.integer' => 'Birim tipi seçilmedi.',
                'unit_type_id.exists' => 'Birim tipi seçilmedi.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
            ]
        );

        view()->share('unitTypes', UnitType::active()->get());

        parent::__construct();
    }
}
