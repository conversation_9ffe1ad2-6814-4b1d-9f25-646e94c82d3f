<?php

namespace App\Http\Controllers\Backend;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductType;
use App\Models\ProductVariant;
use App\Models\ProductCostHistory;
use App\Models\Stock;
use App\Models\StockMovementItem;
use App\Models\StockReservation;
use App\Models\Unit;
use App\Models\UnitType;
use App\Libraries\Helpers;
use Illuminate\Http\Request;
use App\Models\ExchangeRate;
use Illuminate\Support\Facades\Cache;

class ProductController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ürünler';
        $this->page = 'product';
        $this->model = new Product();
        $this->relation = ['category', 'brand', 'unit', 'unitType', 'variants', 'stocks.stockReservations'];

        $this->view = (object)array(
            'breadcrumb' => array(
                '<PERSON><PERSON>ün <PERSON>' => '#',
                'Ürünler' => route('backend.product_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|max:255',
                'sku' => 'nullable|string|max:40',
                'barcode' => 'nullable|string|max:50',
                'description' => 'nullable|string|min:3|max:255',
                'weight' => 'nullable|numeric|min:0|max:999999.999',
                'volume' => 'nullable|numeric|min:0|max:999999.999',
                'width' => 'nullable|numeric|min:0|max:999999.999',
                'height' => 'nullable|numeric|min:0|max:999999.999',
                'length' => 'nullable|numeric|min:0|max:999999.999',
                'category_id' => 'required|integer|exists:categories,id',
                'brand_id' => 'required|integer|exists:brands,id',
                'product_type_id' => 'required|integer|exists:product_types,id',
                'unit_id' => 'required|integer|exists:units,id',
                'unit_type_id' => 'required|integer|exists:unit_types,id',
                'purchase_price' => 'nullable|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0',
                'purchase_currency_code' => 'nullable|string|max:3',
                'sale_currency_code' => 'nullable|string|max:3',
                'is_active' => 'required|boolean',
            ],
            [
                'name.required' => 'Ürün adı zorunludur.',
                'name.max' => 'Ürün adı en fazla 255 karakter olabilir.',
                'sku.max' => 'Ürün kodu en fazla 40 karakter olabilir.',
                'barcode.max' => 'Ürün barkodu en fazla 50 karakter olabilir.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
                'description.min' => 'Açıklama en az 3 karakter olmalıdır.',
                'description.max' => 'Açıklama en fazla 255 karakter olabilir.',
                'weight.numeric' => 'Ağırlık sayısal olmalıdır.',
                'weight.min' => 'Ağırlık en az 0 olmalıdır.',
                'weight.max' => 'Ağırlık en fazla 999999.999 olmalıdır.',
                'volume.numeric' => 'Hacim sayısal olmalıdır.',
                'volume.min' => 'Hacim en az 0 olmalıdır.',
                'volume.max' => 'Hacim en fazla 999999.999 olmalıdır.',
                'width.numeric' => 'Genişlik sayısal olmalıdır.',
                'width.min' => 'Genişlik en az 0 olmalıdır.',
                'width.max' => 'Genişlik en fazla 999999.999 olmalıdır.',
                'height.numeric' => 'Yükseklik sayısal olmalıdır.',
                'height.min' => 'Yükseklik en az 0 olmalıdır.',
                'height.max' => 'Yükseklik en fazla 999999.999 olmalıdır.',
                'length.numeric' => 'Uzunluk sayısal olmalıdır.',
                'length.min' => 'Uzunluk en az 0 olmalıdır.',
                'length.max' => 'Uzunluk en fazla 999999.999 olmalıdır.',
                'purchase_price.numeric' => 'Alış fiyatı sayısal olmalıdır.',
                'purchase_price.min' => 'Alış fiyatı en az 0 olmalıdır.',
                'sale_price.numeric' => 'Satış fiyatı sayısal olmalıdır.',
                'sale_price.min' => 'Satış fiyatı en az 0 olmalıdır.',
                'purchase_currency_code.string' => 'Alış para birimi metin formatında olmalıdır.',
                'purchase_currency_code.max' => 'Alış para birimi en fazla 3 karakter olmalıdır.',
                'sale_currency_code.string' => 'Satış para birimi metin formatında olmalıdır.',
                'sale_currency_code.max' => 'Satış para birimi en fazla 3 karakter olmalıdır.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
                'category_id.required' => 'Kategori seçilmedi.',
                'category_id.exists' => 'Kategori seçilmedi.',
                'brand_id.required' => 'Marka seçilmedi.',
                'brand_id.exists' => 'Seçilen marka geçersiz.',
                'product_type_id.required' => 'Ürün tipi seçilmedi.',
                'product_type_id.exists' => 'Seçilen ürün tipi geçersiz.',
                'unit_id.required' => 'Birim seçilmedi.',
                'unit_id.exists' => 'Birim seçilmedi.',
                'unit_type_id.required' => 'Birim tipi seçilmedi.',
                'unit_type_id.exists' => 'Birim tipi seçilmedi.',
            ]
        );

        view()->share('categories', Category::active()->doesntHave('children')->get());
        view()->share('brands', Brand::active()->get());
        view()->share('unitTypes', UnitType::active()->with('units')->get());
        view()->share('units', Unit::active()->get());
        view()->share('currencies', ExchangeRate::active()->select('code', 'name')->distinct('code')->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('productTypes', ProductType::active()->get());
        parent::__construct();
    }

    public function save(Request $request, $unique = null)
    {
        $validator = \Validator::make($request->all(), $this->validation[0], $this->validation[1]);

        if ($validator->fails()) {
            return $request->ajax()
                ? response()->json(['success' => false, 'errors' => $validator->errors()])
                : redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $params = $request->all();

            if (empty($params['sku'])) {
                $params['sku'] = Product::generateSku($params['category_id'], $params['brand_id'], $params['name']);
            }

            if (!empty($params['width']) && !empty($params['height']) && !empty($params['length'])) {
                $params['volume'] = ($params['width'] * $params['height'] * $params['length']) / 1000000; // m³ cinsinden
            }

            if (isset($params['sale_price']) && isset($params['purchase_price'])) {
                if ($params['sale_price'] < $params['purchase_price']) {
                    $params['notes'] = ($params['notes'] ?? '') . "\nUYARI: Satış fiyatı alış fiyatından düşük!";
                }
            }

            if (!is_null($unique)) {
                $product = Product::find($unique);
                if (!$product) {
                    return $request->ajax()
                        ? response()->json(['success' => false, 'message' => 'Güncellenecek ürün bulunamadı.'])
                        : redirect()->back()->with('error', 'Güncellenecek ürün bulunamadı.');
                }
                $product->update($params);
                $message = 'Ürün başarılı şekilde güncellendi';
            } else {
                $product = Product::create($params);
                $message = 'Ürün başarılı şekilde oluşturuldu';
            }

            Cache::flush();

            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => $message,
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', $message);

        } catch (\Exception $e) {
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Ürün kaydedilirken bir hata oluştu'
                ])
                : redirect()->back()->with('error', 'Ürün kaydedilirken bir hata oluştu')->withInput();
        }
    }
    private function calculateAvailableQuantity($stock, $excludeReservationId = null)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        if ($excludeReservationId) {
            $baseQuery->where('id', '!=', $excludeReservationId);
        }

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');

        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $stock->quantity - $salesReservations + $purchaseReservations;
    }

    private function calculateReservedQuantity($stock)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');
        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $salesReservations - $purchaseReservations;
    }

    private function getSalesReservations($stock)
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('reservation_type_id', 1)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');
    }

    private function getPurchaseReservations($stock)
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('reservation_type_id', 2)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');
    }

    private function calculateAvailableQuantityForProduct($product)
    {
        $totalAvailable = 0;
        foreach ($product->stocks as $stock) {
            $totalAvailable += $this->calculateAvailableQuantity($stock);
        }
        return $totalAvailable;
    }

    public function detail(Request $request, $unique = NULL)
    {
        $item = $this->model::with([
            'category',
            'brand',
            'unitType',
            'unit',
            'variants',
            'stocks.warehouse',
            'stocks.warehouseLocation',
            'stocks.variant',
            'stocks.stockReservations',
            'costHistories.current',
        ])->find($unique);

        if (is_null($item)) {
            return redirect()->back()->with('error', 'Ürün bulunamadı');
        }

        if ($request->has('datatable') && $request->has('type')) {
            switch ($request->type) {
                case 'variants':
                    $select = ProductVariant::where('product_id', $unique)
                        ->with(['stocks.stockReservations'])
                        ->where('product_variants.is_active', 1);

                    $obj = datatables()->of($select)
                        ->editColumn('sku', function ($item) {
                            return $item->sku ?? '-';
                        })
                        ->editColumn('name', function ($item) {
                            return $item->name ?? '-';
                        })
                        ->editColumn('barcode', function ($item) {
                            return $item->barcode ?? '-';
                        })
                        ->editColumn('purchase_price', function ($item) {
                            $currency = $item->purchase_currency_code ?? 'TRY';
                            $price = $item->purchase_price ?? 0;
                            return number_format($price, 2, ',', '.') . ' ' . $currency;
                        })
                        ->editColumn('sale_price', function ($item) {
                            $currency = $item->sale_currency_code ?? 'TRY';
                            $price = $item->sale_price ?? 0;
                            return number_format($price, 2, ',', '.') . ' ' . $currency;
                        })
                        ->addColumn('total_stock_quantity', function ($item) {
                            return $item->stocks->sum('quantity');
                        })
                                                ->addColumn('available_stock_quantity', function ($item) {
                            $totalAvailable = 0;
                            foreach ($item->stocks as $stock) {
                                $totalAvailable += $this->calculateAvailableQuantity($stock);
                            }
                            return $totalAvailable;
                        })
                        ->addColumn('is_active', function ($item) {
                            return $item->is_active == 1 || $item->is_active == true ? 'Aktif' : 'Pasif';
                        })
                        ->rawColumns([])
                        ->addIndexColumn()
                        ->make(true);

                    return $obj;

                case 'costhistory':
                    $select = ProductCostHistory::where('product_id', $unique)
                        ->with(['current'])
                        ->where('product_cost_histories.is_active', 1)
                        ->orderBy('valid_from', 'desc');

                    $obj = datatables()->of($select)
                        ->editColumn('current.title', function ($item) {
                            return $item->current->title ?? '-';
                        })
                        ->editColumn('cost', function ($item) {
                            return number_format($item->cost, 2, ',', '.');
                        })
                        ->editColumn('currency_code', function ($item) {
                            return $item->currency_code ?? 'TRY';
                        })
                        ->editColumn('valid_from', function ($item) {
                            return $item->valid_from ? \Carbon\Carbon::parse($item->valid_from)->format('d.m.Y') : '-';
                        })
                        ->editColumn('valid_to', function ($item) {
                            return $item->valid_to ? \Carbon\Carbon::parse($item->valid_to)->format('d.m.Y') : '-';
                        })
                        ->editColumn('notes', function ($item) {
                            return $item->notes ?? '-';
                        })
                        ->addIndexColumn()
                        ->make(true);

                    return $obj;

                case 'stocks':
                    $select = Stock::query()
                        ->select('stocks.*')
                        ->with(['warehouse', 'warehouseLocation', 'variant', 'stockBatch', 'stockReservations'])
                        ->where('stocks.product_id', $unique)
                        ->where('stocks.is_active', 1);

                    $obj = datatables()->of($select)
                        ->filterColumn('warehouse.name', function($query, $keyword) {
                            $query->whereHas('warehouse', function($q) use ($keyword) {
                                $q->where('name', 'like', "%{$keyword}%");
                            });
                        })
                        ->filterColumn('warehouse_location.name', function($query, $keyword) {
                            $query->whereHas('warehouseLocation', function($q) use ($keyword) {
                                $q->where('name', 'like', "%{$keyword}%");
                            });
                        })
                        ->filterColumn('variant.name', function($query, $keyword) {
                            $query->whereHas('variant', function($q) use ($keyword) {
                                $q->where('name', 'like', "%{$keyword}%");
                            });
                        })
                        ->editColumn('warehouse.name', function ($item) {
                            return $item->warehouse->name ?? '-';
                        })
                        ->editColumn('warehouse_location.name', function ($item) {
                            return $item->warehouseLocation->name ?? 'Ana Lokasyon';
                        })
                        ->editColumn('variant.name', function ($item) {
                            return $item->variant->name ?? '-';
                        })
                        ->editColumn('quantity', function ($item) {
                            $unit = $item->product ? $item->product->unit : null;
                            return formatQuantityWithUnit($item->quantity, $unit);
                        })
                        ->addColumn('reserved_quantity', function ($item) {
                            $reservedQuantity = $this->calculateReservedQuantity($item);
                            $productUnit = $item->product ? $item->product->unit : null;
                            $formatted = formatQuantityWithUnit(abs($reservedQuantity), $productUnit);

                            if ($reservedQuantity > 0) {
                                return '<span class="text-danger fw-bold">-' . $formatted . '</span>';
                            } elseif ($reservedQuantity < 0) {
                                return '<span class="text-success fw-bold">+' . $formatted . '</span>';
                            } else {
                                return '<span class="text-muted fw-bold">0</span>';
                            }
                        })
                        ->addColumn('available_quantity', function ($item) {
                            $availableQuantity = $this->calculateAvailableQuantity($item);
                            $itemUnit = $item->product ? $item->product->unit : null;
                            $formatted = formatQuantityWithUnit($availableQuantity, $itemUnit);

                            $textClass = 'text-success';
                            if ($availableQuantity <= 0) {
                                $textClass = 'text-danger';
                            } elseif ($availableQuantity < ($item->quantity * 0.2)) {
                                $textClass = 'text-warning';
                            }

                            return '<span class="' . $textClass . ' fw-bold">' . $formatted . '</span>';
                        })
                        ->addColumn('reserved_info', function ($item) {
                            $salesReservations = $this->getSalesReservations($item);
                            $purchaseReservations = $this->getPurchaseReservations($item);

                            $info = '';
                            if ($salesReservations > 0) {
                                $reservedUnit = $item->product ? $item->product->unit : null;
                                $formatted = formatQuantityWithUnit($salesReservations, $reservedUnit);
                                $info .= '<div class="text-danger fw-medium">Satış: -' . $formatted . '</div>';
                            }
                            if ($purchaseReservations > 0) {
                                $reservedUnit = $item->product ? $item->product->unit : null;
                                $formatted = formatQuantityWithUnit($purchaseReservations, $reservedUnit);
                                $info .= '<div class="text-success fw-medium">Alış: +' . $formatted . '</div>';
                            }

                            return $info ?: '<div class="text-muted fw-medium">Rezervasyon yok</div>';
                        })
                        ->rawColumns(['reserved_quantity', 'available_quantity', 'reserved_info'])
                        ->addIndexColumn()
                        ->make(true);

                    return $obj;

                case 'movements':
                    $select = StockMovementItem::where('product_id', $unique)
                        ->with(['stockMovement.stockMovementType', 'stockMovement.current', 'unit'])
                        ->whereHas('stockMovement', function($q) {
                            $q->whereNull('deleted_at');
                        })
                        ->orderBy('stock_movement_items.created_at', 'desc');

                    $obj = datatables()->of($select)
                        ->editColumn('stockMovement.movement_date', function ($item) {
                            return !is_null($item->stockMovement->movement_date) ?
                                \Carbon\Carbon::parse($item->stockMovement->movement_date)->format('d.m.Y H:i') : '-';
                        })
                        ->editColumn('stockMovement.stockMovementType.name', function ($item) {
                            return $item->stockMovement->stockMovementType->name ?? '-';
                        })
                        ->editColumn('stockMovement.current.title', function ($item) {
                            return $item->stockMovement->current->title ?? '-';
                        })
                        ->editColumn('quantity', function ($item) {
                            $sign = in_array($item->stockMovement->stock_movement_type_id, [1, 3]) ? '+' : '-';
                            $class = $sign == '+' ? 'text-success-600' : 'text-danger-600';
                            $movementUnit = $item->unit ? $item->unit : null;
                            $formatted = formatQuantity($item->quantity, $movementUnit);
                            return '<span class="fw-medium ' . $class . '">' . $sign . $formatted . '</span>';
                        })
                        ->editColumn('unit.symbol', function ($item) {
                            return $item->unit->symbol ?? '-';
                        })
                        ->editColumn('unit_price', function ($item) {
                            $currency = $item->currency_code ?? 'TRY';
                            return number_format($item->unit_price, 2, ',', '.') . ' ' . $currency;
                        })
                        ->editColumn('total_price', function ($item) {
                            $currency = $item->currency_code ?? 'TRY';
                            return number_format($item->total_price, 2, ',', '.') . ' ' . $currency;
                        })
                        ->rawColumns(['quantity'])
                        ->addIndexColumn()
                        ->make(true);

                    return $obj;
            }
        }

        return view("backend.$this->page.detail", compact('item', 'unique'));
    }
}
