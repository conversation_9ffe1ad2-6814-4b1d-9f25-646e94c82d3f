<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Routing\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Route::macro("desc", function ($value) {
            /**
             * @var Route $this
             */
            $this;

            $o = $this->getAction("desc");

            if ($o == null) {
                $this->action["desc"] = [$value];
            } elseif (gettype($o) == "string") {
                $this->action["desc"] = array_merge([$o], is_array($value) ? $value : [$value]);
            } elseif (is_array($o)) {
                $this->action["desc"] = array_merge($o, is_array($value) ? $value : [$value]);
            }

            return $this;
        });

        Route::macro("getDesc", function () {
            $o = $this->getAction("desc");
            $o = is_array($o) ? $o : [$o];
            return collect($o);
        });
    }
}
