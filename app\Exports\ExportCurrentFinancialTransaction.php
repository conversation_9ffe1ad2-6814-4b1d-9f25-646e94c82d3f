<?php

namespace App\Exports;

use App\Models\ExchangeRate;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class ExportCurrentFinancialTransaction
{
    protected $items;

    public function __construct($items)
    {
        $this->items = $items;
    }

    public function export(): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Cari Finansal Hareketler');

        // Sayfa ayarları (isteğe bağlı)
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(0);

        $sheet->getPageMargins()
            ->setTop(0.5)
            ->setRight(0.5)
            ->setBottom(0.5)
            ->setLeft(0.5)
            ->setHeader(0.2)
            ->setFooter(0.3);
        $sheet->getPageSetup()->setHorizontalCentered(true); // Yatayda ortala
        // Sütun genişliği ayarları
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Başlık stili
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Veri stili
        $valueStyle = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Başlıklar
        $headers = [
            'İşlem No',
            'Cari',
            'Tarih',
            'İşlem Tipi',
            'Tutar',
        ];

        // Başlıkları yaz
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue("{$col}1", $header);
            $sheet->getStyle("{$col}1")->applyFromArray($headerStyle);
            $col++;
        }

        // Satırları yaz
        $rowNum = 2;
        foreach ($this->items as $item) {
            $col = 'A';

            // İşlem No
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['invoice_no'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Cari
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['current_name'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Tarih
            $dateFormatted = '';
            if (!empty($item['invoice_date'])) {
                try {
                    $dateFormatted = date('d.m.Y', strtotime($item['invoice_date']));
                } catch (\Exception $e) {
                    $dateFormatted = '';
                }
            }
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $dateFormatted, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // İşlem Tipi
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['transaction_type_name'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Tutar - Dinamik para birimi sembolü
            $amount = $item['total_amount'] ?? 0;
            $currencySymbol = $item['currency_code'] === 'TRY' ? '₺' : ($item['currency_symbol'] ?? '₺');
            $formattedAmount = number_format($amount, 2, ',', '.') . ' ' . $currencySymbol;

            $sheet->setCellValueExplicit("{$col}{$rowNum}", $formattedAmount, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;


            $rowNum++;
        }

        return $spreadsheet;
    }
}
