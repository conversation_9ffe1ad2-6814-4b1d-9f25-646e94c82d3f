<?php

namespace Database\Seeders;

use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createUnits();
        });

        $this->command->info('Unit verileri başarıyla oluşturuldu.');
    }

    /**
     * Birimleri oluştur
     */
    private function createUnits(): void
    {
        $unitTypes = UnitType::all()->keyBy('name');

        $units = [
            // Ağırlık birimleri
            ['name' => 'Kilogram', 'code' => 'KG', 'symbol' => 'kg', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 3],
            ['name' => 'Gram', 'code' => 'G', 'symbol' => 'g', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Ton', 'code' => 'TON', 'symbol' => 't', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 3],
            ['name' => 'Miligram', 'code' => 'MG', 'symbol' => 'mg', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Pound', 'code' => 'LB', 'symbol' => 'lb', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Ounce', 'code' => 'OZ', 'symbol' => 'oz', 'type' => 'Ağırlık', 'allow_decimal' => 1, 'decimal_places' => 2],

            // Uzunluk birimleri
            ['name' => 'Metre', 'code' => 'M', 'symbol' => 'm', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Santimetre', 'code' => 'CM', 'symbol' => 'cm', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Milimetre', 'code' => 'MM', 'symbol' => 'mm', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Kilometre', 'code' => 'KM', 'symbol' => 'km', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 3],
            ['name' => 'İnç', 'code' => 'IN', 'symbol' => 'in', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Fit', 'code' => 'FT', 'symbol' => 'ft', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Yard', 'code' => 'YD', 'symbol' => 'yd', 'type' => 'Uzunluk', 'allow_decimal' => 1, 'decimal_places' => 2],

            // Sıvı birimleri
            ['name' => 'Litre', 'code' => 'L', 'symbol' => 'L', 'type' => 'Sıvı', 'allow_decimal' => 1, 'decimal_places' => 3],
            ['name' => 'Mililitre', 'code' => 'ML', 'symbol' => 'ml', 'type' => 'Sıvı', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Santilitre', 'code' => 'CL', 'symbol' => 'cl', 'type' => 'Sıvı', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Desilitre', 'code' => 'DL', 'symbol' => 'dl', 'type' => 'Sıvı', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Galon (US)', 'code' => 'GAL', 'symbol' => 'gal', 'type' => 'Sıvı', 'allow_decimal' => 1, 'decimal_places' => 2],

            // Hacim birimleri
            ['name' => 'Metreküp', 'code' => 'M3', 'symbol' => 'm³', 'type' => 'Hacim', 'allow_decimal' => 1, 'decimal_places' => 4],
            ['name' => 'Santimetreküp', 'code' => 'CM3', 'symbol' => 'cm³', 'type' => 'Hacim', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Desimetreküp', 'code' => 'DM3', 'symbol' => 'dm³', 'type' => 'Hacim', 'allow_decimal' => 1, 'decimal_places' => 3],
            ['name' => 'Milimetreküp', 'code' => 'MM3', 'symbol' => 'mm³', 'type' => 'Hacim', 'allow_decimal' => 1, 'decimal_places' => 1],

            // Alan birimleri
            ['name' => 'Metrekare', 'code' => 'M2', 'symbol' => 'm²', 'type' => 'Alan', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Santimetrekare', 'code' => 'CM2', 'symbol' => 'cm²', 'type' => 'Alan', 'allow_decimal' => 1, 'decimal_places' => 1],
            ['name' => 'Hektar', 'code' => 'HA', 'symbol' => 'ha', 'type' => 'Alan', 'allow_decimal' => 1, 'decimal_places' => 2],
            ['name' => 'Dönüm', 'code' => 'DONUM', 'symbol' => 'dönüm', 'type' => 'Alan', 'allow_decimal' => 1, 'decimal_places' => 2],

            // Adet birimleri
            ['name' => 'Adet', 'code' => 'AD', 'symbol' => 'ad', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Düzine', 'code' => 'DZ', 'symbol' => 'dz', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Paket', 'code' => 'PKT', 'symbol' => 'pkt', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Koli', 'code' => 'KOLI', 'symbol' => 'koli', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Çift', 'code' => 'CIFT', 'symbol' => 'çift', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Takım', 'code' => 'TAKIM', 'symbol' => 'takım', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
            ['name' => 'Gross', 'code' => 'GROSS', 'symbol' => 'gross', 'type' => 'Adet', 'allow_decimal' => 0, 'decimal_places' => 0],
        ];

        foreach ($units as $unitData) {
            $unitType = $unitTypes->get($unitData['type']);

            if ($unitType) {
                Unit::updateOrCreate(
                    [
                        'name' => $unitData['name'],
                        'unit_type_id' => $unitType->id,
                    ],
                    [
                        'code' => $unitData['code'],
                        'symbol' => $unitData['symbol'],
                        'description' => $unitData['type'] . ' birimi',
                        'unit_type_id' => $unitType->id,
                        'allow_decimal' => $unitData['allow_decimal'],
                        'decimal_places' => $unitData['decimal_places'],
                        'is_active' => 1,
                        'created_by' => 1,
                        'updated_by' => 1,
                    ]
                );
            }
        }
    }
}
