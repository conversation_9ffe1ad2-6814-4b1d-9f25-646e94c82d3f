@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Döviz Kurları Veri Kaynağı</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="data-source" class="form-label">Veri Kaynağı</label>
                        <select id="data-source" class="form-select form-select-sm">
                            <option value="database">Veritabanı Kayıtları</option>
                            <option value="tcmb">TCMB XML (Anlık Çekilen)</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-4" id="database-date-container">
                    <div class="form-group">
                        <label for="date-filter" class="form-label">Veritabanı Kaydı Tarihi</label>
                        <select id="date-filter" class="form-select form-select-sm">
                            @foreach ($availableDates as $date)
                                <option value="{{ $date }}" {{ $date == now()->format('Y-m-d') ? 'selected' : '' }}>
                                    {{ \Carbon\Carbon::parse($date)->format('d.m.Y') }}
                                </option>
                            @endforeach
                        </select>
                        <small class="form-text text-muted">Veritabanına kaydedilmiş döviz kurları.</small>
                    </div>
                </div>

                <div class="col-md-4" id="tcmb-date-container" style="display: none;">
                    <div class="form-group">
                        <label for="tcmb-date" class="form-label">TCMB Tarih Seçimi</label>
                        <input type="text" id="tcmb-date" class="form-control form-control-sm"
                            value="{{ now()->format('Y-m-d') }}" placeholder="Tarih seçin">
                        <small class="form-text text-muted">TCMB'den doğrudan XML çekilir, veritabanına kaydedilmez. Hafta
                            sonları veri bulunmaz.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div>
                <span id="loading-indicator" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                        <span class="visually-hidden">Yükleniyor...</span>
                    </div>
                    Veriler çekiliyor...
                </span>
                <a href="{{ route('backend.exchange_rate_refresh') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                    <iconify-icon icon="mdi:refresh" class="me-1"></iconify-icon> Kurları Güncelle
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <div id="error-message" class="alert alert-danger" style="display: none;"></div>
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Döviz Kodu</th>
                            <th scope="col">Sembol</th>
                            <th scope="col">Döviz Adı</th>
                            <th scope="col" class="text-center">Alış</th>
                            <th scope="col" class="text-center">Satış</th>
                            <th scope="col" class="text-center">Son Güncelleme</th>
                            <th scope="col" class="text-center">Durum</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var dataTable;
            var dataSource = 'database';
            var flatpickrInstance;

            initializeFlatpickr();

            function initializeFlatpickr() {
                if (typeof flatpickr !== 'function') {
                    var link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css';
                    document.head.appendChild(link);

                    var script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/flatpickr';
                    script.onload = function() {
                        var trScript = document.createElement('script');
                        trScript.src = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/tr.js';
                        trScript.onload = function() {
                            initFlatpickrInstance();
                        };
                        document.head.appendChild(trScript);
                    };
                    document.head.appendChild(script);
                } else {
                    if (typeof flatpickr.l10ns.tr === 'undefined') {
                        var trScript = document.createElement('script');
                        trScript.src = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/tr.js';
                        trScript.onload = function() {
                            initFlatpickrInstance();
                        };
                        document.head.appendChild(trScript);
                    } else {
                        initFlatpickrInstance();
                    }
                }
            }

            function initFlatpickrInstance() {
                flatpickrInstance = flatpickr("#tcmb-date", {
                    dateFormat: "d.m.Y",
                    maxDate: "today",
                    locale: "tr",
                    disable: [
                        function(date) {
                            return date.getDay() === 0 || date.getDay() === 6;
                        }
                    ],
                    onChange: function(selectedDates, dateStr) {
                        if (dataSource === 'tcmb') {
                            refreshTcmbData();
                        }
                    }
                });
            }

            var dtConfig = {
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        d.date = $('#date-filter').val();
                        return d;
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'code',
                        name: 'code',
                        className: 'text-center',
                    },
                    {
                        data: 'symbol',
                        name: 'symbol',
                        className: 'text-center',
                    },
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return parseFloat(row.buying_rate).toLocaleString('tr-TR', {
                                minimumFractionDigits: 4,
                                maximumFractionDigits: 4
                            });
                        },
                        data: 'buying_rate',
                        name: 'buying_rate',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return parseFloat(row.selling_rate).toLocaleString('tr-TR', {
                                minimumFractionDigits: 4,
                                maximumFractionDigits: 4
                            });
                        },
                        data: 'selling_rate',
                        name: 'selling_rate',
                        className: 'text-center',
                    },
                    {
                        data: 'updated_at',
                        name: 'updated_at',
                        className: 'text-center',
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center',
                        render: function(data, type, row) {
                            var checked = data == 'Aktif' ? 'checked' : '';
                            return '<div class="form-switch switch-primary d-flex align-items-center justify-content-center">' +
                                '<input class="form-check-input toggle-status" type="checkbox" role="switch" ' +
                                checked + ' data-id="' + row.id + '">' +
                                '</div>';
                        }
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            };
            $(document).on('change', '.toggle-status', function() {
                var id = $(this).data('id');
                var is_active = $(this).is(':checked') ? 1 : 0;

                $.ajax({
                    url: "{{ route('backend.' . $container->page . '_list') }}",
                    type: 'POST',
                    data: {
                        id: id,
                        is_active: is_active,
                        toggle_status: true
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Durum başarıyla güncellendi');
                        } else {
                            alert('Durum güncellenirken bir hata oluştu');
                            table.ajax.reload();
                        }
                    },
                    error: function() {
                        alert('Durum güncellenirken bir hata oluştu');
                        table.ajax.reload();
                    }
                });
            });

            BaseCRUD.selector = "[datatable]";
            dataTable = BaseCRUD.ajaxtable(dtConfig);

            $('#data-source').change(function() {
                dataSource = $(this).val();

                if (dataSource === 'database') {
                    $('#database-date-container').show();
                    $('#tcmb-date-container').hide();
                    refreshDatabaseData();
                } else {
                    $('#database-date-container').hide();
                    $('#tcmb-date-container').show();

                    var today = new Date();
                    if (today.getDay() === 0 || today.getDay() === 6) {
                        var fridayOffset = today.getDay() === 0 ? 2 :
                        1;
                        today.setDate(today.getDate() - fridayOffset);

                        if (flatpickrInstance) {
                            flatpickrInstance.setDate(today);
                        }
                    }

                    refreshTcmbData();
                }
            });

            $('#date-filter').change(function() {
                refreshDatabaseData();
            });

            function refreshDatabaseData() {
                if (dataTable) {
                    dataTable.destroy();
                }

                BaseCRUD.selector = "[datatable]";
                dataTable = BaseCRUD.ajaxtable(dtConfig);
            }

            function refreshTcmbData() {
                $('#loading-indicator').show();
                $('#error-message').hide();

                var selectedDate;
                if (flatpickrInstance && flatpickrInstance.selectedDates.length > 0) {
                    var rawDate = flatpickrInstance.selectedDates[0];
                    selectedDate = rawDate.getFullYear() + '-' +
                        String(rawDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(rawDate.getDate()).padStart(2, '0');
                } else {
                    selectedDate = $('#tcmb-date').val();

                    if (selectedDate.includes('.')) {
                        var parts = selectedDate.split('.');
                        if (parts.length === 3) {
                            selectedDate = parts[2] + '-' + parts[1] + '-' + parts[0];
                        }
                    }
                }

                var selectedDateObj = new Date(selectedDate);
                var dayOfWeek = selectedDateObj.getDay();

                if (dayOfWeek === 0 || dayOfWeek === 6) {
                    var fridayOffset = dayOfWeek === 0 ? 2 : 1;
                    selectedDateObj.setDate(selectedDateObj.getDate() - fridayOffset);
                    selectedDate = selectedDateObj.getFullYear() + '-' +
                        String(selectedDateObj.getMonth() + 1).padStart(2, '0') + '-' +
                        String(selectedDateObj.getDate()).padStart(2, '0');

                    $('#error-message').html('TCMB hafta sonları döviz kuru yayınlamaz. En son iş günü seçildi.')
                        .show();

                    if (flatpickrInstance) {
                        flatpickrInstance.setDate(selectedDate);
                    }
                }

                $.ajax({
                    url: "{{ route('backend.exchange_rate_external') }}",
                    type: 'POST',
                    data: {
                        date: selectedDate
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#error-message').hide();

                            if (dataTable) {
                                dataTable.destroy();
                            }

                            dataTable = $('#dataTable').DataTable({
                                data: response.data,
                                columns: [{
                                        data: 'code',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'symbol',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'name',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'buying_rate',
                                        className: 'text-center',
                                        render: function(data) {
                                            return parseFloat(data).toLocaleString(
                                                'tr-TR', {
                                                    minimumFractionDigits: 4,
                                                    maximumFractionDigits: 4
                                                });
                                        }
                                    },
                                    {
                                        data: 'selling_rate',
                                        className: 'text-center',
                                        render: function(data) {
                                            return parseFloat(data).toLocaleString(
                                                'tr-TR', {
                                                    minimumFractionDigits: 4,
                                                    maximumFractionDigits: 4
                                                });
                                        }
                                    },
                                    {
                                        data: 'rate_date',
                                        className: 'text-center',
                                        render: function(data) {
                                            return data + ' (TCMB XML)';
                                        }
                                    },
                                    {
                                        data: null,
                                        className: 'text-center',
                                        render: function(data, type, row) {
                                            return '<span class="badge bg-info">TCMB XML</span>';
                                        }
                                    }
                                ],
                                pageLength: 15,
                                order: [
                                    [0, 'desc']
                                ],
                            });
                        } else {
                            $('#error-message').html(response.message ||
                                'Veriler yüklenemedi. Lütfen başka bir tarih seçiniz.').show();
                            if (dataTable) {
                                dataTable.destroy();
                            }

                            dataTable = $('#dataTable').DataTable({
                                data: [],
                                columns: [{
                                        data: 'code',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'symbol',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'name',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'buying_rate',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'selling_rate',
                                        className: 'text-center',
                                    },
                                    {
                                        data: 'rate_date',
                                        className: 'text-center',
                                    },
                                    {
                                        data: null,
                                        className: 'text-center',
                                        render: function() {
                                            return '';
                                        }
                                    }
                                ],
                                pageLength: 15,
                                language: {
                                    emptyTable: "Seçilen tarih için veri bulunamadı."
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        var errorMessage =
                            'Seçilen tarih için döviz kurları yüklenemedi. Lütfen daha yeni bir tarih seçiniz.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            console.error('TCMB API hatası:', xhr.responseJSON.message);
                        }

                        $('#error-message').html(errorMessage).show();

                        if (dataTable) {
                            dataTable.destroy();
                        }

                        dataTable = $('#dataTable').DataTable({
                            data: [],
                            columns: [{
                                    data: 'code',
                                    className: 'text-center',
                                },
                                {
                                    data: 'symbol',
                                    className: 'text-center',
                                },
                                {
                                    data: 'name',
                                    className: 'text-center',
                                },
                                {
                                    data: 'buying_rate',
                                    className: 'text-center',
                                },
                                {
                                    data: 'selling_rate',
                                    className: 'text-center',
                                },
                                {
                                    data: 'rate_date',
                                    className: 'text-center',
                                },
                                {
                                    data: null,
                                    className: 'text-center',
                                    render: function() {
                                        return '';
                                    }
                                }
                            ],
                            pageLength: 15,
                            language: {
                                emptyTable: "Seçilen tarih için veri bulunamadı."
                            }
                        });
                    },
                    complete: function() {
                        $('#loading-indicator').hide();
                    }
                });
            }
        });
    </script>
@endsection
