<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $users = [
                [
                    'name' => 'Talha',
                    'surname' => 'Koyun',
                    'tc' => '11111111111',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 111 11 11',
                    'email' => '<EMAIL>',
                    'username' => 'mtk',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Bur<PERSON>',
                    'surname' => '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                    'tc' => '11111111112',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 222 22 22',
                    'email' => 'burcu.seyrek<PERSON><PERSON>@kodlio.com.tr',
                    'username' => 'bsb',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'İbrahim Ethem',
                    'surname' => 'Özdemir',
                    'tc' => '11111111113',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 333 33 33',
                    'email' => '<EMAIL>',
                    'username' => 'eoz',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Muhammed Ali',
                    'surname' => 'Kartal',
                    'tc' => '11111111114',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 444 44 44',
                    'email' => '<EMAIL>',
                    'username' => 'akt',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Salih',
                    'surname' => 'Tunç',
                    'tc' => '11111111115',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 555 55 55',
                    'email' => '<EMAIL>',
                    'username' => 'stn',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Muhammet',
                    'surname' => 'Altınsoy',
                    'tc' => '11111111116',
                    'title' => 'Süper Admin',
                    'role_id' => 1,
                    'phone' => '+90 (555) 666 66 66',
                    'email' => '<EMAIL>',
                    'username' => 'mas',
                    'password' => Hash::make('123123'),
                    'is_active' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
            ];

            DB::table('users')->upsert(
                $users,
                ['email'],
                ['name', 'surname', 'tc', 'title', 'role_id', 'phone', 'username', 'password', 'is_active', 'updated_at']
            );
        });

        $this->command->info('User verileri başarıyla oluşturuldu.');
    }
}
