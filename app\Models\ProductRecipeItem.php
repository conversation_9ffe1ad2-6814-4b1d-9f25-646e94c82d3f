<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ProductRecipeItem extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_recipe_items';

    protected $guarded = [];

    public function products()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function productRecipes()
    {
        return $this->belongsTo(ProductRecipe::class, 'product_recipe_id');
    }
}
