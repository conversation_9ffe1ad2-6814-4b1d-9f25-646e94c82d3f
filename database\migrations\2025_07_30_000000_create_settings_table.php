<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique()->comment('Ayar anahtarı');
            $table->text('value')->nullable()->comment('Ayar değeri');
            $table->string('name', 255)->comment('Ayar adı');
            $table->text('description')->nullable()->comment('Ayar açıklaması');
            $table->string('type', 50)->default('text')->comment('Ayar tipi (text, number, select, etc.)');
            $table->text('options')->nullable()->comment('Seçenekler (JSON format)');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Para gösterimi ayarını ekle
        DB::table('settings')->insert([
            'key' => 'decimal_places',
            'value' => '2',
            'name' => 'Para Gösterimi - Ondalık Basamak Sayısı',
            'description' => 'Paranın ondalık kısmında kaç basamak gösterileceğini belirler (2-8 arası)',
            'type' => 'select',
            'options' => json_encode([
                '2' => '2 Basamak',
                '3' => '3 Basamak',
                '4' => '4 Basamak',
                '5' => '5 Basamak',
                '6' => '6 Basamak',
                '7' => '7 Basamak',
                '8' => '8 Basamak'
            ]),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
