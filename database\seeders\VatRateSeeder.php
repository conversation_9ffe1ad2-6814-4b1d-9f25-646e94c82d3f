<?php

namespace Database\Seeders;

use App\Models\VatRate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class VatRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $vatRates = [
                [
                    'rate' => '0',
                    'is_active' => 1,
                ],
                [
                    'rate' => '1',
                    'is_active' => 1,
                ],
                [
                    'rate' => '5',
                    'is_active' => 1,
                ],
                [
                    'rate' => '8',
                    'is_active' => 1,
                ],
                [
                    'rate' => '10',
                    'is_active' => 1,
                ],
                [
                    'rate' => '12',
                    'is_active' => 1,
                ],
                [
                    'rate' => '15',
                    'is_active' => 1,
                ],
                [
                    'rate' => '18',
                    'is_active' => 1,
                ],
                [
                    'rate' => '20',
                    'is_active' => 1,
                ],
            ];

            foreach ($vatRates as $vatRate) {
                VatRate::updateOrCreate(
                    ['rate' => $vatRate['rate']],
                    $vatRate
                );
            }
        });

        $this->command->info('KDV oranları başarıyla oluşturuldu.');
    }
}
