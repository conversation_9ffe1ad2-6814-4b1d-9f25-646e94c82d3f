<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            RouteSeeder::class,
            RoleSeeder::class,
            UserSeeder::class,
            CountrySeeder::class,
            CitySeeder::class,
            DistrictSeeder::class,
            TaxOfficeSeeder::class,
            VatRateSeeder::class,
            ProductTypeSeeder::class,
            CategorySeeder::class,
            BrandSeeder::class,
            UnitTypeSeeder::class,
            UnitSeeder::class,
            StatusSeeder::class,
            WarehouseSeeder::class,
            WarehouseLocationSeeder::class,
            StockMovementTypeSeeder::class,
            StockMovementReasonSeeder::class,
            StockReservationTypeSeeder::class,
            StockReservationReasonSeeder::class,
            OfferTypeSeeder::class,
            OrderTypeSeeder::class,
            CurrentTypeSeeder::class,
            InstitutionTypeSeeder::class,
            ProductSeeder::class,
            ProductVariantSeeder::class,
            StockSeeder::class,
            CurrentSeeder::class,
            InvoiceTypeSeeder::class,
            InvoiceStatusSeeder::class,
            ExpenseVoucherStatusSeeder::class,
            TransactionTypeSeeder::class,
            WaybillTypeSeeder::class,
            WaybillStatusSeeder::class,
        ]);
    }
}
