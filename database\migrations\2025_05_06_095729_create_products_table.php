<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->string('sku', 255)->unique();
            $table->integer('product_type_id')->nullable();
            $table->longText('description')->nullable();
            $table->decimal('weight', 10, 3)->default(0)->nullable();
            $table->decimal('width', 10, 2)->default(0)->nullable();
            $table->decimal('height', 10, 2)->default(0)->nullable();
            $table->decimal('length', 10, 2)->default(0)->nullable();
            $table->decimal('volume', 10, 4)->default(0)->nullable();
            $table->string('barcode')->nullable()->unique();
            $table->decimal('purchase_price', 10, 2)->default(0)->nullable();
            $table->decimal('sale_price', 10, 2)->default(0)->nullable();
            $table->string('purchase_currency_code', 10)->nullable();
            $table->string('sale_currency_code', 10)->nullable();
            $table->integer('category_id')->nullable();
            $table->integer('brand_id')->nullable();
            $table->integer('unit_type_id')->nullable();
            $table->integer('unit_id')->nullable();
            $table->decimal('quantity', 10, 2)->default(0)->nullable();
            $table->integer('critical_stock_level')->default(0);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
