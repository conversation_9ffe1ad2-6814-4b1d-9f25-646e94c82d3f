@php
$tryRate = $exchangeRates->where('code', 'TRY')->first();
if (old('products')) {
$oldProductsData = old('products');
$repopulatedProducts = [];
$tryRateId = $tryRate->id ?? null;
$allExchangeRates = $exchangeRates->keyBy('id');

foreach ($oldProductsData as $stockId => $productData) {
if (empty($stockId) || empty($productData['stock_id'])) continue;

$product = new \stdClass();
$product->stock_id = $stockId;
$product->item_no = $productData['item_no'] ?? 0;
$product->product_code = $productData['product_code'] ?? '';
$product->product_name = $productData['product_name'] ?? '';
$product->quantity = $productData['quantity'] ?? 1;
$product->unit = $productData['unit'] ?? '';
$product->unit_price = (float)($productData['price'] ?? 0.00);
$product->vat_status = $productData['vat_status'] ?? 1;
$product->exchange_rate_id = $productData['exchange_rate_id'] ?? $tryRateId;

$currentRate = $allExchangeRates->get($product->exchange_rate_id);
$product->exchange_rate = $productData['exchange_rate'] ?? ($currentRate ? $currentRate->selling_rate : 1.0);

$product->vat_rate = (int)($productData['vat_rate'] ?? 0);
$product->total_price = (float)($productData['amount'] ?? 0.00);
$product->vat_amount = (float)($productData['vat_amount'] ?? 0.00);
$product->total_amount = (float)($productData['total_amount'] ?? 0.00);

$repopulatedProducts[] = $product;
}
$products = collect($repopulatedProducts);
}
@endphp
@if (!isset($rowsOnly) || !$rowsOnly)
<div class="col-12 col-md-12">
    <div class="card">
        <div class="card-body">
            <div class="table-responsive scroll-sm">
                <table class="table bordered-table text-sm" id="productTable">
                    <thead>
                        <tr>
                            <th class="text-sm">Sıra No</th>
                            <th class="text-sm">Kodu</th>
                            <th class="text-sm">Açıklama</th>
                            <th class="text-sm">Miktar</th>
                            <th class="text-sm">Birim</th>
                            <th class="text-sm">Birim Fiyat</th>
                            <th class="text-sm">KDV D/H</th>
                            <th class="text-sm">Döviz Türü</th>
                            <th class="text-sm">KDV %</th>
                            <th class="text-sm">Tutar</th>
                            <th class="text-sm">Sil</th>
                        </tr>
                    </thead>
                    <tbody>
                        @endif
                        @if (isset($products) && count($products) > 0)
                        @foreach ($products as $product)
                        <tr data-id="{{ $product->stock_id }}">
                            <td>
                                <input type="text" value="{{$product->item_no}}"
                                    style="width: {{ strlen($product->item_no) + 2 }}ch;" readonly>
                                <input type="hidden" name="products[{{ $product->stock_id }}][stock_id]"
                                    value="{{ $product->stock_id }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][product_code]"
                                    value="{{ $product->product_code }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][product_name]"
                                    value="{{ $product->product_name }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][unit]"
                                    value="{{ $product->unit }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][item_no]"
                                    value="{{ $product->item_no }}">
                            </td>
                            <td>
                                <input type="text" class="invoive-form-control products-code"
                                    name="products[{{$product->stock_id}}][product_code]"
                                    value="{{old('products.'.$product->stock_id.'.product_code', $product->product_code)}}"
                                    style="width: {{ strlen($product->product_code) + 2 }}ch;">
                            </td>
                            <td>
                                <input type="text" class="invoive-form-control products-name"
                                    name="products[{{$product->stock_id}}][product_name]"
                                    value="{{old('products.'.$product->stock_id.'.product_name', $product->product_name)}}"
                                    style="width: {{ strlen($product->product_name) + 2 }}ch;">
                            </td>
                            <td>
                                <input type="number" class="invoive-form-control product-quantity"
                                    name="products[{{ $product->stock_id }}][quantity]"
                                    value="{{ old('products.'.$product->stock_id.'.quantity', $product->quantity) }}"
                                    min="1">
                            </td>
                            <td>
                                <input type="text" class="invoive-form-control products-unit"
                                    name="products[{{$product->stock_id}}][unit]"
                                    value="{{old('products.'.$product->stock_id.'.unit', $product->unit)}}" readonly>
                            </td>
                            <td>
                                <input type="number" class="invoive-form-control product-price"
                                    name="products[{{ $product->stock_id }}][price]"
                                    value="{{ old('products.'.$product->stock_id.'.price', (isset($product->from_db) && $product->from_db) ? $product->unit_price : '0.00') }}"
                                    step="0.01">
                            </td>
                            <td>
                                <select class="invoive-form-control form-control-sm"
                                    name="products[{{ $product->stock_id }}][vat_status]">
                                    <option value="1" {{ old('products.'.$product->stock_id.'.vat_status',
                                        $product->vat_status)
                                        == 1 ? 'selected' : '' }}>Dahil
                                    </option>
                                    <option value="0" {{ old('products.'.$product->stock_id.'.vat_status',
                                        $product->vat_status)
                                        == 0 ? 'selected' : '' }}>Hariç
                                    </option>
                                </select>
                            </td>
                            <td>
                                <select class="invoive-form-control product-currency"
                                    name="products[{{ $product->stock_id }}][exchange_rate_id]">
                                    @foreach ($exchangeRates as $rate)
                                    <option value="{{ $rate->id }}" data-selling-rate="{{ $rate->selling_rate }}"
                                        data-symbol="{{ $rate->symbol }}" {{ old('products.'.$product->
                                        stock_id.'.exchange_rate_id',
                                        (string)$product->exchange_rate_id) === (string)$rate->id ? 'selected' : '' }}>
                                        {{ $rate->code }}
                                    </option>
                                    @endforeach
                                </select>
                                <input type="hidden" name="products[{{ $product->stock_id }}][exchange_rate]"
                                    class="product-exchange-rate"
                                    value="{{ old('products.'.$product->stock_id.'.exchange_rate', $product->exchange_rate) }}">
                            </td>
                            <td>
                                <input type="number" class="invoive-form-controlform-control-sm"
                                    name="products[{{ $product->stock_id }}][vat_rate]"
                                    value="{{ old('products.'.$product->stock_id.'.vat_rate', $product->vat_rate) }}"
                                    step="1.00" required>
                            </td>
                            <td>
                                <input type="text" class="invoive-form-control product-amount"
                                    value="{{ number_format($product->total_price, 2, ',', '.') }}" readonly>
                                <input type="hidden" name="products[{{ $product->stock_id }}][amount]"
                                    value="{{ old('products.'.$product->stock_id.'.amount', $product->total_price) }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][vat_amount]"
                                    value="{{ old('products.'.$product->stock_id.'.vat_amount', $product->vat_amount) }}">
                                <input type="hidden" name="products[{{ $product->stock_id }}][total_amount]"
                                    value="{{ old('products.'.$product->stock_id.'.total_amount', $product->total_amount) }}">
                            </td>
                            <td>
                                <button type="button" class="remove-row remove-product">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        aria-hidden="true" role="img"
                                        class="text-danger-main text-xl iconify iconify--ic" width="1em" height="1em"
                                        viewBox="0 0 24 24">
                                        <path fill="currentColor"
                                            d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z">
                                        </path>
                                    </svg>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                        @endif
                        @if (!isset($rowsOnly) || !$rowsOnly)
                    </tbody>
                </table>
                <x-form-error field="products" />
            </div>
            <button type="button" id="addProductBtn"
                class="btn btn-sm btn-primary-600 radius-8 d-inline-flex align-items-center gap-1 mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true"
                    role="img" class="text-xl iconify iconify--simple-line-icons" width="1em" height="1em"
                    viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                        d="M512 0C229.232 0 0 229.232 0 512c0 282.784 229.232 512 512 512c282.784 0 512-229.216 512-512C1024 229.232 794.784 0 512 0m0 961.008c-247.024 0-448-201.984-448-449.01c0-247.024 200.976-448 448-448s448 200.977 448 448s-200.976 449.01-448 449.01M736 480H544V288c0-17.664-14.336-32-32-32s-32 14.336-32 32v192H288c-17.664 0-32 14.336-32 32s14.336 32 32 32h192v192c0 17.664 14.336 32 32 32s32-14.336 32-32V544h192c17.664 0 32-14.336 32-32s-14.336-32-32-32">
                    </path>
                </svg> Ekle
            </button>
        </div>
    </div>
</div>

@endif