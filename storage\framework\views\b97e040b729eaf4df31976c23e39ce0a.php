<?php
    $title = $container->title;
    $subTitle = $container->title . ' Ayarları';
?>

<?php $__env->startSection('content'); ?>
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6"><?php echo e($container->title); ?></h5>
                </div>

                <div class="card-body">
                    <form action="<?php echo e(route('backend.' . $container->page . '_save')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row gy-3">
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Para Gösterimi - Ondalık Basamak Sayısı</label>
                                    <div class="icon-field">
                                        <span class="icon">
                                            <iconify-icon icon="mdi:decimal"></iconify-icon>
                                        </span>
                                        <select class="form-control form-select" name="decimal_places">
                                            <?php $__currentLoopData = $item->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($value); ?>" 
                                                    <?php echo e(old('decimal_places', $item->value) == $value ? 'selected' : ''); ?>>
                                                    <?php echo e($label); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <?php if (isset($component)) { $__componentOriginalc93285135aa759ebaf0b3dc38aeeeb0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc93285135aa759ebaf0b3dc38aeeeb0d = $attributes; } ?>
<?php $component = App\View\Components\FormError::resolve(['field' => 'decimal_places'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\FormError::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc93285135aa759ebaf0b3dc38aeeeb0d)): ?>
<?php $attributes = $__attributesOriginalc93285135aa759ebaf0b3dc38aeeeb0d; ?>
<?php unset($__attributesOriginalc93285135aa759ebaf0b3dc38aeeeb0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc93285135aa759ebaf0b3dc38aeeeb0d)): ?>
<?php $component = $__componentOriginalc93285135aa759ebaf0b3dc38aeeeb0d; ?>
<?php unset($__componentOriginalc93285135aa759ebaf0b3dc38aeeeb0d); ?>
<?php endif; ?>
                                    <small class="form-text text-muted">
                                        <?php echo e($item->description); ?>

                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><iconify-icon icon="mdi:information"></iconify-icon> Bilgi</h6>
                                    <p class="mb-0">
                                        Bu ayar, teklif ve teklif ürünlerindeki para değerlerinin kullanıcı arayüzünde nasıl gösterileceğini belirler. 
                                        Veriler veritabanında her zaman 8 basamaklı ondalık olarak saklanır, sadece gösterim bu ayara göre yapılır.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3 mt-4">
                            <button type="submit" class="btn btn-primary d-flex align-items-center gap-2">
                                <iconify-icon icon="iconamoon:check-bold"></iconify-icon>
                                Kaydet
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\erp_web\resources\views/backend/setting/form.blade.php ENDPATH**/ ?>