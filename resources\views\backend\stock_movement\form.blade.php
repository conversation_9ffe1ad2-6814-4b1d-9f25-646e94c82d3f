@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? 'Stok Hareketi Düzenle' : 'Yeni Stok Hareketi Ekle';
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $subTitle }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                        @csrf
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Genel Bilgiler</h6>
                            </div>
                            <div class="card-body">
                                <div class="row gy-3">
                                    <div class="col-12 col-md-6">
                                        <label class="form-label">Hareket Tipi <span class="text-danger">*</span></label>
                                        <select name="movement_type_id" id="movement-type-select" class="form-select select2" required>
                                            <option value="">Seçiniz</option>
                                            @foreach ($movementTypes as $type)
                                                <option value="{{ $type->id }}"
                                                    {{ old('movement_type_id', $item->movement_type_id) == $type->id ? 'selected' : '' }}>
                                                    {{ $type->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <x-form-error field="movement_type_id" />
                                    </div>

                                    <div class="col-12 col-md-6" id="movement-reason-section">
                                        <label class="form-label">Hareket Nedeni <span class="text-danger">*</span></label>
                                        <select name="stock_movement_reason_id" class="form-select select2" required>
                                            <option value="">Önce hareket tipi seçiniz</option>
                                        </select>
                                        <x-form-error field="stock_movement_reason_id" />
                                    </div>

                                    <div class="col-12 col-md-6">
                                        <label class="form-label">Hareket Tarihi <span class="text-danger">*</span></label>
                                        <input type="datetime-local" name="movement_date" class="form-control"
                                            value="{{ old('movement_date', $item->movement_date ? $item->movement_date->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i')) }}"
                                            required>
                                        <x-form-error field="movement_date" />
                                    </div>

                                    <div class="col-12 col-md-6" id="source-warehouse-section">
                                        <label class="form-label">Depo</label>
                                        <select name="warehouse_id" class="form-select select2">
                                            <option value="">Seçiniz</option>
                                            @foreach ($warehouses as $warehouse)
                                                <option value="{{ $warehouse->id }}"
                                                    {{ old('warehouse_id', $item->warehouse_id) == $warehouse->id ? 'selected' : '' }}>
                                                    {{ $warehouse->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <x-form-error field="warehouse_id" />
                                    </div>

                                    <div class="col-12 col-md-6" id="source-location-section">
                                        <label class="form-label">Lokasyon</label>
                                        <select name="location_id" class="form-select select2">
                                            <option value="">Seçiniz</option>
                                            @if ($item->warehouse_id)
                                                @foreach ($locations->where('warehouse_id', $item->warehouse_id) as $location)
                                                    <option value="{{ $location->id }}"
                                                        {{ old('location_id', $item->location_id) == $location->id ? 'selected' : '' }}>
                                                        {{ $location->name }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                        <x-form-error field="location_id" />
                                    </div>

                                    <div class="col-12 col-md-6" id="target-warehouse-section" style="display: none;">
                                        <label class="form-label">Hedef Depo</label>
                                        <select name="target_warehouse_id" class="form-select select2">
                                            <option value="">Seçiniz</option>
                                            @foreach ($warehouses as $warehouse)
                                                <option value="{{ $warehouse->id }}"
                                                    {{ old('target_warehouse_id', $item->target_warehouse_id) == $warehouse->id ? 'selected' : '' }}>
                                                    {{ $warehouse->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <x-form-error field="target_warehouse_id" />
                                    </div>

                                    <div class="col-12 col-md-6" id="target-location-section" style="display: none;">
                                        <label class="form-label">Hedef Lokasyon</label>
                                        <select name="target_location_id" class="form-select select2">
                                            <option value="">Seçiniz</option>
                                            @if ($item->target_warehouse_id)
                                                @foreach ($locations->where('warehouse_id', $item->target_warehouse_id) as $location)
                                                    <option value="{{ $location->id }}"
                                                        {{ old('target_location_id', $item->target_location_id) == $location->id ? 'selected' : '' }}>
                                                        {{ $location->name }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </select>
                                        <x-form-error field="target_location_id" />
                                    </div>

                                    <div class="col-12 col-md-6" id="current-section">
                                        <label class="form-label">Cari Hesap</label>
                                        <select name="current_id" class="form-select select2">
                                            <option value="">Seçiniz</option>
                                            @foreach ($currents as $current)
                                                <option value="{{ $current->id }}"
                                                    {{ old('current_id', $item->current_id) == $current->id ? 'selected' : '' }}>
                                                    {{ $current->title }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <x-form-error field="current_id" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">Ürün Listesi</h6>
                                <button type="button" class="btn btn-primary btn-sm" id="add-product-row">
                                    Ürün Ekle
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="products-container" class="row gy-3">
                                </div>

                                <div class="alert alert-info" id="no-products-message">
                                    Henüz ürün eklenmedi. "Ürün Ekle" butonuna tıklayarak ürün ekleyebilirsiniz.
                                </div>
                            </div>
                        </div>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Notlar</h6>
                            </div>
                            <div class="card-body">
                                <textarea name="notes" class="form-control" rows="3"
                                    placeholder="Ek notlar ekleyebilirsiniz...">{{ old('notes', $item->notes) }}</textarea>
                                <x-form-error field="notes" />
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                Kaydet
                            </button>
                            <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">
                                İptal
                            </a>
                        </div>

                        <input type="hidden" id="products_data" name="products_data" value="{{ old('products_data') }}">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <template id="product-row-template">
        <div class="col-12 product-row-wrapper" data-index="">
            <div class="card border">
                <div class="card-body p-3">
                    <div class="row gy-3">
                        <div class="col-12 col-md-4">
                            <label class="form-label">Ürün <span class="text-danger">*</span></label>
                            <select name="products[INDEX][product_id]" class="form-select select2 product-select" required>
                                <option value="">Seçiniz</option>
                            </select>
                        </div>

                        <div class="col-12 col-md-3">
                            <label class="form-label">Varyant</label>
                            <select name="products[INDEX][variant_id]" class="form-select select2 variant-select">
                                <option value="">Seçiniz</option>
                            </select>
                        </div>

                        <div class="col-12 col-md-2">
                            <label class="form-label">Miktar <span class="text-danger">*</span></label>
                            <input type="number"
                                   step="0.01"
                                   name="products[INDEX][quantity]"
                                   class="form-control quantity-input"
                                   required
                                   min="0.01"
                                   placeholder="0.00"
                                   data-allow-decimal="1"
                                   data-decimal-places="4"
                                   data-unit-symbol="adet">
                        </div>
                        <div class="col-12 col-md-2">
                            <label class="form-label">Birim</label>
                            <input type="text" class="form-control unit-display" readonly placeholder="-">
                        </div>

                        <div class="col-12 col-md-1">
                            <label class="form-label">Sil</label>
                            <button type="button" class="btn btn-danger btn-sm remove-product-row d-block w-100">
                                Sil
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var allVariants = [
                @foreach ($variants as $variant)
                    {
                        id: {{ $variant->id }},
                        name: "{{ $variant->name }}",
                        product_id: {{ $variant->product_id }}
                    },
                @endforeach
            ];

            var allProducts = [
                @foreach ($products as $product)
                    {
                        id: {{ $product->id }},
                        name: "{{ $product->name }}",
                        sku: "{{ $product->sku }}",
                        unit_id: {{ $product->unit_id ?? 'null' }},
                        unit_name: "{{ $product->unit->name ?? '' }}",
                        unit_symbol: "{{ $product->unit->symbol ?? '' }}",
                        unit_allow_decimal: {{ $product->unit->allow_decimal ?? 1 }},
                        unit_decimal_places: {{ $product->unit->decimal_places ?? 4 }}
                    },
                @endforeach
            ];

            var allStocks = [
                @foreach ($stocks as $stock)
                    {
                        id: {{ $stock->id }},
                        product_id: {{ $stock->product_id }},
                        variant_id: {{ $stock->variant_id ?? 'null' }},
                        warehouse_id: {{ $stock->warehouse_id }},
                        warehouse_location_id: {{ $stock->warehouse_location_id ?? 'null' }},
                        quantity: {{ $stock->quantity }},
                        available_quantity: {{ $stock->quantity }},
                        product_name: "{{ $stock->product->name }}",
                        product_sku: "{{ $stock->product->sku }}"
                    },
                @endforeach
            ];

            var allLocations = [
                @foreach ($locations as $location)
                    {
                        id: {{ $location->id }},
                        name: "{{ $location->name }}",
                        warehouse_id: {{ $location->warehouse_id }}
                    },
                @endforeach
            ];

            var allMovementReasons = [
                @foreach ($movementReasons as $reason)
                    {
                        id: {{ $reason->id }},
                        name: "{{ $reason->name }}",
                        movement_type_id: {{ $reason->movement_type_id }}
                    },
                @endforeach
            ];

            var productRowIndex = 0;

            var currentMovementType = null;

            $('#movement-type-select').on('change', function() {
                var movementTypeId = $(this).val();
                currentMovementType = movementTypeId;

                updateMovementReasons(movementTypeId);
                updateFormFields(movementTypeId);
                updateAllProductSelects();
            }).trigger('change');

            function updateMovementReasons(movementTypeId) {
                var reasonSelect = $('select[name="stock_movement_reason_id"]');
                var isNewRecord = !@json($item->id);

                if (movementTypeId == 3) {
                    reasonSelect.html('<option value="">Transfer</option>');
                    reasonSelect.removeAttr('required');
                    reasonSelect.val('');
                } else if (movementTypeId) {
                    reasonSelect.attr('required', 'required');
                    reasonSelect.html('<option value="">Seçiniz</option>');

                    var currentReasonId = @json($item->stock_movement_reason_id);

                    var filteredReasons = allMovementReasons.filter(function(reason) {
                        var matchesType = reason.movement_type_id == movementTypeId;

                        if (movementTypeId == 1) {
                            return reason.id == 5 || reason.id == 12 || (!isNewRecord && reason.id == currentReasonId);
                        }

                        if (movementTypeId == 2) {
                            return reason.id == 10 || (!isNewRecord && reason.id == currentReasonId);
                        }

                        return matchesType;
                    });

                    filteredReasons.forEach(function(reason) {
                        reasonSelect.append('<option value="' + reason.id + '">' + reason.name +
                            '</option>');
                    });
                } else {
                    reasonSelect.attr('required', 'required');
                    reasonSelect.html('<option value="">Önce hareket tipi seçiniz</option>');
                }
            }

                        function updateFormFields(movementTypeId) {
                if (movementTypeId == 1) {
                    $('#target-warehouse-section').hide();
                    $('#target-location-section').hide();
                    $('#source-warehouse-section').show();
                    $('#source-location-section').show();
                    $('#source-warehouse-section label').text('Depo');
                    $('#source-location-section label').text('Lokasyon');
                    $('#current-section').show();
                    $('#movement-reason-section').show();

                } else if (movementTypeId == 2) {
                    $('#target-warehouse-section').hide();
                    $('#target-location-section').hide();
                    $('#source-warehouse-section').show();
                    $('#source-location-section').show();
                    $('#source-warehouse-section label').text('Kaynak Depo');
                    $('#source-location-section label').text('Kaynak Lokasyon');
                    $('#current-section').show();
                    $('#movement-reason-section').show();

                } else if (movementTypeId == 3) {
                    $('#target-warehouse-section').show();
                    $('#target-location-section').show();
                    $('#source-warehouse-section').show();
                    $('#source-location-section').show();
                    $('#source-warehouse-section label').text('Kaynak Depo');
                    $('#source-location-section label').text('Kaynak Lokasyon');
                    $('#current-section').hide();
                    $('#movement-reason-section').hide();

                } else {
                    $('#current-section').hide();
                    $('#movement-reason-section').hide();
                }
            }



            $('#add-product-row').on('click', function() {
                addProductRow();
            });

            function loadExistingData() {
                @if ($item->warehouse_id)
                    $('select[name="warehouse_id"]').val('{{ $item->warehouse_id }}').trigger('change');

                    setTimeout(function() {
                        @if ($item->location_id)
                            $('select[name="location_id"]').val('{{ $item->location_id }}');
                        @endif
                    }, 50);
                @endif

                @if ($item->target_warehouse_id)
                    setTimeout(function() {
                        $('select[name="target_warehouse_id"]').val('{{ $item->target_warehouse_id }}')
                            .trigger('change');

                        setTimeout(function() {
                            @if ($item->target_location_id)
                                $('select[name="target_location_id"]').val(
                                    '{{ $item->target_location_id }}');
                            @endif
                        }, 50);
                    }, 100);
                @endif

                setTimeout(function() {
                    @if ($item->stock_movement_type_id)
                        $('#movement-type-select').val('{{ $item->stock_movement_type_id }}').trigger(
                            'change');

                        setTimeout(function() {
                            @if ($item->stock_movement_reason_id)
                                $('select[name="stock_movement_reason_id"]').val(
                                    '{{ $item->stock_movement_reason_id }}');
                            @endif
                        }, 100);
                    @endif
                }, 150);

                setTimeout(function() {
                    @if ($item->stock_movement_type_id)
                        currentMovementType = '{{ $item->stock_movement_type_id }}';
                    @endif

                    @if ($item->items->count() > 0)
                        @foreach ($item->items as $index => $movementItem)
                            var productData = {
                                product_id: {{ $movementItem->product_id }},
                                variant_id: {{ $movementItem->variant_id ?? 'null' }},
                                quantity: {{ $movementItem->quantity }}
                            };

                            addProductRowWithData(productData);
                        @endforeach
                    @else
                        addProductRow();
                    @endif
                }, 500);
            }

            function addProductRowWithData(data) {
                var template = $('#product-row-template').html();
                template = template.replace(/INDEX/g, productRowIndex);

                var $row = $(template);
                $row.attr('data-index', productRowIndex);

                $('#products-container').append($row);
                $('#no-products-message').hide();

                initializeProductRow($row, productRowIndex);

                setTimeout(function() {
                    updateProductSelect($row.find('.product-select'));
                    $row.find('.product-select').val(data.product_id).trigger('change');

                    setTimeout(function() {
                                                if (data.variant_id && data.variant_id !== 'null') {
                            $row.find('.variant-select').val(data.variant_id);
                        }
                        $row.find('.quantity-input').val(data.quantity);

                        updateQuantityDisplay($row);
                        setupQuantityInput($row, data.product_id);
                        updateProductsData();
                    }, 150);
                }, 50);

                productRowIndex++;
            }

            function addProductRow() {
                var template = $('#product-row-template').html();
                template = template.replace(/INDEX/g, productRowIndex);

                var $row = $(template);
                $row.attr('data-index', productRowIndex);

                $('#products-container').append($row);
                $('#no-products-message').hide();

                // Yeni eklenen satırın card yapısını doğru şekilde işlemek için
                initializeProductRow($row, productRowIndex);
                productRowIndex++;
                updateProductsData();
            }

            function initializeProductRow($row, index) {
                var $productSelect = $row.find('.product-select');
                var $variantSelect = $row.find('.variant-select');
                var $unitDisplay = $row.find('.unit-display');
                var $quantityInput = $row.find('.quantity-input');

                $productSelect.select2({
                    placeholder: "Ürün seçiniz",
                    allowClear: true
                });
                $variantSelect.select2({
                    placeholder: "Varyant seçiniz",
                    allowClear: true
                });

                updateProductSelect($productSelect);

                // İlk kurulum
                setupQuantityInput($row, null);

                $productSelect.on('change', function() {
                    var productId = $(this).val();
                    updateVariants($variantSelect, productId);
                    updateQuantityDisplay($row);
                    setupQuantityInput($row, productId);
                });

                $variantSelect.on('change', function() {
                    updateQuantityDisplay($row);
                });

                $quantityInput.on('input', function() {
                    updateQuantityDisplay($row);
                });

                                $row.find('.remove-product-row').on('click', function() {
                    $row.remove();
                    updateProductsData();
                    if ($('#products-container .product-row-wrapper').length === 0) {
                        $('#no-products-message').show();
                    }
                });
            }

            function updateVariants($variantSelect, productId) {
                $variantSelect.html('<option value="">Seçiniz</option>');

                if (productId) {
                    var filteredVariants = allVariants.filter(function(variant) {
                        return variant.product_id == productId;
                    });

                    filteredVariants.forEach(function(variant) {
                        $variantSelect.append('<option value="' + variant.id + '">' + variant.name +
                            '</option>');
                    });
                }
            }

            function updateUnitDisplay($unitDisplay, productId) {
                if (productId) {
                    var selectedProduct = allProducts.find(function(product) {
                        return product.id == productId;
                    });

                    if (selectedProduct && selectedProduct.unit_symbol) {
                        $unitDisplay.val(selectedProduct.unit_symbol);
                    } else {
                        $unitDisplay.val('-');
                    }
                } else {
                    $unitDisplay.val('-');
                }
            }

            function updateQuantityDisplay($row) {
                var productId = $row.find('.product-select').val();
                updateUnitDisplay($row.find('.unit-display'), productId);
                setupQuantityInput($row, productId);
            }

            function setupQuantityInput($row, productId) {
                var $quantityInput = $row.find('.quantity-input');

                if (!productId) {
                    // Varsayılan değerler
                    $quantityInput.attr('step', '0.01');
                    $quantityInput.attr('min', '0.01');
                    $quantityInput.removeAttr('pattern');
                    $quantityInput.data('allow-decimal', 1);
                    $quantityInput.data('decimal-places', 4);
                    $quantityInput.data('unit-symbol', 'adet');
                    return;
                }

                var selectedProduct = allProducts.find(function(product) {
                    return product.id == productId;
                });

                if (selectedProduct) {
                    var allowDecimal = selectedProduct.unit_allow_decimal;
                    var decimalPlaces = selectedProduct.unit_decimal_places;
                    var unitSymbol = selectedProduct.unit_symbol || 'adet';

                    // Input özelliklerini güncelle
                    if (allowDecimal == 0) {
                        $quantityInput.attr('step', '1');
                        $quantityInput.attr('min', '1');
                        $quantityInput.attr('pattern', '[0-9]+');
                    } else {
                        var step = '0.' + '0'.repeat(decimalPlaces - 1) + '1';
                        $quantityInput.attr('step', step);
                        $quantityInput.attr('min', step);
                        $quantityInput.removeAttr('pattern');
                    }

                    // Data attribute'ları güncelle
                    $quantityInput.data('allow-decimal', allowDecimal);
                    $quantityInput.data('decimal-places', decimalPlaces);
                    $quantityInput.data('unit-symbol', unitSymbol);
                }
            }

            function updateAllProductSelects() {
                $('#products-container .product-select').each(function() {
                    updateProductSelect($(this));
                });
            }

            function updateProductSelect($select) {
                var currentValue = $select.val();
                var availableProducts = getAvailableProducts();

                $select.html('<option value="">Seçiniz</option>');

                availableProducts.forEach(function(product) {
                    var stockInfo = '';
                    if (currentMovementType == 2 || currentMovementType == 3) {
                        var stock = getProductStock(product.id);
                        if (stock && stock.quantity > 0) {
                            stockInfo = ' (Stok: ' + stock.quantity + ')';
                        }
                    }

                    $select.append('<option value="' + product.id + '">' +
                        product.name + ' (' + product.sku + ')' + stockInfo + '</option>');
                });

                if (currentValue && availableProducts.some(p => p.id == currentValue)) {
                    $select.val(currentValue);
                }
            }

            function getAvailableProducts() {
                if (currentMovementType == 1) {
                    return allProducts;
                }

                var warehouseId = $('select[name="warehouse_id"]').val();
                var locationId = $('select[name="location_id"]').val();

                if (!warehouseId) {
                    return [];
                }

                var availableProductIds = [];
                var filteredStocks = allStocks.filter(function(stock) {
                    var warehouseMatch = stock.warehouse_id == warehouseId;
                    var locationMatch = !locationId || stock.warehouse_location_id == locationId;
                    var hasStock = stock.quantity > 0;

                    return warehouseMatch && locationMatch && hasStock;
                });

                filteredStocks.forEach(function(stock) {
                    if (availableProductIds.indexOf(stock.product_id) === -1) {
                        availableProductIds.push(stock.product_id);
                    }
                });

                return allProducts.filter(function(product) {
                    return availableProductIds.indexOf(product.id) !== -1;
                });
            }

            function getProductStock(productId, variantId = null) {
                var warehouseId = $('select[name="warehouse_id"]').val();
                var locationId = $('select[name="location_id"]').val();

                if (!warehouseId) {
                    return null;
                }

                var stocks = allStocks.filter(function(stock) {
                    var warehouseMatch = stock.warehouse_id == warehouseId;
                    var locationMatch = !locationId || stock.warehouse_location_id == locationId;
                    var productMatch = stock.product_id == productId;
                    var variantMatch = variantId ? stock.variant_id == variantId : true;

                    return warehouseMatch && locationMatch && productMatch && variantMatch;
                });

                var totalQuantity = stocks.reduce(function(total, stock) {
                    return total + stock.quantity;
                }, 0);

                return {
                    quantity: totalQuantity,
                    stocks: stocks
                };
            }



                        function updateProductsData() {
                var products = [];

                $('#products-container .product-row-wrapper').each(function() {
                    var $row = $(this);
                    var productId = $row.find('.product-select').val();
                    var quantity = parseFloat($row.find('.quantity-input').val()) || 0;
                    var variantId = $row.find('.variant-select').val();

                    if (productId && quantity > 0) {
                        var productData = {
                            product_id: parseInt(productId),
                            variant_id: variantId ? parseInt(variantId) : null,
                            quantity: quantity
                        };

                        products.push(productData);
                    }
                });

                $('#products_data').val(JSON.stringify(products));
            }

            $('select[name="warehouse_id"]').on('change', function() {
                var warehouseId = $(this).val();
                var locationSelect = $('select[name="location_id"]');

                locationSelect.html('<option value="">Seçiniz</option>');

                if (warehouseId) {
                    var filteredLocations = allLocations.filter(function(location) {
                        return location.warehouse_id == warehouseId;
                    });

                    filteredLocations.forEach(function(location) {
                        locationSelect.append('<option value="' + location.id + '">' + location
                            .name + '</option>');
                    });
                }
                updateAllProductSelects();
            });

            $('select[name="location_id"]').on('change', function() {
                updateAllProductSelects();
            });

            $('select[name="target_warehouse_id"]').on('change', function() {
                var warehouseId = $(this).val();
                var locationSelect = $('select[name="target_location_id"]');

                locationSelect.html('<option value="">Seçiniz</option>');

                if (warehouseId) {
                    var filteredLocations = allLocations.filter(function(location) {
                        return location.warehouse_id == warehouseId;
                    });

                    filteredLocations.forEach(function(location) {
                        locationSelect.append('<option value="' + location.id + '">' + location
                            .name + '</option>');
                    });
                }
            });

            // Quantity input kontrolü - birim ayarlarına göre
            $(document).on('input', '.quantity-input', function() {
                var input = $(this);

                // Birim kontrolü - ondalık kabul etmiyorsa tam sayıya yuvarla
                var allowDecimal = input.data('allow-decimal');
                if (allowDecimal == 0) {
                    var value = parseFloat(input.val()) || 0;
                    if (value % 1 !== 0) {
                        input.val(Math.round(value));
                    }
                }

                updateProductsData();
            });

            // Blur event'i ile ek kontrol
            $(document).on('blur', '.quantity-input', function() {
                var input = $(this);
                var allowDecimal = input.data('allow-decimal');
                var decimalPlaces = input.data('decimal-places') || 4;

                var value = parseFloat(input.val()) || 0;

                if (allowDecimal == 0) {
                    input.val(Math.round(value));
                } else {
                    input.val(value.toFixed(decimalPlaces));
                }

                updateProductsData();
            });

            $(document).on('change', '.product-select, .variant-select, .quantity-input', function() {
                updateProductsData();
            });

            $('#stock-movement-form').on('submit', function(e) {
                var productsData = $('#products_data').val();

                if (!productsData || productsData === '[]') {
                    e.preventDefault();
                    alert('En az bir ürün eklemelisiniz!');
                    return false;
                }

                $('#products_data').val(JSON.stringify(products));
            });

            @if ($item->id)
                loadExistingData();
            @else
                addProductRow();
            @endif
        });
    </script>
@endsection
