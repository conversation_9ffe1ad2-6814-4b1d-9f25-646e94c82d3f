<?php

namespace App\Http\Controllers\Backend;

use App\Models\ExpenseVoucher;
use Illuminate\Http\Request;
use App\Models\ExpenseType;
use App\Models\ExpenseVoucherStatu;
use App\Models\PaymentType;
use Illuminate\Support\Facades\Cache;

class ExpenseVouchersController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Masraf Fişleri';
        $this->page = 'expense_voucher';
        $this->model = new ExpenseVoucher();
        $this->listQuery = ExpenseVoucher::filter(request())->select();
        $this->relation = ['expenseType', 'paymentType', 'expenseVoucherStatu'];
        $this->view = (object) [
            'breadcrumb' => [
                'Masraf Fişleri' => route('backend.expense_voucher_list'),
            ],
        ];

        $this->validation = [
            // Validation kuralları
            [
                'voucher_date' => 'required|date',
                'expense_type_id' => 'required|exists:expense_types,id',
                'payment_type_id' => 'required|exists:payment_types,id',
                'description' => 'nullable|string|max:1000',
                'subtotal' => 'required|numeric|min:0',
                'vat_total' => 'nullable|numeric|min:0',
                'vat_rate' => 'required|numeric|min:0',
                'grand_total' => 'required|numeric|min:0',
                'expense_voucher_statu_id' => 'required|integer|exists:expense_voucher_status,id',
                'reason_for_cancellation' => 'required_if:expense_voucher_statu_id,3|nullable|string|min:5|max:500',
            ],
            // Validation mesajları
            [
                'voucher_date.required' => 'Fiş tarihi zorunludur.',
                'expense_type_id.required' => 'Masraf türü seçimi zorunludur.',
                'payment_type_id.required' => 'Ödeme türü seçimi zorunludur.',
                'subtotal.required' => 'Ara toplam zorunludur.',
                'vat_rate.required' => 'Kdv oranı zorunludur.',
                'grand_total.required' => 'Genel toplam zorunludur.',
                'expense_voucher_statu_id.required' => 'Fiş durumu zorunludur.',
                'expense_voucher_statu_id.exists' => 'Seçilen fiş durumu geçerli değil.',
                'reason_for_cancellation.required_if' => 'İptal durumunda iptal sebebi girmek zorunludur.',
                'reason_for_cancellation.min' => 'İptal sebebi en az 5 karakter olmalıdır.',
                'reason_for_cancellation.max' => 'İptal sebebi en fazla 500 karakter olabilir.',
            ]
        ];

        view()->share('expenseVoucherStatu', ExpenseVoucherStatu::where('is_active', 1)->get());
        view()->share('expenseTypes', ExpenseType::where('is_active', 1)->orderBy('sort_order')->get());
        view()->share('paymentTypes', PaymentType::where('is_active', 1)->orderBy('sort_order')->get());

        parent::__construct();
    }
    public function detail(Request $request, $unique = null)
    {
        $item = $this->model::with($this->relation)->find((int)$unique);
        
        if (!$item) {
            return redirect()->route('backend.' . $this->page . '_list')->with('error', 'Masraf fişi bulunamadı');
        }

        return view("backend.$this->page.detail", compact('item'));
    }

    public function updateStatus(Request $request, $unique = null)
    {
        $item = $this->model::find((int)$unique);
        
        if (!$item) {
            return response()->json([
                'status' => false,
                'message' => 'Masraf fişi bulunamadı'
            ]);
        }

        // Sadece bekleyen durumdaki fişler değiştirilebilir
        if ($item->expense_voucher_statu_id != 1) {
            return response()->json([
                'status' => false,
                'message' => 'Bu fişin durumu zaten değiştirilmiş. Sadece bekleyen durumdaki fişler değiştirilebilir.'
            ]);
        }

        $request->validate([
            'expense_voucher_statu_id' => 'required|integer|exists:expense_voucher_status,id',
            'reason_for_cancellation' => 'required_if:expense_voucher_statu_id,3|nullable|string|min:5|max:500',
        ], [
            'expense_voucher_statu_id.required' => 'Durum seçimi zorunludur.',
            'expense_voucher_statu_id.exists' => 'Seçilen durum geçerli değil.',
            'reason_for_cancellation.required_if' => 'İptal durumunda iptal sebebi girmek zorunludur.',
            'reason_for_cancellation.min' => 'İptal sebebi en az 5 karakter olmalıdır.',
            'reason_for_cancellation.max' => 'İptal sebebi en fazla 500 karakter olabilir.',
        ]);

        try {
            $item->update([
                'expense_voucher_statu_id' => $request->expense_voucher_statu_id,
                'reason_for_cancellation' => $request->reason_for_cancellation,
            ]);

            Cache::flush();

            return response()->json([
                'status' => true,
                'message' => 'Durum başarıyla güncellendi'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Durum güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        $voucher = $this->model::find((int)$request->post('id'));

        if (!$voucher) {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı',
            ]);
        }

        try {
            // bootHook() içindeki deleting çalışır ve false dönerse silme işlemi iptal olur.
            if ($voucher->delete()) {
                Cache::flush(); // Daha iyisi: Cache::forget("expense_voucher_{$voucher->id}");

                return response()->json([
                    'status' => true,
                    'message' => 'Masraf fişi başarıyla silindi',
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => session()->get('error') ?: 'Masraf fişi silinemedi.',
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
