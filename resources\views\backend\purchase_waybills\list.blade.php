@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.purchase_waybill_export_excel') }}"
                    class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                    Excel
                </a>
                <a href="{{ route('backend.' . $container->page . '_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                    Ekle
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date" filter-name="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date" filter-name="filter-end-date">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Durum</label>
                    <select class="form-control" id="filter-waybill-statu-id" filter-name = "filter-waybill-statu-id">
                        <option value="">Tüm Durumlar</option>
                        @foreach ($waybillStatu as $statu)
                            <option value="{{ $statu->id }}">{{ $statu->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="table-respıonsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">İrsaliye No</th>
                            <th scope="col" class="text-center">Tarih</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Veriler AJAX ile yüklenecek -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            // DataTable
            const table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        return $.extend({}, d, {
                            start_date: $('[filter-name="filter-start-date"]').val(),
                            end_date: $('[filter-name="filter-end-date"]').val(),
                            waybill_statu_id: $('[filter-name = "filter-waybill-statu-id"]').val() // Durum filtresini ekle
                        });
                    }
                },
                columns: [{
                        data: 'waybill_no',
                        name: 'waybill_no',
                        className: 'text-center'
                    },
                    {
                        data: 'waybill_date',
                        name: 'waybill_date',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                if (data) {
                                    var date = new Date(data);
                                    return date.toLocaleDateString('tr-TR');
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    {
                        data: 'total_amount',
                        name: 'total_amount',
                        className: 'text-center',
                        render: function(data, type, row) {
                            let currencySymbol = '₺'; // Varsayılan sembol (TL)
                            if (row.currency_symbol) {
                                currencySymbol = row.currency_symbol;
                            } else if (row.exchangeRate && row.exchangeRate.symbol) {
                                currencySymbol = row.exchangeRate.symbol;
                            }
                            return parseFloat(data).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }) + ' ' + currencySymbol;
                        }
                    },
                    {
                        data: 'waybillStatu.name',
                        name: 'waybillStatu.name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            const rowId = row.id || '';
                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.purchase_waybill_pdf') }}/${rowId}" class="bg-danger-100 text-danger-600 bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center  rounded-circle" target="_blank">
                                    <iconify-icon icon="mdi:file-pdf-box" width="24" height="24"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.purchase_waybill_detail') }}/${row.id}" class="bg-primary-light text-primary-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="iconamoon:eye-light" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    }
                ],
                order: [
                    [1, 'desc']
                ],
                pageLength: 15,
            });
            $('#filter-waybill-statu-id, #filter-start-date, #filter-end-date').on('change',
                function() {
                    table.ajax.reload();
                });
            $('[filter-name]').on('change input', function() {
                $(`[datatable]`).DataTable().ajax.reload();
            });
            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");
        });
    </script>
@endsection
