<?php

namespace App\Http\Controllers\Backend;

use App\Http\Requests\Backend\OrderRequest;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderType;
use App\Models\PaymentType;
use App\Models\Stock;
use App\Models\Warehouse;
use App\Models\StockReservation;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\StockMovement;
use App\Models\StockMovementItem;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ReceivedOrderController extends BaseController
{
    use BasePattern;


    public function __construct()
    {
        $this->title = 'Alınan Sipariş';
        $this->page = 'order_received';
        $this->model = new Order();
        $this->relation = [
            'warehouse',
            'exchangeRate',
            'current' => function ($query) {
                $query->withTrashed();
            },
            'paymentType',
            'orderProducts.stock.product.unit',
            'orderProducts.stock.variant',
        ];
        $this->listQuery = Order::filter(request())->where('order_type_id', 1);
        $this->view = (object)array(
            'breadcrumb' => array(
                'Ayarlar' => '#',
                'Alınan Sipariş' => route('backend.order_received_list'),
            ),
        );

        view()->share('orderProducts', OrderProduct::get());
        view()->share('warehouses', Warehouse::get());
        view()->share('exchangeRate', ExchangeRate::where('is_active', 1)->whereIn('id', function ($query) {
            $query->selectRaw('MAX(id)')
                ->from('exchange_rates')
                ->where('is_active', 1)
                ->groupBy('code');
        })->get());
        view()->share('orderType', OrderType::get());
        view()->share('current', Current::with(['country', 'city', 'district'])->whereIn('current_type_id', [1, 3])->where('is_active', 1)->get());
        view()->share('paymentType', PaymentType::get());
        parent::__construct();
    }
    public function save(Request $request, $unique = null)
    {
        try {
            $validator = Validator::make($request->all(), (new OrderRequest())->rules(), (new OrderRequest())->messages());
            if ($validator->fails()) {
                return $request->ajax()
                    ? response()->json(['success' => false, 'errors' => $validator->errors()], 422)
                    : redirect()->back()->withErrors($validator)->withInput();
            }
            
            // Database transaction başlat
            DB::beginTransaction();
            
            $params = $request->except(['products', 'stockTable_length', 'stockTable_start', 'stockTable_search', 'stockTable_order', 'stockTable_draw']);
            $products = $request->has('products')
                ? (is_string($request->input('products'))
                    ? json_decode($request->input('products'), true) ?? []
                    : $request->input('products'))
                : [];
            if (!empty($products)) {
                $params['total_price'] = round(parseTrNumber($request->input('total_price', 0)), 8);
                $params['vat_amount'] = round(parseTrNumber($request->input('vat_amount', 0)), 8);
                $params['total_amount'] = round(parseTrNumber($request->input('total_amount', 0)), 8);
            }
            // Dövizli alt toplamlar sadece TRY harici döviz seçildiyse kaydedilsin
            $exchangeRate = ExchangeRate::find($request->input('exchange_rate_id'));
            $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
            if ($currencyCode !== 'TRY') {
                $params['total_price_fx'] = round((float)($request->input('total_price_fx') ?? 0), 8);
                $params['vat_amount_fx'] = round((float)($request->input('vat_amount_fx') ?? 0), 8);
                $params['total_amount_fx'] = round((float)($request->input('total_amount_fx') ?? 0), 8);
            } else {
                $params['total_price_fx'] = null;
                $params['vat_amount_fx'] = null;
                $params['total_amount_fx'] = null;
            }
            if (!is_null($unique)) {
                $order = Order::find($unique);
                $order->update($params);
            } else {
                $order = Order::create($params);
            }
            if ($order && $order->id) {
                OrderProduct::where('order_id', $order->id)->delete();
                if (!empty($products)) {
                    $order->processOrderProducts($products);
                }
            }
            
            // Tüm işlemler başarılı, transaction'ı commit et
            DB::commit();
            Cache::flush();
            
            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => 'Sipariş başarılı şekilde kaydedildi',
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', 'Sipariş başarılı şekilde kaydedildi');
        } catch (\Exception $e) {
            // Hata durumunda transaction'ı rollback et
            DB::rollback();
            
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Sipariş kaydedilirken bir hata oluştu: ' . $e->getMessage()
                ])
                : redirect()->back()->with('error', 'Sipariş kaydedilirken bir hata oluştu: ' . $e->getMessage())->withInput();
        }
    }
    public function detail(Request $request, $unique = null)
    {
        try {
            $order = Order::with([
                // 'branch',
                'warehouse',
                'exchangeRate',
                'current',
                'paymentType',
                'orderType',
                'orderProducts.stock.product.unit',
                'orderProducts.stock.variant'
            ])->findOrFail($unique ?? $request->input('id'));

            // Faturaya aktar işlemi
            if ($request->has('transfer_to_invoice')) {
                return $this->handleTransferToInvoice($order);
            }

            $breadcrumb = [
                'Ayarlar' => '#',
                'Alınan Sipariş' => route('backend.order_received_list'),
                'Alınan Sipariş Detay' => route('backend.order_received_detail', ['unique' => $unique])
            ];
            view()->share('breadcrumb', $breadcrumb);
            return view('backend.order_received.detail', [
                'container' => (object)[
                    'page' => $this->page,
                    'title' => 'Alınan Sipariş'
                ],
                'title' => 'Alınan Sipariş Detay',
                'subTitle' => 'Alınan Sipariş Detay',
                'item' => $order
            ]);
        } catch (\Exception $e) {
            return redirect()->route('backend.order_received_list')->with('error', 'Sipariş detayı görüntülenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    private function handleTransferToInvoice($order)
    {
        try {
            DB::beginTransaction();

            // Onaylanmış ama henüz faturaya aktarılmamış ürünleri getir (status = 1)
            $orderProducts = OrderProduct::with(['order.current', 'order.exchangeRate', 'stock.product.unit', 'stock.variant'])
                ->where('order_id', $order->id)
                ->where('status', 1) // Sadece onaylanmış ürünler (henüz faturaya aktarılmamış)
                ->get();

            if ($orderProducts->isEmpty()) {
                DB::rollBack();

                // Daha önce faturaya aktarılmış ürün var mı kontrol et
                $transferredProducts = OrderProduct::where('order_id', $order->id)
                    ->where('status', 3) // Faturaya aktarılmış ürünler
                    ->exists();

                if ($transferredProducts) {
                    return redirect()->back()->with('error', 'Bu siparişte faturaya aktarılacak yeni onaylanmış ürün bulunamadı. Tüm onaylanmış ürünler daha önce faturaya aktarılmış.');
                } else {
                    return redirect()->back()->with('error', 'Bu siparişte onaylanmış ürün bulunamadı');
                }
            }

            // Orders tablosundaki değerleri doğrudan kullan
            $netAmount = $order->total_price;
            $taxAmount = $order->vat_amount;
            $totalAmount = $order->total_amount;

            // Satış faturası oluştur
            $invoice = Invoice::create([
                'invoice_no' => Invoice::generatePreviewInvoiceNumber(),
                'invoice_date' => Carbon::now(),
                'due_date' => Carbon::now(), // Boş bırak
                'current_id' => $order->current_id,
                'warehouse_id' => $order->warehouse_id,
                'invoice_type_id' => 2, // Satış faturası
                'exchange_rate_id' => $order->exchange_rate_id,
                'payment_type_id' => $order->payment_type_id,
                'shipping_address' => $order->shipping_address ?? null,
                'net_amount' => $netAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'net_amount_fx' => $order->total_price_fx ?? null,
                'tax_amount_fx' => $order->vat_amount_fx ?? null,
                'total_amount_fx' => $order->total_amount_fx ?? null,
                'invoice_status_id' => 1,
                'description' => null,
                'is_active' => 1,
                'created_by' => Auth::id(),
            ]);
            $current = Current::find($invoice->current_id);
            if ($current) {
                $currencyCode = $order->exchangeRate ? $order->exchangeRate->code : 'TRY';

                // Sadece TRY ise balances tablosuna ekle
                if ($currencyCode === 'TRY') {
                    $balance = $current->balance()->where('current_id', $current->id)->first();

                    if ($balance) {
                        // Eğer balance varsa, debit_balance'ı güncelle
                        $balance->increment('debit_balance', $invoice->total_amount);
                        $balance->save();
                    } else {
                        // Eğer balance yoksa, yeni bir kayıt oluştur
                        $balance = $current->balance()->create([
                            'debit_balance' => $invoice->total_amount, 'credit_balance' => 0,
                        ]);
                    }
                } else {
                    // TRY dışındaki dövizler için sadece balance_currencies tablosuna ekle
                    $balance = $current->balance()->where('current_id', $current->id)->first();

                    if (!$balance) {
                        // Balance yoksa yeni oluştur (debit_balance = 0)
                        $balance = $current->balance()->create([
                            'debit_balance' => 0, 'credit_balance' => 0,
                        ]);
                    }
                }

                // BalanceCurrency işlemleri (dövizli bakiye)
                if ($currencyCode === 'TRY') {
                    $amountFX = $invoice->total_amount;
                } else {
                    // TRY dışındaki dövizler için dövizli tutarı hesapla
                    $exchangeRate = $order->exchangeRate ? $order->exchangeRate->selling_rate : 1;
                    $amountFX = $exchangeRate > 0 ? $invoice->total_amount / $exchangeRate : $invoice->total_amount;
                }

                $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $currencyCode)->first();
                if ($balanceCurrency) {
                    $balanceCurrency->increment('debit_balance', $amountFX);
                    $balanceCurrency->save();
                } else {
                    $balance->balanceCurrencies()->create([
                        'currency_code' => $currencyCode,
                        'debit_balance' => $amountFX,
                        'credit_balance' => 0,
                        'current_id' => $current->id,
                    ]);
                }
            }
            $totalStockAmount = $orderProducts->sum('total_price');
            $currencyCode = $order->exchangeRate ? $order->exchangeRate->code : 'TRY';

            $stockMovement = StockMovement::create([
                'movement_no' => StockMovement::generateMovementNo(),
                'invoice_id' => $invoice->id,
                'current_id' => $order->current_id,
                'starter_id' => Auth::id(),
                'movement_date' => now(),
                'status_id' => 1,
                'stock_movement_reason_id' => 6,
                'stock_movement_type_id' => 2,
                'notes' => 'Fatura aktarımı sonucu otomatik oluşturuldu',
                'warehouse_id' => $order->warehouse_id,
                'location_id' => $order->location_id ?? null,
                'target_warehouse_id' => null,
                'target_location_id' => null,
                'total_amount' => $totalStockAmount,
                'currency_code' => $currencyCode,
            ]);

            // Fatura kalemlerini oluştur ve sipariş ürünlerinin durumunu güncelle
            foreach ($orderProducts as $orderProduct) {
                // Ürün adı ve varyant bilgilerini hazırla
                $productDisplayName = '';
                if ($orderProduct->stock && $orderProduct->stock->product) {
                    $productDisplayName = $orderProduct->stock->product->name;
                    if ($orderProduct->stock->variant) {
                        $productDisplayName .= ' / ' . $orderProduct->stock->variant->name;
                    }
                }

                // Varyant ID'sini al
                $variantId = null;
                if ($orderProduct->stock && $orderProduct->stock->variant) {
                    $variantId = $orderProduct->stock->variant->id;
                }

                // Unit ID'sini al
                $unitId = null;
                if ($orderProduct->stock && $orderProduct->stock->product && $orderProduct->stock->product->unit) {
                    $unitId = $orderProduct->stock->product->unit->id;
                }
                $itemNo = InvoiceItem::generateItemNo($invoice->invoice_no);

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_no' => $itemNo,
                    'stock_id' => $orderProduct->stock_id,
                    'product_id' => $orderProduct->stock ? $orderProduct->stock->product_id : 0,
                    'product_variant_id' => $variantId,
                    'quantity' => $orderProduct->quantity,
                    'unit_price' => $orderProduct->unit_price,
                    'vat_rate' => $orderProduct->vat_rate,
                    'vat_status' => $orderProduct->vat_status,
                    'total_price' => $orderProduct->total_price,
                    'currency_type' => $orderProduct->currency_type,
                    'exchange_rate' => $orderProduct->exchange_rate,
                    'is_active' => 1,
                    'created_by' => Auth::id(),
                ]);

                // Sipariş ürününün durumunu "Faturaya Aktarıldı" olarak güncelle
                $orderProduct->status = 3; // 3: Faturaya Aktarıldı
                $orderProduct->save();

                StockMovementItem::create([
                    'stock_movement_id' => $stockMovement->id,
                    'stock_id' => $orderProduct->stock_id,
                    'stock_batch_id' => null,
                    'product_id' => $orderProduct->stock ? $orderProduct->stock->product_id : null,
                    'variant_id' => $variantId,
                    'unit_id' => $unitId,
                    'base_quantity' => $orderProduct->quantity,
                    'quantity' => $orderProduct->quantity,
                    'unit_price' => $orderProduct->unit_price,
                    'total_price' => $orderProduct->quantity * $orderProduct->unit_price,
                    'currency_code' => $currencyCode,
                    'status_id' => 1,
                ]);
            }



            DB::commit();

            $transferredCount = $orderProducts->count();
            return redirect()->back()->with('success', "{$transferredCount} adet onaylanmış ürün başarıyla satış faturasına aktarıldı. Fatura No: " . $invoice->invoice_no);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Faturaya aktarım sırasında bir hata oluştu: ' . $e->getMessage());
        }
    }

    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);

            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');

            if ($item && $item->id) {
                $item->orderProducts = OrderProduct::where('order_id', $item->id)
                    ->where('is_active', 1)
                    ->get();
                foreach ($item->orderProducts as $product) {
                    if (empty($product->item_no)) {
                        $product->item_no = OrderProduct::generateItemNo($item->order_number);
                        $product->save();
                    }
                }
                if ($item->branch_id) {
                    view()->share('branchWarehouses', Warehouse::where('branch_id', $item->branch_id)->get());
                }
                if ($item->current_id) {
                    $currentList = Current::where('is_active', 1)->get();
                    $deletedCurrent = Current::withTrashed()->where('id', $item->current_id)->first();

                    if ($deletedCurrent && $deletedCurrent->deleted_at) {
                        $currentWithDeleted = $currentList->toBase();
                        $currentWithDeleted->push($deletedCurrent);
                        view()->share('current', $currentWithDeleted);
                    }
                }
            }
        } else {
            $item = new $this->model;
            $item->order_number = Order::generateOrderNumber(1);
            $sampleItemNos = [];
            for ($i = 1; $i <= 20; $i++) { // 20 örnek item_no oluştur
                $sampleItemNos[] = $item->order_number . '-' . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
            view()->share('sampleItemNos', $sampleItemNos);
        }

        // Mevcut ürünleri partial format'ına çevir
        $products = [];
        if (isset($item->orderProducts) && $item->orderProducts->count() > 0) {
            $products = $item->orderProducts->map(function($product) {
                return (object) [
                    'stock_id' => $product->stock_id,
                    'item_no' => $product->item_no,
                    'product_code' => $product->stock->product->sku ?? '',
                    'product_name' => $product->stock->product->name . ($product->stock->variant ? ' / ' . $product->stock->variant->name : ''),
                    'unit' => $product->stock->product->unit->name ?? '',
                    'quantity' => $product->quantity,
                    'unit_price' => $product->unit_price,
                    'vat_status' => $product->vat_status,
                    'vat_rate' => $product->vat_rate,
                    'exchange_rate_id' => $product->exchange_rate_id,
                    'exchange_rate' => $product->exchange_rate,
                    'total_price' => $product->total_price,
                    'vat_amount' => $product->vat_amount,
                    'total_amount' => $product->total_amount,
                ];
            })->toArray();
        }

        return view("backend.$this->page.form", compact('item', 'products'));
    }
    protected function datatableHook($datatable)
    {
        return $datatable
            ->addColumn('currency_type', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->currency_code;
                }
                return 'TRY'; // Varsayılan değer
            })
            ->addColumn('currency_symbol', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->symbol;
                }
                return '₺'; // Varsayılan değer (Türk Lirası)
            })
            ->addColumn('product_statuses', function ($item) {
                $statuses = [];
                if ($item->orderProducts && $item->orderProducts->count() > 0) {
                    foreach ($item->orderProducts as $product) {
                        $statuses[] = [
                            'status' => $product->status,
                            'quantity' => $product->quantity
                        ];
                    }
                }
                return $statuses;
            })
            ->addColumn('current_info', function ($item) {
                if ($item->current) {
                    return [
                        'name' => $item->current->title,
                        'deleted' => $item->current->deleted_at ? true : false,
                    ];
                }

                return [
                    'name' => '-',
                    'deleted' => false,
                ];
            })
            ->editColumn('total_amount', function ($item) {
                return formatMoney($item->total_amount);
            });
    }
    public function updateStatus(Request $request)
    {
        $productIds = $request->input('product_ids', []);
        $newStatus = $request->input('status');
        $reasonForCancellation = $request->input('reason_for_cancellation');

        if (empty($productIds) || is_null($newStatus)) {
            return response()->json([
                'success' => false,
                'message' => "Ürün ID'leri veya durum bilgisi eksik"
            ]);
        }

        // İptal durumu seçildiğinde validasyon
        if ($newStatus == 2) {
            $trimmedReason = trim($reasonForCancellation);
            $length = mb_strlen($trimmedReason);

            if (empty($trimmedReason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'İptal sebebi zorunludur.'
                ]);
            }

            if ($length < 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lütfen iptal sebebi en az 5 karakter olmalıdır.'
                ]);
            }

            if ($length > 500) {
                return response()->json([
                    'success' => false,
                    'message' => 'İptal sebebi 500 karakterden uzun olamaz.'
                ]);
            }
        }

        try {
            DB::beginTransaction();

            $orderProducts = OrderProduct::with(['stock', 'stockReservations', 'order']) // stockReservations ilişkisini ekledik
                ->whereIn('id', $productIds)
                ->get();

            foreach ($orderProducts as $orderProduct) {
                $oldStatus = $orderProduct->status;

                if ($oldStatus === (int) $newStatus) {
                    continue;
                }

                $orderProduct->status = $newStatus;

                // İptal durumu seçildiğinde sebebi kaydet
                if ($newStatus == 2) {
                    $orderProduct->reason_for_cancellation = $reasonForCancellation;
                } else {
                    $orderProduct->reason_for_cancellation = null;
                }

                $orderProduct->save();

                $stock = $orderProduct->stock;
                $currentId = $orderProduct->order?->current_id;

                if (!$stock) {
                    continue;
                }
                if ((int) $newStatus === 1) {
                    StockReservation::create([
                        'stock_id'              => $stock->id,
                        'order_id'              => $orderProduct->order_id,
                        'product_id'            => $stock->product_id,
                        'variant_id'            => $stock->variant_id,
                        'reservation_reason_id' => 1, // sabit değer
                        'reservation_type_id'   => 1, // sabit değer
                        'current_id'            => $currentId,
                        'priority_level'        => 5,
                        'quantity'              => $orderProduct->quantity,
                        'start_date'            => now(),
                        'is_active'             => 1,
                    ]);
                }
                if (in_array((int) $newStatus, [0, 2])) {
                    StockReservation::where('order_id', $orderProduct->order_id)
                        ->where('product_id', $stock->product_id)
                        ->where('variant_id', $stock->variant_id)
                        ->whereNull('deleted_at') // zaten silinmemiş olan
                        ->delete(); // soft delete çalışır (modelde SoftDeletes trait varsa)
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Ürün durumları başarıyla güncellendi'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Ürün durumları güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }
    public function calculateTotals(Request $request)
    {
        try {
            $productsData = $request->input('products', []);
            $mainExchangeRateId = $request->input('exchange_rate_id');

            // Gerekli tüm döviz kurlarını tek seferde çekelim
            $productExchangeRateIds = array_column($productsData, 'exchange_rate_id');
            $allRateIds = array_unique(array_merge([$mainExchangeRateId], $productExchangeRateIds));
            $exchangeRates = ExchangeRate::whereIn('id', $allRateIds)->get()->keyBy('id');

            // Ana faturanın döviz kurunu alalım
            $mainExchangeRate = $exchangeRates->get($mainExchangeRateId);
            $mainCurrencySymbol = $mainExchangeRate ? $mainExchangeRate->symbol : '₺';
            $mainSellingRate = $mainExchangeRate ? $mainExchangeRate->selling_rate : 1;

            $grandTotalNetTry = 0;
            $grandTotalVatTry = 0;
            $grandTotalWithVatTry = 0;
            $productRows = [];

            // Her ürün için hesaplama yap
            foreach ($productsData as $stockId => $product) {
                $quantity = (int)($product['quantity'] ?? 1);
                $unitPrice = (float)parseTrNumber($product['price'] ?? 0);
                $vatRate = (float)($product['vat_rate'] ?? 0);
                $vatStatus = (int)($product['vat_status'] ?? 0); // 0: Hariç, 1: Dahil
                $rowExchangeRateId = $product['exchange_rate_id'] ?? $mainExchangeRateId;

                // Ürün satırının döviz kurunu al
                $rowExchangeRate = $exchangeRates->get($rowExchangeRateId);
                $rowSellingRate = $rowExchangeRate ? $rowExchangeRate->selling_rate : 1;

                // Tutar ve KDV'yi ürünün kendi para biriminde hesapla
                $lineNet = round($quantity * $unitPrice, 8);
                $lineVat = 0;
                if ($vatStatus === 1) { // KDV Dahil
                    $lineVat = round($lineNet - ($lineNet / (1 + ($vatRate / 100))), 8);
                    $lineNet = round($lineNet - $lineVat, 8); // Net tutarı KDV'den arındır
                } else { // KDV Hariç
                    $lineVat = round($lineNet * ($vatRate / 100), 8);
                }
                $lineWithVat = round($lineNet + $lineVat, 8);

                // Her satırın tutarını TL'ye çevir
                $lineTotalTry = round($lineWithVat * $rowSellingRate, 8);

                // Genel toplamlara TL tutarları ekle
                $grandTotalNetTry = round($grandTotalNetTry + ($lineNet * $rowSellingRate), 8);
                $grandTotalVatTry = round($grandTotalVatTry + ($lineVat * $rowSellingRate), 8);
                $grandTotalWithVatTry = round($grandTotalWithVatTry + $lineTotalTry, 8);

                // Frontend'deki satırları güncellemek için verileri hazırla
                $productRows[$stockId] = [
                    // Görünen tutar inputu için TL karşılığını gönder
                    'total_amount' => formatMoney($lineTotalTry),

                    // Kaydetmek için gizli inputlara orijinal dövizdeki tutarları gönder
                    'amount_raw' => $lineNet,
                    'vat_amount_raw' => $lineVat,
                    'total_amount_raw' => $lineWithVat,
                ];
            }

            // Genel toplamların ana faturanın para birimine göre karşılığını hesapla
            $grandTotalNetFx = $mainSellingRate > 0 ? round($grandTotalNetTry / $mainSellingRate, 8) : 0;
            $grandTotalVatFx = $mainSellingRate > 0 ? round($grandTotalVatTry / $mainSellingRate, 8) : 0;
            $grandTotalWithVatFx = $mainSellingRate > 0 ? round($grandTotalWithVatTry / $mainSellingRate, 8) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_price' => formatMoney($grandTotalNetTry),
                    'vat_amount' => formatMoney($grandTotalVatTry),
                    'total_amount' => formatMoney($grandTotalWithVatTry),
                    'total_price_fx' => formatMoney($grandTotalNetFx),
                    'vat_amount_fx' => formatMoney($grandTotalVatFx),
                    'total_amount_fx' => formatMoney($grandTotalWithVatFx),
                    'currency_symbol' => $mainCurrencySymbol,
                    'product_rows' => $productRows,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Hesaplama sırasında bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    public function delete(Request $request)
    {
        $order = $this->model::find((int)$request->post('id'));

        if (!$order) {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı',
            ]);
        }

        // Faturaya aktarılmış ürün var mı kontrol et
        $hasTransferredProducts = OrderProduct::where('order_id', $order->id)
            ->where('status', 3)
            ->exists();

        if ($hasTransferredProducts) {
            return response()->json([
                'status' => false,
                'message' => 'Onaylanmış veya faturaya aktarılmış ürün içeren sipariş silinemez.',
            ]);
        }

        try {
            $order->delete(); // boot() içindeki deleting çalışır

            Cache::flush(); // Daha iyisi: Cache::forget("order_{$order->id}");

            return response()->json([
                'status' => true,
                'message' => 'Sipariş başarıyla silindi',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
