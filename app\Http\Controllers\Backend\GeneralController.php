<?php
namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\ExchangeRate;
use App\Models\Stock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class GeneralController extends Controller
{

    public function index(Request $request)
    {

        return view('backend.index');
    }

    public function cacheall()
    {
        Cache::flush();
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        return response()->json(['status' => true]);
    }



    public function stockList(Request $request)
    {

        if ($request->has('datatable')) {
            $select = Stock::active()->with(['product.category','product.unit', 'variant', 'stockReservations']);
            
            // Warehouse filtrelemesi
            if ($request->has('warehouse_id') && !empty($request->warehouse_id)) {
                $select->where('warehouse_id', $request->warehouse_id);
            }
            
            $select = $select->get();
            $obj = datatables()->of($select);
            $obj = $obj
                ->editColumn('category', function ($item) {
                    return $item->product?->category?->name ?? '-';
                })
                ->editColumn('stock_code', function ($item) {
                    return $item->product?->sku ?? '-';
                })
                ->editColumn('product_name', function ($item) {
                    return $item->product?->name ?? '-';
                })
                ->editColumn('variant_name', function ($item) {
                    return $item->variant?->name ?? '-';
                })
                ->editColumn('available_stock', function ($item) {
                    $totalStock = $item->quantity;
                    $salesReservations = 0;
                    $purchaseReservations = 0;
                    
                    foreach ($item->stockReservations as $reservation) {
                        // StockController'daki mantık: is_active = 1 ve (end_date null veya >= now())
                        if ($reservation->is_active == 1) {
                            if (!$reservation->end_date || $reservation->end_date >= now()) {
                                if ($reservation->reservation_type_id == 1) {
                                    $salesReservations += $reservation->quantity;
                                } elseif ($reservation->reservation_type_id == 2) {
                                    $purchaseReservations += $reservation->quantity;
                                }
                            }
                        }
                    }
                    
                    // StockController'daki calculateAvailableQuantity mantığı: totalStock - salesReservations + purchaseReservations
                    return $totalStock - $salesReservations + $purchaseReservations;
                });

            $obj = $obj->addIndexColumn()->make(true);  

            // Debug bilgisi ekle
            $response = response()->json($obj->original);
            $response->header('X-Debug-Warehouse-ID', $request->warehouse_id ?? 'null');
            
            return $response;
        }

        return view("backend.general_stock_list");
    }

    public function addSelectedProductsToOrder(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $orderNumber = $request->input('order_number', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate', 'stockReservations'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $orderNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->sale_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Stok hesaplama
            $totalStock = $stock->quantity;
            $salesReservations = 0;
            $purchaseReservations = 0;
            
            foreach ($stock->stockReservations as $reservation) {
                if ($reservation->is_active == 1) {
                    if (!$reservation->end_date || $reservation->end_date >= now()) {
                        if ($reservation->reservation_type_id == 1) {
                            $salesReservations += $reservation->quantity;
                        } elseif ($reservation->reservation_type_id == 2) {
                            $purchaseReservations += $reservation->quantity;
                        }
                    }
                }
            }
            
            $availableStock = $totalStock - $salesReservations + $purchaseReservations;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // TRY'ye çevrilmiş fiyat
                'original_unit_price' => $salePrice, // Orijinal fiyat
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice,
                'available_stock' => $availableStock
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.order_received.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }
    
    public function addSelectedProductsToInvoice(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $invoiceNumber = $request->input('invoice_no', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $invoiceNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->sale_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kurunu bul
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;

            // OrderProduct benzeri nesne oluştur
            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice,
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.invoices.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }

    public function addSelectedProductsToPurchaseInvoice(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $invoiceNumber = $request->input('invoice_no', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $invoiceNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->purchase_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kurunu bul
            $productSaleCurrencyCode = $stock->product->purchase_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;

            // OrderProduct benzeri nesne oluştur
            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice,
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.purchase_invoices.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }

    public function addSelectedProductsToOrderGiven (Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $orderNumber = $request->input('order_number', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $orderNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->sale_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // TRY'ye çevrilmiş fiyat
                'original_unit_price' => $salePrice, // Orijinal fiyat
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.order_given.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }

    public function addSelectedProductsToOffer(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $offerNumber = $request->input('offer_number', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $offerNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->sale_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // TRY'ye çevrilmiş fiyat
                'original_unit_price' => $salePrice, // Orijinal fiyat
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.offer_given.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }

    public function addSelectedProductsToOfferReceived(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $offerNumber = $request->input('offer_number', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant','product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $offerNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = $stock->product->sale_price ?? 0;
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // TRY'ye çevrilmiş fiyat
                'original_unit_price' => $salePrice, // Orijinal fiyat
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => $salePrice,
                'vat_amount' => 0,
                'total_amount' => $salePrice
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.offer_received.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }
    public function addSelectedProductsToReturnPurchaseInvoice(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $invoiceNumber = $request->input('invoice_no', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant', 'product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $invoiceNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = 0; // Fiyatı her zaman 0 olarak ayarla
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // Her zaman 0
                'original_unit_price' => $salePrice, // Her zaman 0
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => 0, // Her zaman 0
                'vat_amount' => 0,
                'total_amount' => 0 // Her zaman 0
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.return_purchase_invoice.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }
    public function addSelectedProductsToReturnSaleInvoice(Request $request)
    {
        $selectedIds = $request->input('selected_ids', []);
        $invoiceNumber = $request->input('invoice_no', '');
        $currentProductCount = $request->input('current_product_count', 0);
        $exchangeRateId = $request->input('exchange_rate_id');

        if (empty($selectedIds)) {
            return response()->json(['success' => false, 'message' => 'Ürün seçilmedi']);
        }

        $stocks = Stock::whereIn('id', $selectedIds)
            ->with(['product.category', 'product.unit', 'variant', 'product.category.vatRate'])
            ->get();

        // Döviz seçeneklerini hazırla (Sadece en güncel kurları al)
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        $exchangeRatesByCode = $exchangeRates->keyBy('code');

        $products = [];
        $productCount = $currentProductCount;

        foreach ($stocks as $stock) {
            $productCount++;
            $itemNo = $invoiceNumber . '-' . str_pad($productCount, 2, '0', STR_PAD_LEFT);
            $stockCode = $stock->variant ? $stock->variant->sku : $stock->product->sku;
            $productName = $stock->product->name;
            if ($stock->variant) {
                $productName .= ' / ' . $stock->variant->name;
            }
            $unit = $stock->product->unit->name ?? '-';
            $salePrice = 0; // Fiyatı her zaman 0 olarak ayarla
            $vatRate = $stock->product->category->vatRate->rate ?? 0;

            // Ürünün kendi satış döviz kodu ve fiyatı
            $productSaleCurrencyCode = $stock->product->sale_currency_code ?? 'TRY';
            $selectedExchangeRate = $exchangeRatesByCode->get($productSaleCurrencyCode);
            $selectedExchangeRateId = $selectedExchangeRate ? $selectedExchangeRate->id : null;
            $selectedExchangeRateValue = $selectedExchangeRate ? $selectedExchangeRate->selling_rate : 1;


            $product = (object) [
                'stock_id' => $stock->id,
                'item_no' => $itemNo,
                'product_code' => $stockCode,
                'product_name' => $productName,
                'unit' => $unit,
                'quantity' => 1,
                'unit_price' => $salePrice, // Her zaman 0
                'original_unit_price' => $salePrice, // Her zaman 0
                'original_currency_code' => $productSaleCurrencyCode, // Orijinal para birimi
                'vat_status' => 0, // Varsayılan hariç
                'vat_rate' => $vatRate,
                'exchange_rate_id' => $selectedExchangeRateId,
                'exchange_rate' => $selectedExchangeRateValue,
                'total_price' => 0, // Her zaman 0
                'vat_amount' => 0,
                'total_amount' => 0 // Her zaman 0
            ];

            $products[] = $product;
        }

        // Partial view ile render et (sadece tbody içeriği)
        $html = view('backend.return_sale_invoice.partials.product_rows', compact('products', 'exchangeRates') + ['rowsOnly' => true])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'product_count' => $productCount
        ]);
    }
}
