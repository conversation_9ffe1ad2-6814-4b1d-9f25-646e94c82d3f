@extends('layout.layout')
@php
$currencyCode = $item->exchangeRate ? $item->exchangeRate->code : 'TRY';
@endphp
@section('content')
<style>
    .status-row td {
        border-color: rgba(0, 0, 0, 0.1) !important;
    }

    .form-check-input {
        opacity: 1 !important;
        background-color: #fff !important;
        border-color: #aaa !important;
    }

    tr[style] td {
        background-color: inherit !important;
        color: inherit !important;
    }

    .info-card {
        height: 100%;
    }

    .address-wrapper {
        max-width: 100%;
        word-wrap: break-word;
        display: inline;
    }
</style>
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fs-6">
                    {{ $subTitle }}
                </h5>
                <a href="{{ route('backend.' . $container->page . '_list') }}"
                    class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                    <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                </a>
            </div>
            <div class="card-body">
                <!-- Fatura Bilgileri -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class=" card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">İrsaliye Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>İrsaliye No:</strong> {{ $item->waybill_no }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>İrsaliye Tarihi:</strong> {{ $item->waybill_date ?
                                        \Carbon\Carbon::parse($item->waybill_date)->format('d.m.Y') : '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Depo:</strong> 
                                        @if(isset($item->warehouse) && $item->warehouse)
                                            {{ $item->warehouse->name }}
                                        @elseif(isset($item->invoice) && $item->invoice && isset($item->invoice->warehouse) && $item->invoice->warehouse)
                                            {{ $item->invoice->warehouse->name }}
                                        @else
                                            -
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Fatura No:</strong> {{ $item->invoice->invoice_no ?? '-' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Cari Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Cari Adı:</strong> 
                                        @php
                                            $cari = $item->current ?? ($item->invoice->current ?? null);
                                        @endphp
                                        {{ $cari->title ?? '-' }}
                                        @if($cari && $cari->deleted_at)
                                            <span class="badge bg-danger">Silinmiş</span>
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Telefon:</strong> {{ $cari->phone ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Adres:</strong>
                                        <span class="address-wrapper">
                                            @if(!empty($item->shipping_address))
                                                {{ $item->shipping_address }}
                                            @else
                                                {{ $cari->address ?? '-' }}
                                                {{ $cari->city->name ?? '-' }}
                                                {{ $cari->country->name ?? '-' }}
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        @if($item->waybill_statu_id == 3 && $item->reason_for_cancellation)
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">İptal Sebebi</h6>
                                <p class="mb-0">{{ $item->reason_for_cancellation }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <h6 class="mb-3">İrsaliye Kalemleri</h6>
                        <div class="table-responsive">
                            <table class="table vertical-striped-table mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-center">Sıra No</th>
                                        <th class="text-center">Ürün Kodu</th>
                                        <th class="text-center">Ürün Adı</th>
                                        <th class="text-center">Miktar</th>
                                        <th class="text-center">Birim</th>
                                        <th class="text-center">Birim Fiyat</th>
                                        <th class="text-center">KDV</th>
                                        <th class="text-center">Toplam</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(($item->waybillItems ?? []) as $waybillItem)
                                    @php
                                    $rowStyle = '';
                                    switch($item->waybill_statu_id) {
                                    case 1: // Beklemede
                                    $rowStyle = 'background-color: #FFC02D6B !important; color: #000 !important;';
                                    break;
                                    case 2: // Onaylandı
                                    $rowStyle = 'background-color: #45b36966 !important; color: #000 !important;';
                                    break;
                                    case 3: // Reddedildi
                                    $rowStyle = 'background-color: #ef47704a !important; color: #000 !important;';
                                    break;
                                    }
                                    @endphp
                                    <tr class="status-row">
                                        <td class="text-center" style="{{ $rowStyle }}">{{ $loop->iteration }}</td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($waybillItem->stock && $waybillItem->stock->variant)
                                            {{ $waybillItem->stock->variant->sku }}
                                            @elseif($waybillItem->stock && $waybillItem->stock->product)
                                            {{ $waybillItem->stock->product->sku ?? '-' }}
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($waybillItem->stock && $waybillItem->stock->product)
                                            {{ $waybillItem->stock->product->name }}
                                            @if($waybillItem->stock->variant)
                                            / {{ $waybillItem->stock->variant->name }}
                                            @endif
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">{{ $waybillItem->quantity }}
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($waybillItem->stock && $waybillItem->stock->product &&
                                            $waybillItem->stock->product->unit)
                                            {{ $waybillItem->stock->product->unit->name }}
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">{{ number_format($waybillItem->price, 2, ',', '.') }}</td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            %{{ $waybillItem->vat_rate }} ({{ $waybillItem->vat_status == 0 ? 'Hariç' :
                                            'Dahil' }})
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">{{ number_format($waybillItem->total, 2, ',', '.') }} </td>
                                    </tr>
                                    @if($waybillItem->status == 2 && $waybillItem->reason_for_cancellation)
                                    <tr>
                                        <td colspan="9" class="text-start">
                                            <strong>İptal Sebebi:</strong> {{ $product->reason_for_cancellation }}
                                        </td>
                                    </tr>
                                    @endif
                                    @endforeach
                                </tbody>
                                <tbody>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>Ara Toplam:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->net_amount, 2, ',', '.') }} </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>KDV:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->tax_amount, 2, ',', '.') }} </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>Genel Toplam:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->total_amount, 2, ',', '.') }} </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @if(isset($transactions) && count($transactions) > 0)
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6 class="mb-3">Tahsilat Geçmişi</h6>
                        <div class="table-responsive">
                            <table class="table vertical-striped-table mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tarih</th>
                                        <th>Tutar</th>
                                        <th>Para Birimi</th>
                                        <th>Ödeme Yöntemi</th>
                                        <th>Açıklama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($transactions as $transaction)
                                    @php
                                    $currencySymbol = $transaction->exchangeRate ? $transaction->exchangeRate->symbol :
                                    '₺';
                                    $paymentMethod = $transaction->paymentType ? $transaction->paymentType->name : '-';
                                    @endphp
                                    <tr>
                                        <td>{{ $transaction->transaction_date ?
                                            $transaction->transaction_date->format('d.m.Y H:i') :
                                            '-' }}</td>
                                        <td>{{ number_format($transaction->amount, 2, ',', '.') }} {{ $currencySymbol }}
                                        </td>
                                        <td>{{ $transaction->exchangeRate ? $transaction->exchangeRate->code : 'TRY' }}
                                        </td>
                                        <td>{{ $paymentMethod }}</td>
                                        <td>{{ $transaction->description ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif
                <div class="text-end mt-3">
                    <button type="button" class="btn btn-primary" id="updateStatusBtn"
                        @if(in_array($item->waybill_statu_id, [2,4,5])) disabled @endif>
                        Durumunu Güncelle
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusUpdateModalLabel">Durum Güncelleme</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="statusSelect" class="form-label">Yeni Durum</label>
                    <div class="d-flex flex-column gap-2">
                        @foreach($waybillStatu as $status)
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="status" id="status_{{ $status->id }}"
                                value="{{ $status->id }}">
                            <label class="form-check-label" for="status_{{ $status->id }}">
                                <span class="
                                        @if($status->id == 1) bg-warning-focus text-warning-600 border border-warning-main
                                        @elseif($status->id == 2) bg-success-focus text-success-600 border border-success-main
                                        @elseif($status->id == 3) bg-danger-focus text-danger-600 border border-danger-main
                                        @endif
                                        px-24 py-4 radius-4 fw-medium text-sm">
                                    {{ $status->name }}
                                </span>
                            </label>
                        </div>
                        @endforeach
                    </div>
                </div>
                <div class="mb-3" id="cancellationReasonDiv" style="display: none;">
                    <label for="reasonForCancellation" class="form-label">İptal Sebebi</label>
                    <textarea class="form-control" id="reasonForCancellation" name="reason_for_cancellation" rows="3"
                        placeholder="İptal sebebini yazınız"></textarea>
                    <div id="cancellationError" class="text-danger mt-1"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" id="saveStatusBtn">Kaydet</button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Durum Güncelle butonuna tıklanınca modalı aç
        document.getElementById('updateStatusBtn').addEventListener('click', function () {
            // Eğer buton disabled ise modal açılmasın
            if (this.hasAttribute('disabled')) return;
            var statusModal = new bootstrap.Modal(document.getElementById('statusUpdateModal'));
            statusModal.show();
        });

        // Modalda: İptal Edildi seçilirse iptal sebebi alanı göster/gizle
        const statusRadios = document.querySelectorAll('input[name="status"]');
        const cancellationDiv = document.getElementById('cancellationReasonDiv');
        const cancellationTextarea = document.getElementById('reasonForCancellation');
        statusRadios.forEach(function(radio) {
            radio.addEventListener('change', function() {
                if (parseInt(this.value) === 3) {
                    cancellationDiv.style.display = 'block';
                    cancellationTextarea.setAttribute('required', 'required');
                } else {
                    cancellationDiv.style.display = 'none';
                    cancellationTextarea.removeAttribute('required');
                    cancellationTextarea.value = '';
                }
            });
        });

        // Kaydet butonuna tıklanınca AJAX ile durum güncelle
        document.getElementById('saveStatusBtn').addEventListener('click', function () {
            const selectedStatus = document.querySelector('input[name="status"]:checked');
            const errorDiv = document.getElementById('cancellationError');
            errorDiv.textContent = '';

            if (!selectedStatus) {
                alert('Lütfen bir durum seçiniz.');
                return;
            }
            const statusId = parseInt(selectedStatus.value);
            let reasonForCancellation = '';
            if (statusId === 3) {
                reasonForCancellation = cancellationTextarea.value.trim();
                if (!reasonForCancellation) {
                    errorDiv.textContent = 'İptal sebebi zorunludur.';
                    return;
                }
                if (reasonForCancellation.length < 5) {
                    errorDiv.textContent = 'Lütfen iptal sebebi en az 5 karakter olmalıdır.';
                    return;
                }
                if (reasonForCancellation.length > 500) {
                    errorDiv.textContent = 'İptal sebebi 500 karakterden uzun olamaz.';
                    return;
                }
            }
            fetch('{{ route("backend.sale_waybill_update_status") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    id: {{ $item->id }},
                    status_id: statusId,
                    reason_for_cancellation: reasonForCancellation
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    //alert(data.message || 'Durum başarıyla güncellendi.');
                    location.reload();
                } else {
                    if (statusId === 3) {
                        errorDiv.textContent = data.message || 'Durum güncellenemedi.';
                    } else {
                        //alert(data.message || 'Durum güncellenemedi.');
                    }
                }
            })
            .catch(error => {
                console.error('Hata:', error);
                //alert('Durum güncellenirken bir hata oluştu.');
            });
        });
    });
</script>
@endsection