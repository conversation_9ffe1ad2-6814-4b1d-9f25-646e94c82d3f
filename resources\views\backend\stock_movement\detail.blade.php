@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = 'Hareket No: ' . ($item->movement_no ?? 'SM-' . str_pad($item->id, 6, '0', STR_PAD_LEFT));
@endphp

@section('content')
    <div class="row g-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Hareket Bilgileri</h5>
                    <div class="d-flex gap-2">
                        @if($item->status_id == 1)
                            <span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Beklemede</span>
                        @elseif($item->status_id == 2)
                            <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span>
                        @else
                            <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">İptal</span>
                        @endif
                        @if($item->status_id != 2)
                            <a href="{{ route('backend.stock_movement_form', $item->id) }}" class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                                Düzenle
                            </a>
                        @endif
                        <a href="{{ route('backend.stock_movement_list') }}" class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                            Geri
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table basic-table mb-0">
                                <tr>
                                    <td width="40%"><strong>Hareket No:</strong></td>
                                    <td>{{ $item->movement_no ?? 'SM-' . str_pad($item->id, 6, '0', STR_PAD_LEFT) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Hareket Tarihi:</strong></td>
                                    <td>{{ $item->movement_date ? $item->movement_date->format('d.m.Y H:i') : '-' }}</td>
                                </tr>
                                <tr>
                                    <td width="40%"><strong>Hareket Tipi:</strong></td>
                                    <td>{{ $item->stockMovementReason ? $item->stockMovementReason->name : '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table basic-table mb-0">
                                <tr>
                                    <td width="40%"><strong>Başlatan:</strong></td>
                                    <td>{{ $item->starter ? $item->starter->fullname : '-' }}</td>
                                </tr>
                                <tr>
                                    <td width="40%"><strong>Onaylayan:</strong></td>
                                    <td>{{ $item->approver ? $item->approver->fullname : '-' }}</td>
                                </tr>
                                <tr>
                                    <td width="40%"><strong>Onay Tarihi:</strong></td>
                                    <td>{{ $item->approval_date ? $item->approval_date->format('d.m.Y H:i') : '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Depo/Lokasyon Bilgileri</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @if($item->stockMovementReason && in_array($item->stockMovementReason->movement_type_id, [2, 3]))
                            <div class="col-md-6">
                                <div class="bg-neutral-50 p-24 radius-8 border border-neutral-200">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="w-40 h-40 bg-warning-100 text-warning-600 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="ri-home-line"></i>
                                        </div>
                                        <h6 class="mb-0 fw-semibold text-neutral-700">Kaynak</h6>
                                    </div>
                                    <div class="ms-52">
                                        <p class="mb-2 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Depo:</span>
                                            <span class="text-neutral-800">{{ $item->warehouse ? $item->warehouse->name : '-' }}</span>
                                        </p>
                                        <p class="mb-0 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Lokasyon:</span>
                                            <span class="text-neutral-800">{{ $item->location ? $item->location->name : 'Ana Lokasyon' }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($item->stockMovementReason && $item->stockMovementReason->movement_type_id == 3)
                            <div class="col-md-6">
                                <div class="bg-neutral-50 p-24 radius-8 border border-neutral-200">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="w-40 h-40 bg-success-100 text-success-600 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="ri-building-line"></i>
                                        </div>
                                        <h6 class="mb-0 fw-semibold text-neutral-700">Hedef</h6>
                                    </div>
                                    <div class="ms-52">
                                        <p class="mb-2 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Depo:</span>
                                            <span class="text-neutral-800">{{ $item->targetWarehouse ? $item->targetWarehouse->name : '-' }}</span>
                                        </p>
                                        <p class="mb-0 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Lokasyon:</span>
                                            <span class="text-neutral-800">{{ $item->targetLocation ? $item->targetLocation->name : 'Ana Lokasyon' }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @elseif($item->stockMovementReason && $item->stockMovementReason->movement_type_id == 1)
                            <div class="col-md-6">
                                <div class="bg-neutral-50 p-24 radius-8 border border-neutral-200">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="w-40 h-40 bg-success-100 text-success-600 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="ri-building-line"></i>
                                        </div>
                                        <h6 class="mb-0 fw-semibold text-neutral-700">Hedef</h6>
                                    </div>
                                    <div class="ms-52">
                                        <p class="mb-2 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Depo:</span>
                                            <span class="text-neutral-800">{{ $item->warehouse ? $item->warehouse->name : '-' }}</span>
                                        </p>
                                        <p class="mb-0 text-neutral-600">
                                            <span class="fw-medium text-neutral-700">Lokasyon:</span>
                                            <span class="text-neutral-800">{{ $item->location ? $item->location->name : 'Ana Lokasyon' }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card basic-data-table">
                <div class="card-header">
                    <h5 class="card-title mb-0">Hareket Detayları</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table datatable class="table bordered-table mb-0" id="itemsTable" data-page-length='10'>
                            <thead>
                                <tr>
                                    <th>Ürün</th>
                                    <th>Varyant</th>
                                    <th>Miktar</th>
                                    <th>Birim Fiyat</th>
                                    <th>Toplam</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    @if($item->notes)
                        <div class="mt-3">
                            <label class="form-label text-muted">Notlar</label>
                            <p class="fw-semibold">{{ $item->notes }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>


    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "#itemsTable";
            var itemsTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=true&type=items",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'product_name',
                        name: 'product_name',
                        className: 'text-center',
                        defaultContent: '-'
                    },
                    {
                        data: 'variant_name',
                        name: 'variant_name',
                        className: 'text-center',
                        defaultContent: '-'
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center'
                    },
                    {
                        data: 'unit_price',
                        name: 'unit_price',
                        className: 'text-center'
                    },
                    {
                        data: 'total_price',
                        name: 'total_price',
                        className: 'text-center'
                    }
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });



            setTimeout(function() {
                $('#itemsTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    itemsTable.search(this.value).draw();
                });


            }, 300);
        });
    </script>
@endsection
