<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportInvoices;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\InvoiceItem;
use App\Models\InvoiceStatus;
use App\Models\InvoiceType;
use App\Models\PaymentTerm;
use App\Models\PaymentType;
use App\Models\Warehouse;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Models\StockMovement;
use App\Models\StockMovementItem;
use App\Models\Stock;
use App\Models\StockReservation;
use App\Models\Transaction;
use App\Models\Unit;
use Illuminate\Support\Facades\DB;

class InvoicesController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Satış Faturaları';
        $this->page = 'invoices';
        $this->upload = 'invoices';
        $this->model = new Invoice();
        $this->relation = ['current', 'invoiceItems', 'invoiceStatus', 'exchangeRate', 'warehouse'];
        $this->listQuery = Invoice::where('invoice_type_id', 2);

        $this->view = (object)[
            'breadcrumb' => [
                'Satış Faturaları' => route('backend.invoices_list'),
            ],
        ];
        $this->validation = [
            [
                'invoice_date' => 'required|date|after_or_equal:today',
                'due_date' => 'required|date|after_or_equal:invoice_date',
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'exchange_rate_id' => 'required|integer|exists:exchange_rates,id',
                'current_id' => 'required|exists:currents,id',
                'payment_type_id'  => 'required|integer|exists:payment_types,id',
                'payment_term_id'  => 'nullable|integer|exists:payment_terms,id',
                'description' => 'nullable|string',
                'products'          => 'required|array|min:1',
            ],
            [
                'warehouse_id.required' => 'Lütfen depo seçiniz',
                'invoice_date.required' => 'Fatura tarihi zorunludur.',
                'invoice_date.date' => 'Fatura tarihi geçerli bir tarih olmalıdır.',
                'invoice_date.after_or_equal' => 'Fatura tarihi bugünden sonra olmalıdır',
                'due_date.required' => 'Vade tarihi zorunludur.',
                'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
                'due_date.after_or_equal' => 'Vade tarihi fatura tarihinden önce olamaz.',
                'current_id.required' => 'Cari hesap seçimi zorunludur.',
                'exchange_rate_id.exists' => 'Seçilen döviz kuru geçerli değil.',
                'exchange_rate_id.required' => 'Lütfen döviz kuru seçiniz',
                'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
                'payment_type_id.required' => 'Lütfen ödeme planı seçiniz',
                'payment_term_id.exists' => 'Seçilen ödeme şartı geçerli değil.',
                'products.required'         => 'Lütfen en az bir ürün ekleyiniz.',
                'products.array'            => 'Ürünler hatalı gönderildi.',
                'products.min'              => 'Lütfen en az bir ürün ekleyiniz.',
            ]
        ];

        view()->share('current', Current::whereIn('current_type_id', [1, 3])->where('is_active', 1)->get());
        view()->share('invoiceType', InvoiceType::where('is_active', 1)->get());
        // En son kur kayıtlarını al
        $latestExchangeRates = ExchangeRate::where('is_active', 1)
            ->select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        view()->share('exchangeRate', $latestExchangeRates);
        view()->share('paymentType', PaymentType::get());
        view()->share('paymentTerms', PaymentTerm::where('is_active', 1)->get());
        view()->share('invoiceItems', InvoiceItem::where('is_active', 1)->get());
        view()->share('invoiceStatus', InvoiceStatus::where('is_active', 1)->get());
        view()->share('products', Product::with(['unit', 'unitType', 'variants'])->where('is_active', 1)->get());
        view()->share('stocks', Stock::with(['product.category', 'variant', 'stockReservations', 'product.category.vatRate'])->where('is_active', 1)->get());
        view()->share('warehouse', Warehouse::where('is_active', 1)->get());
        parent::__construct();
    }
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::with([
                'exchangeRate',
                'invoiceItems.stock.product.unit',
                'invoiceItems.stock.variant',
                'paymentTerm',
            ])->find((int)$unique);
            $products = $item->invoiceItems ?? [];

            // Mevcut ürünleri partial format'ına çevir
            $products = [];
            if (isset($item->invoiceItems) && $item->invoiceItems->count() > 0) {
                // En güncel döviz kurlarını al
                $exchangeRates = ExchangeRate::where('is_active', 1)
                    ->select('*')
                    ->whereIn('id', function ($query) {
                        $query->selectRaw('MAX(id)')
                            ->from('exchange_rates')
                            ->where('is_active', 1)
                            ->groupBy('code');
                    })
                    ->get();
                $exchangeRatesByCode = $exchangeRates->keyBy('code');
                
                $products = $item->invoiceItems->map(function ($product) use ($exchangeRatesByCode) {
                    // Currency type'dan exchange rate ID'sini bul
                    $exchangeRateId = null;
                    if (!empty($product->currency_type)) {
                        $exchangeRate = $exchangeRatesByCode->get($product->currency_type);
                        if ($exchangeRate) {
                            $exchangeRateId = $exchangeRate->id;
                        }
                    }
                    
                    return (object) [
                        'stock_id' => $product->stock_id,
                        'item_no' => $product->item_no,
                        'product_code' => $product->stock->product->sku ?? '',
                        'product_name' => $product->stock->product->name . ($product->stock->variant ? ' / ' . $product->stock->variant->name : ''),
                        'unit' => $product->stock->product->unit->name ?? '',
                        'quantity' => $product->quantity,
                        'unit_price' => $product->unit_price,
                        'vat_status' => $product->vat_status,
                        'vat_rate' => $product->vat_rate,
                        'exchange_rate_id' => $exchangeRateId,
                        'exchange_rate' => $product->exchange_rate,
                        'total_price' => $product->total_price,
                        'vat_amount' => $product->vat_amount,
                        'total_amount' => $product->total_amount,
                    ];
                })->toArray();
            }

            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
        } else {
            $item = new $this->model;
            // Yeni fatura için önizleme numarası oluştur (counter'ı artırmadan)
            $item->invoice_no = Invoice::generatePreviewInvoiceNumber();
            $item->invoice_date = now();
            $item->due_date = now()->addDays(30);
            $item->status = 0;
            $item->is_active = 1;
            $products = [];
        }

        return view("backend.$this->page.form", compact('item', 'products'));
    }

    public function saveHook(Request $request)
    {
        // 1. Request'ten parametreleri al
        $params = $request->except(['products', 'stockTable_length', 'stockTable_start', 'stockTable_search', 'stockTable_order', 'stockTable_draw']);

        // 2. Tutar alanlarını parse et (TL ve dövizli)
        if (isset($params['net_amount'])) {
            $params['net_amount'] = parseTrNumber($params['net_amount']);
        }
        if (isset($params['tax_amount'])) {
            $params['tax_amount'] = parseTrNumber($params['tax_amount']);
        }
        if (isset($params['total_amount'])) {
            $params['total_amount'] = parseTrNumber($params['total_amount']);
        }
        if (isset($params['net_amount_fx'])) {
            $params['net_amount_fx'] = parseTrNumber($params['net_amount_fx']);
        }
        if (isset($params['tax_amount_fx'])) {
            $params['tax_amount_fx'] = parseTrNumber($params['tax_amount_fx']);
        }
        if (isset($params['total_amount_fx'])) {
            $params['total_amount_fx'] = parseTrNumber($params['total_amount_fx']);
        }

        // Validation kurallarını güncelleme durumuna göre ayarla
        $invoiceId = $request->route('unique');
        if ($invoiceId) {
            // Güncelleme işleminde mevcut kaydın ID'sini hariç tut
            $this->validation[0]['invoice_no'] = 'required|string|max:50|unique:invoices,invoice_no,' . $invoiceId;
        } else {
            // Yeni kayıt için normal unique kuralı
            $this->validation[0]['invoice_no'] = 'required|string|max:50|unique:invoices,invoice_no';
        }

        // Yeni fatura ise gerçek fatura numarası oluştur
        if (!$invoiceId) {
            $params['invoice_no'] = Invoice::generatePreviewInvoiceNumber();
        }

        // Exchange rate ID'den currency code ve exchange rate bilgilerini al
        if (isset($params['exchange_rate_id']) && !empty($params['exchange_rate_id'])) {
            $exchangeRate = ExchangeRate::find($params['exchange_rate_id']);
            if ($exchangeRate) {
                $currencyCode = $exchangeRate->code;
                $exchange_rate_value = $exchangeRate->selling_rate;

                if ($currencyCode === 'TRY') {
                    // Sadece TL alanlarını doldur, FX alanlarını null yap
                    $params['net_amount_fx'] = null;
                    $params['tax_amount_fx'] = null;
                    $params['total_amount_fx'] = null;
                } else {
                    // Hem TL hem FX alanlarını doldur
                    if (isset($params['net_amount'])) {
                        $params['net_amount_fx'] = (float)$params['net_amount'] / (float)$exchange_rate_value;
                    }
                    if (isset($params['tax_amount'])) {
                        $params['tax_amount_fx'] = (float)$params['tax_amount'] / (float)$exchange_rate_value;
                    }
                    if (isset($params['total_amount'])) {
                        $params['total_amount_fx'] = (float)$params['total_amount'] / (float)$exchange_rate_value;
                    }
                }
            }
        } else {
            // TRY seçilmişse (exchange_rate_id boş)
            $params['net_amount_fx'] = null;
            $params['tax_amount_fx'] = null;
            $params['total_amount_fx'] = null;
        }

        // products verisini invoice_items olarak session'a kaydet
        if (isset($request['products'])) {
            session(['temp_invoice_items' => $request['products']]);
            unset($params['products']);
        }

        // Fatura kaydedildiğinde balance güncelleme işlemi (yeni kayıt ve güncelleme için)
        if (isset($params['current_id']) && isset($params['total_amount'])) {
            // total_amount değerini Türk Lirası formatından parse et (zaten yukarıda parse edildi)
            $totalAmount = $params['total_amount'];

            // Güncelleme işleminde eski fatura tutarını al
            $oldAmount = 0;
            if (!empty($invoiceId)) {
                $oldInvoice = Invoice::find($invoiceId);
                if ($oldInvoice) {
                    $oldAmount = (float) $oldInvoice->total_amount;
                }
            }

            // Güncelleme işleminde eski döviz bilgilerini al
            $oldExchangeRateId = null;
            if (!empty($invoiceId)) {
                $oldInvoice = Invoice::find($invoiceId);
                if ($oldInvoice) {
                    $oldExchangeRateId = $oldInvoice->exchange_rate_id;
                }
            }

            // Balance güncelleme işlemini session'a kaydet, saveBack'te yapılacak
            session(['temp_balance_update' => [
                'current_id' => $params['current_id'],
                'total_amount' => $totalAmount,
                'old_amount' => $oldAmount,
                'old_exchange_rate_id' => $oldExchangeRateId,
                'is_update' => !empty($invoiceId), // Güncelleme işlemi mi?
                'invoice_id' => $invoiceId // Fatura ID'si (güncelleme için)
            ]]);
        }

        // currency ve exchange_rate parametrelerini kaldır
        unset($params['currency']);
        unset($params['exchange_rate']);

        return $params;
    }

    public function saveBack($obj)
    {
        try {
            DB::beginTransaction();
            // Session'dan items verisini al
            $items = session('temp_invoice_items');

            if (!empty($items) && is_array($items)) {
                // Mevcut invoice_items kayıtlarını al
                $existingItems = InvoiceItem::where('invoice_id', $obj->id)->get();
                $existingItemIds = $existingItems->pluck('id')->toArray();
                $updatedItemIds = [];

                foreach ($items as $item) {
                    // item_no ile mevcut kaydı bul
                    $existingItem = InvoiceItem::where('invoice_id', $obj->id)
                        ->where('item_no', $item['item_no'] ?? null)
                        ->first();

                    $stock = isset($item['stock_id']) ? Stock::find($item['stock_id']) : null;
                    $product_id = $stock ? $stock->product_id : ($item['product_id'] ?? null);
                    $product_variant_id = $stock ? $stock->variant_id : ($item['product_variant_id'] ?? null);

                    // Exchange rate ID'den para birimi kodunu al
                    $currencyCode = 'TRY';
                    $exchangeRateValue = 1.0000;
                    if (isset($item['exchange_rate_id']) && !empty($item['exchange_rate_id'])) {
                        $exchangeRate = ExchangeRate::find($item['exchange_rate_id']);
                        if ($exchangeRate) {
                            $currencyCode = $exchangeRate->code;
                            $exchangeRateValue = $exchangeRate->selling_rate;
                        }
                    }
                    
                    $itemData = [
                        'invoice_id'         => $obj->id,
                        'item_no'            => $item['item_no'] ?? null,
                        'stock_id'           => $item['stock_id'] ?? null,
                        'product_id'         => $product_id,
                        'product_variant_id' => $product_variant_id,
                        'quantity'           => $item['quantity'] ?? 0,
                        'unit_price'         => $item['price'] ?? 0,
                        'vat_status'         => $item['vat_status'] ?? 0,
                        'vat_rate'           => $item['vat_rate'] ?? 0,
                        'total_price'        => $item['total_amount'] ?? 0,
                        'currency_type'      => $currencyCode,
                        'exchange_rate'      => $exchangeRateValue,
                        'is_active'          => 1,
                        'created_by'         => Auth::id(),
                    ];

                    if ($existingItem) {
                        $existingItem->update($itemData);
                        $updatedItemIds[] = $existingItem->id;
                    } else {
                        $newItem = InvoiceItem::create($itemData);
                        $updatedItemIds[] = $newItem->id;
                    }
                }

                // Güncellenmeyen mevcut kayıtları sil (form'dan kaldırılan satırlar)
                $itemsToDelete = array_diff($existingItemIds, $updatedItemIds);
                if (!empty($itemsToDelete)) {
                    InvoiceItem::whereIn('id', $itemsToDelete)->delete();
                }
            } else {
                // Eğer items yoksa tüm mevcut kayıtları sil
                InvoiceItem::where('invoice_id', $obj->id)->delete();
            }

            // Session'dan geçici veriyi temizle
            session()->forget('temp_invoice_items');

            // Balance güncelleme işlemi
            $balanceUpdate = session('temp_balance_update');
            if ($balanceUpdate && isset($balanceUpdate['current_id']) && isset($balanceUpdate['total_amount'])) {
                $current = Current::find($balanceUpdate['current_id']);
                if ($current) {
                    // Yeni fatura bilgileri
                    $exchangeRate = $obj->exchangeRate;
                    $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
                    $exchangeRateValue = $exchangeRate ? $exchangeRate->selling_rate : 1.0000;
                    $newAmount = $obj->total_amount;
                    $newAmountFX = $currencyCode === 'TRY' ? $newAmount : ($exchangeRateValue > 0 ? $newAmount / $exchangeRateValue : $newAmount);

                    // Eski fatura bilgileri
                    $oldExchangeRate = null;
                    $oldCurrencyCode = 'TRY';
                    $oldExchangeRateValue = 1.0000;
                    $oldAmount = $balanceUpdate['old_amount'] ?? 0;
                    $oldAmountFX = 0;
                    if (!empty($balanceUpdate['old_exchange_rate_id'])) {
                        $oldExchangeRate = ExchangeRate::find($balanceUpdate['old_exchange_rate_id']);
                        if ($oldExchangeRate) {
                            $oldCurrencyCode = $oldExchangeRate->code;
                            $oldExchangeRateValue = $oldExchangeRate->selling_rate;
                        }
                    }
                    $oldAmountFX = $oldCurrencyCode === 'TRY' ? $oldAmount : ($oldExchangeRateValue > 0 ? $oldAmount / $oldExchangeRateValue : $oldAmount);

                    // 1. Önce eski bakiyeden düş
                    if (!empty($balanceUpdate['is_update'])) {
                        // Eski para birimi TRY ise balances tablosundan düş
                        if ($oldCurrencyCode === 'TRY') {
                            $balance = $current->balance()->firstOrCreate(
                                ['current_id' => $current->id],
                                ['debit_balance' => 0, 'is_active' => 1, 'created_by' => Auth::id()]
                            );
                            if ($oldAmount > 0) {
                                $balance->decrement('debit_balance', $oldAmount);
                            }
                        }

                        // BalanceCurrencies tablosundan düş (TÜM para birimleri için)
                        $balance = $current->balance()->firstOrCreate(
                            ['current_id' => $current->id],
                            ['debit_balance' => 0, 'is_active' => 1, 'created_by' => Auth::id()]
                        );
                        $balanceCurrency = $balance->balanceCurrencies()->firstOrCreate(
                            ['currency_code' => $oldCurrencyCode, 'current_id' => $current->id],
                            ['debit_balance' => 0, 'credit_balance' => 0]
                        );
                        if ($oldAmountFX > 0) {
                            $balanceCurrency->decrement('debit_balance', $oldAmountFX);
                        }
                    }

                    // 2. Sonra yeni bakiyeye ekle
                    if ($currencyCode === 'TRY') {
                        $balance = $current->balance()->firstOrCreate(
                            ['current_id' => $current->id],
                            ['debit_balance' => 0, 'credit_balance' => 0, 'is_active' => 1, 'created_by' => Auth::id()]
                        );
                        $balance->increment('debit_balance', $newAmount);
                    }

                    // BalanceCurrencies tablosuna kayıt (TÜM para birimleri için)
                    $balance = $current->balance()->firstOrCreate(
                        ['current_id' => $current->id],
                        ['debit_balance' => 0, 'is_active' => 1, 'created_by' => Auth::id()]
                    );
                    $balanceCurrency = $balance->balanceCurrencies()->firstOrCreate(
                        ['currency_code' => $currencyCode, 'current_id' => $current->id],
                        ['debit_balance' => 0, 'credit_balance' => 0]
                    );
                    $balanceCurrency->increment('debit_balance', $newAmountFX);
                }
                session()->forget('temp_balance_update');
            }

            // --- STOCK MOVEMENT OLUŞTURMA ---
            if ($obj && $obj->id && $obj->invoice_type_id == 2) {
                // Mevcut StockMovement var mı kontrol et (invoice_id ile)
                $stockMovement = StockMovement::where('invoice_id', $obj->id)->first();
                if ($stockMovement) {
                    // Güncelle
                    $stockMovement->update([
                        'movement_date' => now(),
                        'total_amount' => $obj->total_amount,
                        'currency_code' => $obj->currency,
                        'warehouse_id' => $obj->warehouse_id,
                        'current_id' => $obj->current_id,
                        'starter_id' => Auth::id(),
                    ]);
                } else {
                    // Oluştur
                    $stockMovement = StockMovement::create([
                        'invoice_id' => $obj->id,
                        'movement_no' => StockMovement::generateMovementNo(),
                        'current_id' => $obj->current_id,
                        'starter_id' => Auth::id(),
                        'movement_date' => now(),
                        'status_id' => 1,
                        'stock_movement_reason_id' => 6,
                        'stock_movement_type_id' => 2,
                        'notes' => 'Fatura oluşturulurken otomatik oluştu - ' . $obj->invoice_no,
                        'warehouse_id' => $obj->warehouse_id,
                        'location_id' => null,
                        'target_warehouse_id' => null,
                        'target_location_id' => null,
                        'total_amount' => $obj->total_amount,
                        'currency_code' => $obj->currency,
                    ]);
                }
                // Faturadaki kalemler
                $invoiceItems = $obj->invoiceItems ?? [];
                $existingItems = StockMovementItem::where('stock_movement_id', $stockMovement->id)->get();

                // Her bir mevcut item'ı, product_id-variant_id anahtarı ile eşle
                $existingItemMap = $existingItems->keyBy(function ($item) {
                    return $item->product_id . '-' . ($item->variant_id ?? '0');
                });

                // Formdan gelen item'lar için anahtarları topla
                $formItemKeys = [];
                foreach ($invoiceItems as $item) {
                    $key = $item->product_id . '-' . ($item->product_variant_id ?? '0');
                    $formItemKeys[] = $key;

                    // Stok ve birim bul
                    $stock = Stock::where('product_id', $item->product_id)
                        ->where('warehouse_id', $obj->warehouse_id)
                        ->when($item->product_variant_id ?? null, function ($q) use ($item) {
                            $q->where('variant_id', $item->product_variant_id);
                        })
                        ->first();
                    $unit = Unit::where('name', $item->unit)->first();

                    $itemData = [
                        'stock_movement_id' => $stockMovement->id,
                        'stock_id' => $stock ? $stock->id : null,
                        'stock_batch_id' => null,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->product_variant_id ?? null,
                        'unit_id' => $unit ? $unit->id : null,
                        'base_quantity' => $item->quantity,
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'total_price' => $item->quantity * $item->unit_price,
                        'currency_code' => $item->currency_type,
                        'status_id' => 1,
                    ];

                    if (isset($existingItemMap[$key])) {
                        // Güncelle
                        $existingItemMap[$key]->update($itemData);
                    } else {
                        // Yeni oluştur
                        StockMovementItem::create($itemData);
                    }
                }

                // Mevcut olup formda olmayanları soft delete yap
                foreach ($existingItems as $oldItem) {
                    $key = $oldItem->product_id . '-' . ($oldItem->variant_id ?? '0');
                    if (!in_array($key, $formItemKeys)) {
                        $oldItem->delete();
                    }
                }
            }

            // --- STOCK RESERVATION OLUŞTURMA ---
            if ($obj && $obj->id && $obj->invoice_type_id == 2) {
                $reservationKeys = [];
                foreach ($obj->invoiceItems as $item) {
                    $reservationData = [
                        'stock_id' => $item->stock_id,
                        'invoice_id' => $obj->id,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->product_variant_id,
                        'reservation_reason_id' => 1,
                        'reservation_type_id' => 1,
                        'current_id' => $obj->current_id,
                        'priority_level' => 5,
                        'quantity' => $item->quantity,
                        'start_date' => $obj->invoice_date,
                        'is_active' => 1,
                    ];
                    $reservation = StockReservation::where([
                        'stock_id' => $item->stock_id,
                        'invoice_id' => $obj->id,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->product_variant_id,
                    ])->first();
                    $key = $item->stock_id . '-' . $item->product_id . '-' . ($item->product_variant_id ?? '0');
                    $reservationKeys[] = $key;
                    if ($reservation) {
                        $reservation->update($reservationData);
                    } else {
                        StockReservation::create($reservationData);
                    }
                }
                // Fazlalık olan rezervasyonları soft delete yap
                $allReservations = StockReservation::where('invoice_id', $obj->id)->get();
                foreach ($allReservations as $reservation) {
                    $key = $reservation->stock_id . '-' . $reservation->product_id . '-' . ($reservation->variant_id ?? '0');
                    if (!in_array($key, $reservationKeys)) {
                        $reservation->delete();
                    }
                }
            }

            DB::commit();
            return redirect()->route("backend.invoices_list")->with('success', 'Fatura başarılı şekilde kaydedildi');
        } catch (\Exception) {
            DB::rollBack();
            return redirect()->route("backend.invoices_list")->with('error', 'Fatura kaydedilirken bir hata oluştu: ');
        }
    }
    public function datatableHook($obj)
    {
        return $obj->addColumn('invoiceStatus.name', function ($item) {
            return $item->invoiceStatus ? $item->invoiceStatus->name : '-';
        });
    }
    public function detail(Request $request, $unique = null)
    {
        try {
            $invoce = Invoice::with(['current', 'invoiceItems', 'invoiceStatus', 'exchangeRate'])->findOrFail($unique ?? $request->input('id'));

            // Tahsilat geçmişini al
            $transactions = Transaction::with(['exchangeRate', 'paymentType'])
                ->where('invoice_id', $invoce->id)
                ->where('transaction_type_id', 2) // Tahsilat işlemleri
                ->orderBy('transaction_date', 'desc')
                ->get();

            // Exchange rate bilgilerini yükle
            $exchangeRates = ExchangeRate::where('is_active', 1)->get()->keyBy('code');

            $breadcrumb = [
                'Ayarlar' => '#',
                'Satış Faturaları' => route('backend.invoices_list'),
                'Satış Faturaları Detayı' => route('backend.invoices_detail',  ['unique' => $unique])
            ];
            view()->share('breadcrumb', $breadcrumb);
            // Sadece 1,2,3 id'li statüler
            $invoiceStatusList = InvoiceStatus::whereIn('id', [1, 2, 3])->get();
            return view('backend.invoices.detail', [
                'container' => (object)[
                    'page' => $this->page,
                    'title' => 'Satış Faturalarıf'
                ],
                'title' => 'Satış Faturaları Detay',
                'subTitle' => 'Satış Faturaları Detay',
                'item' => $invoce,
                'transactions' => $transactions,
                'invoiceStatusList' => $invoiceStatusList,
                'exchangeRates' => $exchangeRates
            ]);
        } catch (\Exception $e) {
            return redirect()->route('backend.invoices_list')->with('error', 'Satış Faturaları detayı görüntülenirken bir hata oluştu: ' . $e->getMessage());
        }
    }
    public function updateStatus(Request $request)
    {
        try {
            $id = $request->input('id');
            $invoice = Invoice::findOrFail($id);
            $newStatus = (int) $request->input('status_id');
            $reasonForCancellation = $request->input('reason_for_cancellation');

            // Eğer zaten Onaylandı durumundaysa, değişikliğe izin verme
            if ($invoice->invoice_status_id === 2) {
                return response()->json(['success' => false, 'message' => 'Onaylanmış faturanın durumu değiştirilemez.']);
            }

            if ($newStatus === 3) {
                // İptal durumunu ve sebebini kaydet
                $invoice->invoice_status_id = 3;
                $invoice->reason_for_cancellation = $reasonForCancellation;
                $invoice->save();
                return response()->json(['success' => true, 'message' => 'Fatura iptal edildi.']);
            } elseif (in_array($newStatus, [1, 2])) {
                // Beklemede veya Onaylandı durumuna güncelle
                $invoice->invoice_status_id = $newStatus;
                $invoice->reason_for_cancellation = null;
                $invoice->save();
                return response()->json(['success' => true, 'message' => 'Fatura durumu güncellendi.']);
            }

            return response()->json(['success' => false, 'message' => 'Geçersiz durum seçimi.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
    }

    public function delete(Request $request)
    {
        $invoice = $this->model::find((int)$request->post('id'));

        if (! $invoice) {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı'
            ]);
        }

        // Model'deki canDelete metodunu kullan
        if (!Invoice::canDelete($invoice)) {
            return response()->json([
                'status' => false,
                'message' => 'Onaylanmış, kısmi muhasebeleştirilmiş ya da muhasebeleştirilmiş fatura silinemez.'
            ]);
        }

        $invoice->delete();

        Cache::flush();

        return response()->json([
            'status' => true,
            'message' => 'Fatura başarıyla silindi.'
        ]);
    }

    public function exportExcel(Request $request, $unique = null)
    {
        // Satış faturalarını (invoice_type_id = 2) ilişkilerle birlikte getir
        $invoices = Invoice::with(['current', 'invoiceStatus'])
            ->where('invoice_type_id', 2)
            ->get();

        $exporter = new ExportInvoices($invoices, 'Satış Faturaları');
        $spreadsheet = $exporter->export();

        // Excel çıktısı için response döndür
        $writer = new Xlsx($spreadsheet);
        $filename = 'satis_faturalari.xlsx';

        // Response ile dosya indirme
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        $writer->save('php://output');
        exit;
    }
    public function pdf(Request $request, $unique = null)
    {
        // $unique ile satış faturası bulma (invoice_type_id = 2)
        $invoice = Invoice::with(['current.city', 'current.country', 'invoiceItems', 'invoiceStatus'])
            ->where('invoice_type_id', 2)
            ->findOrFail($unique ?? $request->input('id')); // $unique null ise, Request'ten id al

        // Request'ten ek parametreleri al (örneğin, format seçenekleri)
        $paperSize = $request->input('paper_size', 'a4'); // Varsayılan A4
        $orientation = $request->input('orientation', 'portrait'); // Varsayılan portre

        // ExportInvoices sınıfına fatura kalemlerini geçir
        $export = new ExportInvoices($invoice->invoiceItems);
        $html = $export->exportToHtml($invoice); // HTML içeriğini al

        // PDF oluştururken Request'ten gelen parametreleri kullan
        $pdf = Pdf::loadHTML($html)
            ->setPaper($paperSize, $orientation)
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'DejaVu Sans', // Türkçe karakter desteği
            ]);

        // Dosya adını dinamik olarak oluştur - geçersiz karakterleri temizle
        $invoiceNo = $invoice->invoice_no ?? 'bilinmiyor';
        $cleanInvoiceNo = preg_replace('/[\/\\\\:*?"<>|]/', '_', $invoiceNo); // Geçersiz karakterleri _ ile değiştir
        $fileName = 'fatura_' . $cleanInvoiceNo . '.pdf';

        // Request'ten download isteği gelip gelmediğini kontrol et
        $attachment = $request->input('download', true); // Varsayılan olarak indirme

        return $pdf->stream($fileName, ['Attachment' => $attachment]);
    }
    public function calculateTotals(Request $request)
    {
        try {
            $productsData = $request->input('products', []);
            $mainExchangeRateId = $request->input('exchange_rate_id');

            // Gerekli tüm döviz kurlarını tek seferde çekelim
            $productExchangeRateIds = array_column($productsData, 'exchange_rate_id');
            $allRateIds = array_unique(array_merge([$mainExchangeRateId], $productExchangeRateIds));
            $exchangeRates = ExchangeRate::whereIn('id', $allRateIds)->get()->keyBy('id');

            // Ana faturanın döviz kurunu alalım
            $mainExchangeRate = $exchangeRates->get($mainExchangeRateId);
            $mainCurrencySymbol = $mainExchangeRate ? $mainExchangeRate->symbol : '₺';
            $mainSellingRate = $mainExchangeRate ? $mainExchangeRate->selling_rate : 1;

            $grandTotalNetTry = 0;
            $grandTotalVatTry = 0;
            $grandTotalWithVatTry = 0;
            $productRows = [];

            // Her ürün için hesaplama yap
            foreach ($productsData as $stockId => $product) {
                $quantity = (int)($product['quantity'] ?? 1);
                $unitPrice = (float)parseTrNumber($product['price'] ?? 0);
                $vatRate = (float)($product['vat_rate'] ?? 0);
                $vatStatus = (int)($product['vat_status'] ?? 0); // 0: Hariç, 1: Dahil
                $rowExchangeRateId = $product['exchange_rate_id'] ?? $mainExchangeRateId;

                // Ürün satırının döviz kurunu al
                $rowExchangeRate = $exchangeRates->get($rowExchangeRateId);
                $rowSellingRate = $rowExchangeRate ? $rowExchangeRate->selling_rate : 1;

                // Tutar ve KDV'yi ürünün kendi para biriminde hesapla
                $lineNet = $quantity * $unitPrice;
                $lineVat = 0;
                if ($vatStatus === 1) { // KDV Dahil
                    $lineVat = $lineNet - ($lineNet / (1 + ($vatRate / 100)));
                    $lineNet = $lineNet - $lineVat; // Net tutarı KDV'den arındır
                } else { // KDV Hariç
                    $lineVat = $lineNet * ($vatRate / 100);
                }
                $lineWithVat = $lineNet + $lineVat;

                // Her satırın tutarını TL'ye çevir
                $lineTotalTry = $lineWithVat * $rowSellingRate;

                // Genel toplamlara TL tutarları ekle
                $grandTotalNetTry += $lineNet * $rowSellingRate;
                $grandTotalVatTry += $lineVat * $rowSellingRate;
                $grandTotalWithVatTry += $lineTotalTry;

                // Frontend'deki satırları güncellemek için verileri hazırla
                $productRows[$stockId] = [
                    // Görünen tutar inputu için TL karşılığını gönder
                    'total_amount' => number_format($lineTotalTry, 2, ',', '.'),

                    // Kaydetmek için gizli inputlara orijinal dövizdeki tutarları gönder
                    'amount_raw' => $lineNet,
                    'vat_amount_raw' => $lineVat,
                    'total_amount_raw' => $lineWithVat,
                ];
            }

            // Genel toplamların ana faturanın para birimine göre karşılığını hesapla
            $grandTotalNetFx = $mainSellingRate > 0 ? $grandTotalNetTry / $mainSellingRate : 0;
            $grandTotalVatFx = $mainSellingRate > 0 ? $grandTotalVatTry / $mainSellingRate : 0;
            $grandTotalWithVatFx = $mainSellingRate > 0 ? $grandTotalWithVatTry / $mainSellingRate : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'net_amount' => number_format($grandTotalNetTry, 2, ',', '.'),
                    'tax_amount' => number_format($grandTotalVatTry, 2, ',', '.'),
                    'total_amount' => number_format($grandTotalWithVatTry, 2, ',', '.'),
                    'net_amount_fx' => number_format($grandTotalNetFx, 2, ',', '.'),
                    'tax_amount_fx' => number_format($grandTotalVatFx, 2, ',', '.'),
                    'total_amount_fx' => number_format($grandTotalWithVatFx, 2, ',', '.'),
                    'currency_symbol' => $mainCurrencySymbol,
                    'product_rows' => $productRows,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Hesaplama sırasında bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }
}
