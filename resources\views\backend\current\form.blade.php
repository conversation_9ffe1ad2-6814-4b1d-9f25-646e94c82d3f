@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON>üzenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>


                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            {{-- Ad --}}
                            <div class="col-6">
                                <label class="form-label">Ünvan</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen şirket ünvanınızı giriniz"
                                        name="title" value="{{ old('title', $item->title ?? '') }}">
                                    <x-form-error field="title" />
                                </div>
                            </div>

                            {{-- Soyad --}}
                            <div class="col-6">
                                <label class="form-label">Kısa Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen kısa açıklama giriniz"
                                        name="short_description" value="{{ old('short_description', $item->short_description ?? '') }}">
                                    <x-form-error field="short_description" />
                                </div>
                            </div>

                            {{-- Email --}}
                            <div class="col-6">
                                <label class="form-label">Email</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mage:email"></iconify-icon>
                                    </span>
                                    <input type="email" class="form-control" placeholder="Lütfen e-posta adresi giriniz"
                                        name="email" value="{{ old('email', $item->email ?? '') }}">
                                    <x-form-error field="email" />
                                </div>
                            </div>

                            {{-- Phone --}}
                            <div class="col-6">
                                <label class="form-label">Telefon No</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:phone-calling-linear"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                        value="{{ old('phone', $item->phone ?? '') }}"
                                        placeholder="Lütfen telefon numarası giriniz">
                                    <x-form-error field="phone" />
                                </div>
                            </div>

                            {{-- Kurum Tipi --}}
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Kurum Tipi</label>
                                    <select class="form-control select2" name="instutation_id">
                                        <option value="">Kurum Tipi Seçin</option>
                                        @foreach ($insutations as $insutation)
                                            <option value="{{ $insutation->id }}"
                                                {{ old('instutation_id', $item->instutation_id) == $insutation->id ? 'selected' : '' }}>
                                                {{ $insutation->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('instutation_id')
                                        <span class="badge badge-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            {{-- Cari Tipi --}}
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Cari Tipi</label>
                                    <select class="form-control select2" name="current_type_id" >
                                        <option value="">Cari Tipi Seçin</option>
                                        @foreach ($currenttypes as $currenttype)
                                            <option value="{{ $currenttype->id }}"
                                                {{ old('current_type_id', $item->current_type_id) == $currenttype->id ? 'selected' : '' }}>
                                                {{ $currenttype->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('current_type_id')
                                        <span class="badge badge-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-6">
                                <label class="form-label">Vergi No</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text"
                                        class="form-control"
                                        placeholder="Lütfen Vergi Numaranızı giriniz"
                                        name="tax_number"
                                        maxlength="10"
                                        pattern="\d{10}"
                                        oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10)"
                                        value="{{ old('tax_number', $item->tax_number ?? '') }}">
                                    <x-form-error field="tax_number" />
                                </div>
                            </div>
                            {{-- Durum --}}
                            <div class="col-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>

                            {{-- Ülke Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Ülke</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="ic:round-flag"></iconify-icon>
                                    </span>
                                    <select id="country" name="country_id" class="form-control select2">
                                        <option value="">Ülke Seçiniz</option>
                                        @foreach ($countrys as $country)
                                            <option value="{{ $country->id }}"
                                                {{ old('country_id') == $country->id || $item->country_id == $country->id || $country->id == 136 ? 'selected' : '' }}>
                                                {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="country_id" />
                                </div>
                            </div>

                            {{-- Şehir Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Şehir</label>
                                <select id="city" name="city_id" class="form-control select2">
                                    <option value="">Şehir Seçiniz</option>
                                    {{-- Mevcut şehirler (veya AJAX ile doldurulacak) --}}
                                    @foreach ($cities as $oneCity)
                                        <option value="{{ $oneCity->id }}"
                                            {{ old('city_id', $item->city_id ?? '') == $oneCity->id ? 'selected' : '' }}>
                                            {{ $oneCity->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            {{-- İlçe Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">İlçe</label>
                                <select id="district" name="district_id" class="form-control select2">
                                    <option value="">İlçe Seçiniz</option>
                                    @foreach ($districts as $dist)
                                        <option value="{{ $dist->id }}"
                                            {{ old('district_id', $item->district_id ?? '') == $dist->id ? 'selected' : '' }}>
                                            {{ $dist->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            {{-- Mahalle Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Adres</label>
                                <input type="text" class="form-control" name="address"
                                id="address" value="{{ old('address', $item->address ?? '') }}">
                                <x-form-error field="address" />
                            </div>

                            {{-- Vergi Dairesi Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Vergi Dairesi</label>
                                <select id="tax_offices" name="tax_offices_id" class="form-control select2">
                                    <option value="">Vergi Dairesi Seçiniz</option>
                                    @foreach ($taxoffices as $taxoffice)
                                        <option value="{{ $taxoffice->id }}"
                                            {{ old('tax_offices_id', $item->tax_offices_id ?? '') == $taxoffice->id ? 'selected' : '' }}>
                                            {{ $taxoffice->name . ' - ' . $taxoffice->code . ' - ' . ($taxoffice->city->name ??'') }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-12 text-end">
                                <a href="{{ route('backend.' . $container->page . '_list') }}"
                                    class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </div><!-- row -->
                    </form>
                </div><!-- card-body -->
            </div><!-- card -->
        </div><!-- col-md-12 -->
    </div><!-- row -->
@endsection

@section('script')
    <script>
        // Blade'den tüm şehir, ilçe, mahalle ve vergi dairesi verilerini JSON olarak al
        const allCities = @json($allCities ?? $cities ?? []);
        const allDistricts = @json($allDistricts ?? $districts ?? []);
        const allTaxOffices = @json($allTaxOffices ?? $taxoffices ?? []);

        $(document).ready(function() {
            let oldCountryId = '{{ old('country_id', $item->country_id ?? '') }}';
            let oldCityId = '{{ old('city_id', $item->city_id ?? '') }}';
            let oldDistrictId = '{{ old('district_id', $item->district_id ?? '') }}';
            let oldTaxOfficeId = '{{ old('tax_offices_id', $item->tax_offices_id ?? '') }}';

            function filterCities(countryId, selectedCityId = null) {
                $('#city').html('<option value="">Şehir Seçiniz</option>');
                if (!countryId) return;
                allCities.filter(c => c.country_id == countryId).forEach(city => {
                    let selected = selectedCityId && selectedCityId == city.id ? 'selected' : '';
                    $('#city').append('<option value="' + city.id + '" ' + selected + '>' + city.name + '</option>');
                });
            }
            function filterDistricts(cityId, selectedDistrictId = null) {
                $('#district').html('<option value="">İlçe Seçiniz</option>');
                if (!cityId) return;
                allDistricts.filter(d => d.city_id == cityId).forEach(dist => {
                    let selected = selectedDistrictId && selectedDistrictId == dist.id ? 'selected' : '';
                    $('#district').append('<option value="' + dist.id + '" ' + selected + '>' + dist.name + '</option>');
                });
            }
            function filterTaxOffices(cityId, selectedTaxOfficeId = null) {
                $('#tax_offices').html('<option value="">Vergi Dairesi Seçiniz</option>');
                if (!cityId) return;
                allTaxOffices.filter(t => t.city_id == cityId).forEach(tax => {
                    let label = tax.name + ' - ' + tax.code + ' - ' + (tax.city_name || '');
                    let selected = selectedTaxOfficeId && selectedTaxOfficeId == tax.id ? 'selected' : '';
                    $('#tax_offices').append('<option value="' + tax.id + '" ' + selected + '>' + label + '</option>');
                });
            }

            $(document).on('change', '#country', function() {
                let countryId = $(this).val();
                filterCities(countryId);
                $('#district').html('<option value="">İlçe Seçiniz</option>');
                $('#neighborhood').html('<option value="">Mahalle Seçiniz</option>');
                $('#tax_offices').html('<option value="">Vergi Dairesi Seçiniz</option>');
            });
            $(document).on('change', '#city', function() {
                let cityId = $(this).val();
                filterDistricts(cityId);
                $('#neighborhood').html('<option value="">Mahalle Seçiniz</option>');
                filterTaxOffices(cityId);
            });
            $(document).on('change', '#district', function() {
                let districtId = $(this).val();
                filterNeighborhoods(districtId);
            });

            // Sayfa açılışında eski değerleri set et
            if (oldCountryId) {
                $('#country').val(oldCountryId);
                filterCities(oldCountryId, oldCityId);
                if (oldCityId) {
                    filterDistricts(oldCityId, oldDistrictId);
                    filterTaxOffices(oldCityId, oldTaxOfficeId);
                }
            }
        });
    </script>
@endsection
