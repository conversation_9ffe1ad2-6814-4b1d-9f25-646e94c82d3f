@extends('layout.layout')
@php
    $title = $container->title . ' Detay';
    $subTitle = $item->name . ' - Detay';
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Ürün Bilgileri</h5>
                    <div class="d-flex gap-2">
                        <a href="{{ route('backend.' . $container->page . '_form', $item->id) }}"
                            class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                            Düzenle
                        </a>
                        <a href="{{ route('backend.' . $container->page . '_list') }}"
                            class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                            Listeye Dön
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table basic-table mb-0">
                                <tr>
                                    <td width="40%">Ür<PERSON><PERSON> (SKU):</td>
                                    <td>{{ $item->sku }}</td>
                                </tr>
                                <tr>
                                    <td>Ürün Adı:</td>
                                    <td>{{ $item->name }}</td>
                                </tr>
                                <tr>
                                    <td>Kategori:</td>
                                    <td>{{ $item->category->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td>Marka:</td>
                                    <td>{{ $item->brand->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td>Barkod:</td>
                                    <td>{{ $item->barcode ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td>Durum:</td>
                                    <td>
                                        @if ($item->is_active)
                                            <span
                                                class="bg-success-focus text-success-600 px-12 py-2 radius-4 fw-medium text-sm">Aktif</span>
                                        @else
                                            <span
                                                class="bg-danger-focus text-danger-600 px-12 py-2 radius-4 fw-medium text-sm">Pasif</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table basic-table mb-0">
                                <tr>
                                    <td width="40%">Alış Fiyatı:</td>
                                    <td>{{ number_format($item->purchase_price, 2, ',', '.') }}
                                        {{ $item->purchase_currency_code ?? 'TRY' }}</td>
                                </tr>
                                <tr>
                                    <td>Satış Fiyatı:</td>
                                    <td>{{ number_format($item->sale_price, 2, ',', '.') }}
                                        {{ $item->sale_currency_code ?? 'TRY' }}</td>
                                </tr>
                                <tr>
                                    <td>Birim Tipi:</td>
                                    <td>{{ $item->unitType->name ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td>Birim:</td>
                                    <td>{{ $item->unit->name ?? '-' }} ({{ $item->unit->symbol ?? '-' }})</td>
                                </tr>
                                <tr>
                                    <td>Kritik Stok Seviyesi:</td>
                                    <td>{{ formatQuantityWithUnit($item->critical_stock_level, $item->unit ?? null) }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if ($item->description)
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Açıklama:</h6>
                                <p>{{ $item->description }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Ağırlık</h6>
                                <p class="fs-5 fw-medium">{{ number_format($item->weight, 3, ',', '.') }} kg</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Ölçüler (E x B x Y)</h6>
                                <p class="fs-5 fw-medium">
                                    {{ number_format($item->length, 2, ',', '.') }} x
                                    {{ number_format($item->width, 2, ',', '.') }} x
                                    {{ number_format($item->height, 2, ',', '.') }} cm
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Hacim</h6>
                                <p class="fs-5 fw-medium">{{ number_format($item->volume, 4, ',', '.') }} m³</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Toplam Stok</h6>
                                <p
                                    class="fs-5 fw-medium {{ $item->total_stock_quantity <= 0 ? 'text-danger' : ($item->total_stock_quantity <= $item->critical_stock_level ? 'text-warning' : 'text-success') }}">
                                    {{ formatQuantityWithUnit($item->total_stock_quantity, $item->unit ?? null) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if ($item->costHistories && $item->costHistories->count() > 0)
            <div class="col-md-12">
                <div class="card basic-data-table">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Maliyet Geçmişi</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table datatable class="table bordered-table mb-0" id="costHistoryTable" data-page-length='10'>
                                <thead>
                                    <tr>
                                        <th scope="col">Tedarikçi</th>
                                        <th scope="col">Maliyet</th>
                                        <th scope="col">Para Birimi</th>
                                        <th scope="col">Başlangıç Tarihi</th>
                                        <th scope="col">Bitiş Tarihi</th>
                                        <th scope="col">Notlar</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        @if ($item->variants && $item->variants->count() > 0)
            <div class="col-md-12">
                <div class="card basic-data-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Ürün Varyantları</h5>
                        <span class="badge bg-primary">{{ $item->variants->count() }} Varyant</span>
                    </div>
                    <div class="card-body">

                        <div class="table-responsive">
                            <table datatable class="table bordered-table mb-0" id="variantsTable" data-page-length='10'>
                                <thead>
                                    <tr>
                                        <th scope="col">Varyant Kodu</th>
                                        <th scope="col">Varyant Adı</th>
                                        <th scope="col">Barkod</th>
                                        <th scope="col">Alış Fiyatı</th>
                                        <th scope="col">Satış Fiyatı</th>
                                        <th scope="col" class="text-center">Stok Durumu</th>
                                        <th scope="col" class="text-center">Durum</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        @if ($item->stocks && $item->stocks->count() > 0)
        <div class="col-md-12">
            <div class="card basic-data-table">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Depo Stok Durumu</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table datatable class="table bordered-table mb-0" id="stocksTable" data-page-length='10'>
                            <thead>
                                <tr>
                                    <th scope="col">Depo</th>
                                    <th scope="col">Lokasyon</th>
                                    <th scope="col">Varyant</th>
                                    <th scope="col" class="text-center">Miktar</th>
                                    <th scope="col" class="text-center">Rezerve</th>
                                    <th scope="col" class="text-center">Kullanılabilir</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endif
        @if ($item->stockMovements && $item->stockMovements->count() > 0)
            <div class="col-md-12">
                <div class="card basic-data-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Stok Hareketleri</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table datatable class="table bordered-table mb-0" id="movementsTable" data-page-length='10'>
                                <thead>
                                    <tr>
                                        <th scope="col">Tarih</th>
                                        <th scope="col">Hareket Tipi</th>
                                        <th scope="col">Cari</th>
                                        <th scope="col" class="text-center">Miktar</th>
                                        <th scope="col">Birim</th>
                                        <th scope="col">Birim Fiyat</th>
                                        <th scope="col">Toplam Fiyat</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            @if ($item->variants && $item->variants->count() > 0)
                BaseCRUD.selector = "#variantsTable";
                var variantsTable = BaseCRUD.ajaxtable({
                    ajax: {
                        url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=true&type=variants",
                        type: 'POST',
                        data: function(d) {
                            var cfilter = {};
                            return $.extend({}, d, {
                                "cfilter": cfilter
                            });
                        },
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    },
                    columns: [{
                            data: 'sku',
                            name: 'sku',
                            className: 'text-center',
                        },
                        {
                            data: 'name',
                            name: 'name',
                            className: 'text-center',
                        },
                        {
                            data: 'barcode',
                            name: 'barcode',
                            defaultContent: '-',
                            className: 'text-center',
                        },
                        {
                            data: 'purchase_price',
                            name: 'purchase_price',
                            className: 'text-center',
                        },
                        {
                            data: 'sale_price',
                            name: 'sale_price',
                            className: 'text-center',
                        },
                        {
                            render: function(data, type, row) {
                                var totalStock = parseFloat(row.total_stock_quantity) || 0;
                                var availableStock = parseFloat(row.available_stock_quantity) || 0;
                                var statusClass = 'text-success-600';

                                if (totalStock <= 0) {
                                    statusClass = 'text-danger-600';
                                } else if (totalStock <= 10) {
                                    statusClass = 'text-warning-600';
                                }

                                return '<span class="fw-medium ' + statusClass + '">' +
                                    parseFloat(availableStock).toLocaleString('tr-TR', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    }) + ' / ' +
                                    parseFloat(totalStock).toLocaleString('tr-TR', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    }) + '</span>';
                            },
                            data: null,
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        },
                        {
                            render: function(data, type, row) {
                                return row.is_active == 'Aktif' || row.is_active == 1 ?
                                    '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                    '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                            },
                            data: null,
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        }
                    ],
                    order: [
                        [0, 'desc']
                    ],
                    pageLength: 10
                });
            @endif

            BaseCRUD.selector = "#stocksTable";
            var stocksTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=true&type=stocks",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'warehouse.name',
                        name: 'warehouse.name',
                        className: 'text-center',
                    },
                    {
                        data: 'warehouse_location.name',
                        name: 'warehouse_location.name',
                        defaultContent: 'Ana Lokasyon',
                        className: 'text-center',
                    },
                    {
                        data: 'variant.name',
                        name: 'variant.name',
                        defaultContent: '-',
                        className: 'text-center',
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center'
                    },
                    {
                        data: 'reserved_quantity',
                        name: 'reserved_quantity',
                        className: 'text-center'
                    },
                    {
                        data: 'available_quantity',
                        name: 'available_quantity',
                        className: 'text-center'
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 10
            });

            BaseCRUD.selector = "#movementsTable";
            var movementsTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=true&type=movements",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'stockMovement.movement_date',
                        name: 'stockMovement.movement_date',
                        className: 'text-center',
                    },
                    {
                        data: 'stockMovement.stockMovementType.name',
                        name: 'stockMovement.stockMovementType.name',
                        className: 'text-center',
                    },
                    {
                        data: 'stockMovement.current.title',
                        name: 'stockMovement.current.title',
                        defaultContent: '-',
                        className: 'text-center',
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center'
                    },
                    {
                        data: 'unit.symbol',
                        name: 'unit.symbol',
                        className: 'text-center',
                    },
                    {
                        data: 'unit_price',
                        name: 'unit_price',
                        className: 'text-center',
                    },
                    {
                        data: 'total_price',
                        name: 'total_price',
                        className: 'text-center',
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 10
            });

            BaseCRUD.selector = "#costHistoryTable";
            var costHistoryTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $item->id) }}?datatable=true&type=costhistory",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'current.title',
                        name: 'current.title',
                        className: 'text-center',
                    },
                    {
                        data: 'cost',
                        name: 'cost',
                        className: 'text-center',
                    },
                    {
                        data: 'currency_code',
                        name: 'currency_code',
                        className: 'text-center',
                    },
                    {
                        data: 'valid_from',
                        name: 'valid_from',
                        className: 'text-center',
                    },
                    {
                        data: 'valid_to',
                        name: 'valid_to',
                        className: 'text-center',
                    },
                    {
                        data: 'notes',
                        name: 'notes',
                        className: 'text-center',
                    }
                ],
                order: [
                    [3, 'desc']
                ],
                pageLength: 10
            });

            setTimeout(function() {
                @if ($item->variants && $item->variants->count() > 0)
                    $('#variantsTable_wrapper .dt-custom-search input').off('keyup').on('keyup',
                        function() {
                            variantsTable.search(this.value).draw();
                        });
                @endif

                $('#stocksTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    stocksTable.search(this.value).draw();
                });

                $('#movementsTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    movementsTable.search(this.value).draw();
                });

                $('#costHistoryTable_wrapper .dt-custom-search input').off('keyup').on('keyup', function() {
                    costHistoryTable.search(this.value).draw();
                });
            }, 300);
        });
    </script>
@endsection
