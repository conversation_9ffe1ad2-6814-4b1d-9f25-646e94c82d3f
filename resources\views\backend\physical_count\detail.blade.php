@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = 'Sayım No: ' . $physicalCount->count_code;
@endphp

@section('content')
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Sayım Bilgileri</h5>
            <div class="d-flex gap-2">
                @if($physicalCount->status_id == 1)
                    <button type="button" class="btn btn-success btn-sm rounded-pill waves-effect waves-themed" id="approveButton" data-id="{{ $physicalCount->id }}" data-status="2">
                        Onayla
                    </button>
                @endif
                @if($physicalCount->status_id == 2)
                    <button type="button" class="btn btn-warning btn-sm rounded-pill waves-effect waves-themed" id="refreshStockButton" data-id="{{ $physicalCount->id }}">
                        Stokları Güncelle
                    </button>
                @endif
                <a href="{{ route('backend.physical_count_form', $physicalCount->id) }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                    Düzenle
                </a>
                <a href="{{ route('backend.physical_count_list') }}"
                    class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                    Geri
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="30%"><strong>Sayım Kodu:</strong></td>
                            <td>{{ $physicalCount->count_code }}</td>
                        </tr>
                        <tr>
                            <td><strong>Depo:</strong></td>
                            <td>{{ $physicalCount->warehouse->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Lokasyon:</strong></td>
                            <td>{{ $physicalCount->location->name ?? 'Tüm Depo' }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="30%"><strong>Sayım Tarihi:</strong></td>
                            <td>{{ $physicalCount->count_date->format('d.m.Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Durum:</strong></td>
                            <td>
                                @if($physicalCount->status_id == 1)
                                    <span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Taslak</span>
                                @elseif($physicalCount->status_id == 2)
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span>
                                @else
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">İptal</span>
                                @endif
                            </td>
                        </tr>
                        @if($physicalCount->approver_id)
                            <tr>
                                <td><strong>Onaylayan:</strong></td>
                                <td>{{ $physicalCount->approver->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Onay Tarihi:</strong></td>
                                <td>{{ $physicalCount->approval_date->format('d.m.Y H:i') }}</td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>

            @if($physicalCount->description)
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <strong>Açıklama:</strong> {{ $physicalCount->description }}
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Sayım Kalemleri</h5>
            @if($physicalCount->status_id == 1)
                <button type="button" class="btn btn-info btn-sm rounded-pill" id="saveCountsButton">
                    Değişiklikleri Kaydet
                </button>
            @endif
        </div>
        <div class="card-body">
            <form id="countItemsForm" action="{{ route('backend.physical_count_save', $physicalCount->id) }}" method="POST">
                @csrf
                <input type="hidden" name="id" value="{{ $physicalCount->id }}">

                <div class="table-responsive">
                    <table class="table bordered-table mb-0" id="countItemsTable">
                        <thead>
                            <tr>
                                <th scope="col">Ürün</th>
                                <th scope="col">Varyant</th>
                                <th scope="col" class="text-center">Sistem Miktarı</th>
                                <th scope="col" class="text-center">Sayılan Miktar</th>
                                <th scope="col" class="text-center">Fark</th>
                                <th scope="col">Notlar</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($physicalCount->items as $item)
                                <tr>
                                    <td>{{ $item->product->name }}</td>
                                    <td>{{ $item->variant->name ?? '-' }}</td>
                                    <td class="text-center system-quantity" data-value="{{ $item->system_quantity }}">
                                        {{ formatQuantityWithUnit($item->system_quantity, $item->product->unit ?? null) }}
                                    </td>
                                    <td class="text-center">
                                        @if($physicalCount->status_id == 1)
                                            @php
                                                $unit = $item->product->unit ?? null;
                                                $step = $unit && $unit->allow_decimal ? '0.' . str_repeat('0', ($unit->decimal_places ?? 4) - 1) . '1' : '1';
                                                $min = $unit && $unit->allow_decimal ? $step : '1';
                                                $pattern = $unit && !$unit->allow_decimal ? '[0-9]+' : null;
                                            @endphp
                                            <input type="number"
                                                   step="{{ $step }}"
                                                   min="0"
                                                   class="form-control form-control-sm counted-quantity"
                                                   name="items[{{ $item->id }}][counted_quantity]"
                                                   data-id="{{ $item->id }}"
                                                   data-allow-decimal="{{ $unit ? $unit->allow_decimal : 1 }}"
                                                   data-decimal-places="{{ $unit ? $unit->decimal_places : 4 }}"
                                                   data-unit-symbol="{{ $unit ? $unit->symbol : 'adet' }}"
                                                   value="{{ $item->counted_quantity }}"
                                                   data-original-value="{{ $item->counted_quantity }}"
                                                   @if($pattern) pattern="{{ $pattern }}" @endif>
                                        @else
                                            {{ formatQuantityWithUnit($item->counted_quantity, $item->product->unit ?? null) }}
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="difference-{{ $item->id }} {{ $item->difference > 0 ? 'text-success' : ($item->difference < 0 ? 'text-danger' : '') }}">
                                            {{ formatQuantityWithUnit($item->difference, $item->product->unit ?? null) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($physicalCount->status_id == 1)
                                            <div class="d-flex flex-column">
                                                <input type="text" class="form-control form-control-sm"
                                                       name="items[{{ $item->id }}][notes]"
                                                       value="{{ $item->notes }}"
                                                       style="width: 100%;">
                                                @if($item->notes && strlen($item->notes) > 10)
                                                    <small class="mt-1 d-block">
                                                        <a href="#" class="text-decoration-none notes-modal-trigger text-primary-600 fw-medium"
                                                           data-item-id="{{ $item->id }}"
                                                           data-notes="{{ $item->notes }}"
                                                           data-product-name="{{ $item->product->name }}"
                                                           data-variant-name="{{ $item->variant->name ?? '' }}"
                                                           role="button"
                                                           aria-label="Ürün notunu görüntüle: {{ $item->product->name }}{{ $item->variant ? ' - ' . $item->variant->name : '' }}">
                                                            Görüntüle
                                                        </a>
                                                    </small>
                                                @endif
                                            </div>
                                        @else
                                            <div style="word-break: break-word;">
                                                @if($item->notes && strlen($item->notes) > 10)
                                                    <span class="notes-preview notes-modal-trigger text-primary-600 fw-medium"
                                                          style="cursor: pointer; text-decoration: underline;"
                                                          data-item-id="{{ $item->id }}"
                                                          data-notes="{{ $item->notes }}"
                                                          data-product-name="{{ $item->product->name }}"
                                                          data-variant-name="{{ $item->variant->name ?? '' }}"
                                                          role="button"
                                                          tabindex="0"
                                                          aria-label="Tam notu görüntüle: {{ $item->product->name }}{{ $item->variant ? ' - ' . $item->variant->name : '' }}"
                                                          title="Tıklayarak tam notu görüntüleyin">
                                                        {{ substr($item->notes, 0, 10) }}...
                                                    </span>
                                                @else
                                                    {{ $item->notes ?: '-' }}
                                                @endif
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>

    <div class="modal fade" id="notesModal" tabindex="-1" role="dialog" aria-labelledby="notesModalLabel" aria-modal="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header border-bottom-0 pb-2">
                    <h5 class="modal-title fw-semibold text-primary-600" id="notesModalLabel">
                        <span id="modalProductName"></span>
                        <span id="modalVariantName" class="text-neutral-500 fw-normal"></span>
                    </h5>
                    <button type="button" class="btn-close" id="notesModalCloseBtn" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-3">
                    <div class="bg-neutral-50 p-24 radius-8 border border-neutral-200" style="overflow-y: auto;">
                        <p id="modalNotesContent" class="mb-0 text-neutral-700 lh-lg" style="white-space: pre-wrap; word-wrap: break-word;"></p>
                    </div>
                </div>
                <div class="modal-footer border-top-0 pt-2">
                    <button type="button" class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed" id="notesModalCloseFooterBtn">
                        Kapat
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var hasChanges = false;
            var originalValues = {};

            function initializeOriginalValues() {
                originalValues = {};
                $('.counted-quantity').each(function() {
                    var input = $(this);
                    var itemId = input.data('id');
                    originalValues['qty_' + itemId] = input.val() || '0';
                });

                $('input[name*="[notes]"]').each(function() {
                    var input = $(this);
                    var name = input.attr('name');
                    var match = name.match(/items\[(\d+)\]\[notes\]/);
                    if (match) {
                        var itemId = match[1];
                        originalValues['notes_' + itemId] = input.val() || '';
                    }
                });

                hasChanges = false;
            }

            function checkForChanges() {
                hasChanges = false;

                $('.counted-quantity').each(function() {
                    var input = $(this);
                    var itemId = input.data('id');
                    var currentValue = input.val() || '0';
                    var originalValue = originalValues['qty_' + itemId] || '0';

                    if (currentValue !== originalValue) {
                        hasChanges = true;
                        return false;
                    }
                });

                if (!hasChanges) {
                    $('input[name*="[notes]"]').each(function() {
                        var input = $(this);
                        var name = input.attr('name');
                        var match = name.match(/items\[(\d+)\]\[notes\]/);
                        if (match) {
                            var itemId = match[1];
                            var currentValue = input.val() || '';
                            var originalValue = originalValues['notes_' + itemId] || '';

                            if (currentValue !== originalValue) {
                                hasChanges = true;
                                return false;
                            }
                        }
                    });
                }
            }

            $(document).on('input', '.counted-quantity', function() {
                var input = $(this);
                var itemId = input.data('id');

                var allowDecimal = input.data('allow-decimal');
                if (allowDecimal == 0) {
                    var value = parseFloat(input.val()) || 0;
                    if (value % 1 !== 0) {
                        input.val(Math.round(value));
                    }
                }

                updateDifference(input);
                checkForChanges();
            });

            $(document).on('blur', '.counted-quantity', function() {
                var input = $(this);
                var allowDecimal = input.data('allow-decimal');
                var decimalPlaces = input.data('decimal-places') || 4;

                var value = parseFloat(input.val()) || 0;

                if (allowDecimal == 0) {
                    input.val(Math.round(value));
                } else {
                    input.val(value.toFixed(decimalPlaces));
                }

                updateDifference(input);
                checkForChanges();
            });

            function updateDifference(input) {
                var itemId = input.data('id');
                var countedQty = parseFloat(input.val()) || 0;
                var systemQty = parseFloat(input.closest('tr').find('.system-quantity').data('value')) || 0;

                var difference = countedQty - systemQty;

                var allowDecimal = input.data('allow-decimal');
                var decimalPlaces = input.data('decimal-places') || 4;
                var unitSymbol = input.data('unit-symbol') || 'adet';

                var formattedDifference;
                if (allowDecimal == 0) {
                    formattedDifference = Math.round(difference).toLocaleString('tr-TR') + ' ' + unitSymbol;
                } else {
                    formattedDifference = difference.toLocaleString('tr-TR', {
                        minimumFractionDigits: decimalPlaces,
                        maximumFractionDigits: decimalPlaces
                    }) + ' ' + unitSymbol;
                }

                var differenceSpan = $('.difference-' + itemId);
                differenceSpan.text(formattedDifference);

                differenceSpan.removeClass('text-success text-danger');
                if (difference > 0) {
                    differenceSpan.addClass('text-success');
                } else if (difference < 0) {
                    differenceSpan.addClass('text-danger');
                }
            }

            function recalculateAllDifferences() {
                $('.counted-quantity').each(function() {
                    updateDifference($(this));
                });
            }

            $(document).on('input', 'input[name*="[notes]"]', function() {
                checkForChanges();
            });

            recalculateAllDifferences();
            initializeOriginalValues();

            $('#saveCountsButton').on('click', function() {
                saveChanges($(this));
            });

            function saveChanges(button) {
                checkForChanges();

                if (!hasChanges) {
                    Swal.fire({
                        icon: 'info',
                        title: '',
                        text: 'Kaydedilecek değişiklik bulunamadı.',
                        timer: 1500,
                        showConfirmButton: false
                    });
                    return;
                }

                button.prop('disabled', true)
                    .html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Kaydediliyor...');

                var formData = $('#countItemsForm').serialize();

                $.ajax({
                    url: "{{ route('backend.physical_count_save', $physicalCount->id) }}",
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                icon: 'success',
                                title: '',
                                text: response.message || 'Değişiklikler başarıyla kaydedildi.',
                                timer: 1500,
                                showConfirmButton: false
                            });

                            initializeOriginalValues();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Hata!',
                                text: response.message || 'Değişiklikler kaydedilirken bir hata oluştu.'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Hata:', xhr.responseText, status, error);
                        var errorMessage = 'Değişiklikler kaydedilirken bir hata oluştu.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Hata!',
                            text: errorMessage
                        });
                    },
                    complete: function() {
                        button.prop('disabled', false)
                            .html('Değişiklikleri Kaydet');
                    },
                    timeout: 3000
                });
            }

            $('#approveButton').on('click', function() {
                var button = $(this);
                var countId = button.data('id');
                var statusId = button.data('status');

                if (hasChanges) {
                    Swal.fire({
                        title: 'Kaydedilmemiş Değişiklikler',
                        text: 'Kaydedilmemiş değişiklikler var. Önce kaydetmek ister misiniz?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonText: 'Evet, kaydet',
                        cancelButtonText: 'Hayır, devam et'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            saveChanges($('#saveCountsButton'));
                            setTimeout(function() {
                                approveCount(button, countId, statusId);
                            }, 1000);
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            approveCount(button, countId, statusId);
                        }
                    });
                } else {
                    approveCount(button, countId, statusId);
                }
            });

            function approveCount(button, countId, statusId) {
                Swal.fire({
                    title: 'Sayımı Onayla',
                    text: 'Sayımı onaylamak istediğinize emin misiniz?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Evet, onayla',
                    cancelButtonText: 'İptal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        button.prop('disabled', true)
                            .html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Onaylanıyor...');

                        $.ajax({
                            url: "{{ route('backend.physical_count_status') }}",
                            type: 'POST',
                            data: {
                                id: countId,
                                status_id: statusId,
                                update_stock: false,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Başarılı!',
                                        text: response.message || 'Sayım başarıyla onaylandı.',
                                        timer: 1500,
                                        showConfirmButton: false
                                    }).then(() => {
                                        window.location.reload();
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Hata!',
                                        text: response.message || 'Sayım onaylanırken bir hata oluştu.'
                                    });
                                    button.prop('disabled', false)
                                        .html('<iconify-icon icon="mdi:check" class="me-1"></iconify-icon> Onayla');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Hata:', xhr.responseText, status, error);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Hata!',
                                    text: 'Sayım onaylanırken bir hata oluştu.'
                                });
                                button.prop('disabled', false)
                                    .html('<iconify-icon icon="mdi:check" class="me-1"></iconify-icon> Onayla');
                            }
                        });
                    }
                });
            }

            $('#refreshStockButton').on('click', function() {
                var button = $(this);
                var countId = button.data('id');

                Swal.fire({
                    title: 'Stokları Güncelle',
                    text: 'Stokları güncellemek istediğinize emin misiniz? İşlem sonrası sayfa yenilenecektir.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Evet, güncelle',
                    cancelButtonText: 'İptal',
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        button.prop('disabled', true)
                            .html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Güncelleniyor...');

                        $.ajax({
                            url: "{{ route('backend.physical_count_refresh_stock', $physicalCount->id) }}",
                            type: 'POST',
                            data: {
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Başarılı!',
                                        text: response.message || 'Stoklar başarıyla güncellendi.',
                                        timer: 1500,
                                        showConfirmButton: false
                                    }).then(() => {
                                        window.location.href = window.location.href.split('?')[0] + '?_=' + new Date().getTime();
                                    });
                                } else {
                                    console.error("Stok güncelleme hatası:", response);
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Hata!',
                                        text: response.message || 'Stoklar güncellenirken bir hata oluştu.'
                                    });
                                    button.prop('disabled', false)
                                        .html('<iconify-icon icon="mdi:refresh" class="me-1"></iconify-icon> Stokları Güncelle');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("AJAX hatası:", error);
                                console.error("Yanıt:", xhr.responseText);

                                var errorMessage = 'Stoklar güncellenirken bir hata oluştu.';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMessage = xhr.responseJSON.message;
                                }

                                Swal.fire({
                                    icon: 'error',
                                    title: 'Hata!',
                                    text: errorMessage
                                });

                                button.prop('disabled', false)
                                    .html('<iconify-icon icon="mdi:refresh" class="me-1"></iconify-icon> Stokları Güncelle');
                            },
                            timeout: 6000
                        });
                    }
                });
            });

            window.addEventListener('beforeunload', function(e) {
                if (hasChanges) {
                    e.preventDefault();
                    e.returnValue = 'Kaydedilmemiş değişiklikler var. Sayfadan ayrılmak istediğinize emin misiniz?';
                }
            });
        });

        var lastFocusedElement;

                $(document).on('click', '.notes-modal-trigger', function(e) {
            e.preventDefault();

            var $this = $(this);
            lastFocusedElement = $this[0];

            var itemId = $this.data('item-id');
            var notes = $this.data('notes');
            var productName = $this.data('product-name');
            var variantName = $this.data('variant-name');

            showNotesModal(itemId, notes, productName, variantName);
        });

        $(document).on('keydown', '.notes-modal-trigger', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
            }
        });

        function showNotesModal(itemId, notes, productName, variantName) {
            try {
                $('#modalProductName').text(productName);

                var $variantElement = $('#modalVariantName');
                if (variantName && variantName.trim() !== '') {
                    $variantElement.text(' - ' + variantName).show();
                } else {
                    $variantElement.text('').hide();
                }

                $('#modalNotesContent').text(notes);

                var modalEl = document.getElementById('notesModal');
                var modal = new bootstrap.Modal(modalEl, {
                    backdrop: true,
                    keyboard: true,
                    focus: false
                });
                modal.show();
            } catch (error) {
                console.error('Modal açma hatası:', error);
                try {
                    $('#notesModal').modal('show');
                } catch (fallbackError) {
                    alert('Not görüntülenirken bir hata oluştu: ' + error.message);
                }
            }
        }

        $(document).ready(function() {
            var $notesModal = $('#notesModal');

            $notesModal.on('shown.bs.modal', function() {
                setTimeout(function() {
                    $('#notesModalCloseFooterBtn').focus();
                }, 150);
            });

            $notesModal.on('hide.bs.modal', function() {
                if (document.activeElement && $notesModal[0].contains(document.activeElement)) {
                    document.activeElement.blur();
                }

                if (lastFocusedElement && document.contains(lastFocusedElement)) {
                    lastFocusedElement.focus();
                }
            });

            $notesModal.on('hidden.bs.modal', function() {
                if (lastFocusedElement && document.contains(lastFocusedElement)) {
                    setTimeout(function() {
                        if (lastFocusedElement && document.contains(lastFocusedElement)) {
                            lastFocusedElement.focus();
                        }
                        lastFocusedElement = null;
                    }, 50);
                } else {
                    lastFocusedElement = null;
                }
            });

            $('#notesModalCloseBtn, #notesModalCloseFooterBtn').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (lastFocusedElement && document.contains(lastFocusedElement)) {
                    lastFocusedElement.focus();
                }

                setTimeout(function() {
                    $notesModal.modal('hide');
                }, 10);
            });

            $notesModal.on('keydown', function(e) {
                if (e.key === 'Escape') {
                    e.preventDefault();

                    if (lastFocusedElement && document.contains(lastFocusedElement)) {
                        lastFocusedElement.focus();
                    }

                    setTimeout(function() {
                        $notesModal.modal('hide');
                    }, 10);
                    return;
                }

                if (e.key === 'Tab') {
                    var focusableElements = $(this).find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    var firstElement = focusableElements.first();
                    var lastElement = focusableElements.last();

                    if (e.shiftKey) {
                        if (document.activeElement === firstElement[0]) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else {
                        if (document.activeElement === lastElement[0]) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            });
        });
    </script>
@endsection
