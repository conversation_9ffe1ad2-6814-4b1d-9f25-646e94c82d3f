@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . $container->page . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date" filter-name="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label"><PERSON><PERSON><PERSON></label>
                    <input type="date" class="form-control" id="filter-end-date" filter-name="filter-end-date">
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Fiş No</th>
                            <th scope="col" class="text-center">Fiş Tarihi</th>
                            <th scope="col" class="text-center">Masraf Türü</th>
                            <th scope="col" class="text-center">Ödeme Türü</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        return $.extend({}, d, {
                            start_date: $('[filter-name="filter-start-date"]').val(),
                            end_date: $('[filter-name="filter-end-date"]').val(),
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'voucher_no',
                        name: 'voucher_no',
                        className: 'text-center',

                    },
                    {
                        data: 'voucher_date',
                        name: 'voucher_date',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                if (data) {
                                    var date = new Date(data);
                                    return date.toLocaleDateString('tr-TR');
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    {
                        data: 'expense_type.name',
                        name: 'expense_type.name',
                        className: 'text-center',
                    },
                    {
                        data: 'payment_type.name',
                        name: 'payment_type.name',
                        className: 'text-center',
                    },
                    {
                        data: 'grand_total',
                        name: 'grand_total',
                        className: 'text-center',
                    },
                    {
                        data: 'expense_voucher_statu.name',
                        name: 'expense_voucher_statu.name',
                        className: 'text-center',
                        render: function (data, type, row) {
                            let background = '';
                            let textColor = '';
                            let border = '';

                            switch (row.expense_voucher_statu.id) {
                                case 1:
                                    background = 'bg-warning-focus';
                                    textColor = 'text-warning-600';
                                    border = 'border-warning-main';
                                    break;
                                case 2:
                                    background = 'bg-success-focus';
                                    textColor = 'text-success-600';
                                    border = 'border-success-main';
                                    break;
                                case 3:
                                    background = 'bg-danger-focus';
                                    textColor = 'text-danger-600';
                                    border = 'border-danger-main';
                                    break;
                                default:
                                    background = 'bg-secondary';
                                    textColor = 'text-white';
                                    border = 'border-secondary';
                            }

                            return `<span class="${background} ${textColor} ${border} px-24 py-4 radius-4 fw-medium text-sm border">${data}</span>`;
                        }
                    },

                    {
                        render: function(data, type, row) {
                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.' . $container->page . '_detail') }}/${row.id}" class="bg-primary-light text-primary-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    }
                ],
                order: [
                    [1, 'desc']
                ],
                pageLength: 15,
            });

            // Cari, işlem tipi veya tarih değiştiğinde anlık filtreleme yap
            $('#filter-invoice-type, #filter-start-date, #filter-end-date').on('change',
                function() {
                    table.ajax.reload();
                });
            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

            $('[filter-name]').change(function() {
                $("[datatable] tbody").empty();
                $("[datatable]").DataTable().ajax.reload();
            });
        });
    </script>
@endsection
