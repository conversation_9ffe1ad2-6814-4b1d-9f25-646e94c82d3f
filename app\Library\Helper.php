<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\TempFile;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\FileBag;
use Illuminate\Support\Facades\File as FacadesFile;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphOneOrMany;
use Illuminate\Support\Facades\Date;

if (!function_exists('number')) {
    function number($param)
    {
        return number_format(intval($param), 0, '', '.');
    }
}

if (!function_exists('activeMenu')) {
    function activeMenu($groupArr)
    {

        if (Route::currentRouteName() == 'backend.' . $groupArr[0]) {
            return 'active open';
        }

        $temp = array();
        foreach ($groupArr as $value) {
            array_push($temp, 'backend.' . $value . '_list');
            array_push($temp, 'backend.' . $value . '_form');
            array_push($temp, 'backend.' . $value . '_show');
            array_push($temp, 'backend.' . $value . '_detail');
        }

        return in_array(Route::currentRouteName(), $temp) ? 'active open' : '';
    }
}

if (!function_exists('activeMenuItem')) {
    function activeMenuItem($groupArr)
    {
        $temp = array();
        foreach ($groupArr as $value) {
            array_push($temp, 'backend.' . $value . '_list');
            array_push($temp, 'backend.' . $value . '_form');
            array_push($temp, 'backend.' . $value . '_show');
            array_push($temp, 'backend.' . $value . '_detail');
        }

        return in_array(Route::currentRouteName(), $temp) ? 'active-page' : '';
    }
}

if (!function_exists('hasPermission')) {
    function hasPermission($route_names)
    {
        $onuser = Auth::user();
        $permissions = $onuser->role['permissions'] ?? null;
        if (!$permissions) {
            return false;
        }

        $permissions = json_decode($permissions, true);
        foreach ((array) $route_names as $route_name) {
            if (in_array($route_name, $permissions)) {
                return true;
            }

            foreach ($permissions as $permission) {
                if (str_starts_with($permission, "backend." . $route_name)) {
                    return true;
                }
            }
        }

        return false;
    }
}

if (!function_exists('phoneNumber')) {
    function phoneNumber($phone)
    {
        return  preg_replace("/[^0-9]/", "", $phone);
    }
}

if (!function_exists('str')) {
    function str($string = null)
    {
        if (func_num_args() === 0) {
            return new class
            {
                public function __call($method, $parameters)
                {
                    return Str::$method(...$parameters);
                }

                public function __toString()
                {
                    return '';
                }
            };
        }

        return Str::of($string);
    }
}

if (!function_exists('strSlugTr')) {
    function strSlugTr($str)
    {
        if (is_array($str)) {
            $str = implode(' ', $str);
        }

        $str = str_replace(
            ['Ç', 'ç', 'Ğ', 'ğ', 'ı', 'İ', 'Ö', 'ö', 'Ş', 'ş', 'Ü', 'ü'],
            ['C', 'c', 'G', 'g', 'i', 'I', 'O', 'o', 'S', 's', 'U', 'u'],
            $str
        );

        return Str::slug($str);
    }
}

if (!function_exists('tempFile')) {
    function tempFile(FileBag $files)
    {
        $request = request();
        $token = $request->header("X-CSRF-TOKEN");
        /** @var \Illuminate\Support\Collection<int, \App\Models\TempFile> $list */
        $list = collect();
        foreach ($files->all() as $file) {
            $uniqId = uniqid();
            $result = Storage::disk("temp_path")
                ->putFileAs(
                    'upload',
                    $file,
                    $uniqId
                );

            $list->push(TempFile::query()->create([
                "path" => $result,
                "token" => $token,
                "name" => $file->getClientOriginalName(),
                "fileable_name" => $request->get("fname", "file"),
                "type" => $file->getMimeType(),
                "fileable_type" => $request->get("ftype", "file"),
                "ip" => $request->ip(),
                "user_agent" => $request->userAgent(),
                "referer" => $request->headers->get("referer"),
                "origin" => $request->headers->get("origin"),
                "expires_at" => Carbon::now()->addMinutes(30),
            ]));
        }

        return $list;
    }
}

if (!function_exists('tempFileManager')) {
    function tempFileManager($token, Countable $tempFileIds, Model $model)
    {
        if (!method_exists($model, 'files')) return $model;

        if (!($model->files() instanceof MorphMany) and !($model->files() instanceof MorphOne) and !($model->files() instanceof MorphOneOrMany)) return $model;

        $temp_files = TempFile::query()->whereIn("id", $tempFileIds)->where("token", $token)->get();

        if ($temp_files->count() <= 0) return $model;
        $m_class = mb_split('\\\\', $model::class);

        $directory = public_path("upload/" . Str::lower(Arr::last($m_class)));
        $files = collect();

        if (!FacadesFile::exists($directory)) {
            FacadesFile::makeDirectory($directory, 0777, true);
        }


        //Move file to storage
        foreach ($temp_files as $temp_file) {
            $old_path = storage_path('temp/' . $temp_file->path);
            $new_file_path = Str::lower(Arr::last($m_class)) . "/" . str(now()->format("YmdHis") . "-" . $temp_file->name)->snake()->__toString();
            $new_path = $directory . "/" . str(now()->format("YmdHis") . "-" . $temp_file->name)->snake()->__toString();

            $r = FacadesFile::copy($old_path, $new_path);

            if ($r) {
                $model->files()->create([
                    "name" => $temp_file->fileable_name,
                    "type" => $temp_file->fileable_type,
                    "extension" => $temp_file->type,
                    "path" => $new_file_path,
                    "disk" => "public_path",
                ]);

                $temp_file->delete();
                FacadesFile::delete($old_path);
            }
        }
        return $model;
    }
}

if (!function_exists('fileManager')) {
    function fileManager(Model $model, \Symfony\Component\HttpFoundation\File\UploadedFile $file, $name = 'file', $disk = 'public_path')
    {
        if ($model->id == null) {
            return $model;
        }

        if (!method_exists($model, 'files')) {
            return $model;
        }

        if (!($model->files() instanceof MorphMany) and !($model->files() instanceof MorphOne) and !($model->files() instanceof MorphOneOrMany)) {
            return $model;
        }
        $m_class = mb_split('\\\\', $model::class);

        $file = Storage::disk($disk)
            ->putFileAs('upload/' . Str::lower(Arr::last($m_class)), $file, Str::snake(Date::now()->timestamp) . " " . $file->getClientOriginalName());

        $model->files()->create([
            'path' => $file,
            'name' => $name,
        ]);

        return $model;
    }
}

if (!function_exists('parseTrNumber')) {
    function parseTrNumber($value)
    {
        $value = str_replace('.', '', $value);
        $value = str_replace(',', '.', $value);
        return floatval($value);
    }
}

if (!function_exists('formatQuantity')) {
    function formatQuantity($quantity, $unit)
    {
        if (!$unit) {
            return number_format($quantity, 2, ',', '.');
        }

        if ($unit->allow_decimal == 0) {
            return number_format($quantity, 0, ',', '.');
        }

        $decimalPlaces = $unit->decimal_places ?? 4;
        return number_format($quantity, $decimalPlaces, ',', '.');
    }
}

if (!function_exists('formatQuantityWithUnit')) {
    function formatQuantityWithUnit($quantity, $unit)
    {
        if (!$unit) {
            return number_format($quantity, 2, ',', '.') . ' adet';
        }

        $formattedQuantity = formatQuantity($quantity, $unit);
        return $formattedQuantity . ' ' . $unit->symbol;
    }
}

// Para formatlaması helper fonksiyonları
if (!function_exists('formatMoney')) {
    /**
     * Para formatla - ayara göre ondalık basamak sayısı
     */
    function formatMoney($amount, $currency = 'TL')
    {
        return Setting::formatMoney($amount, $currency);
    }
}

if (!function_exists('formatMoneyNumber')) {
    /**
     * Para formatla (sadece sayı) - ayara göre ondalık basamak sayısı
     */
    function formatMoneyNumber($amount)
    {
        return Setting::formatMoneyNumber($amount);
    }
}

if (!function_exists('getDecimalPlaces')) {
    /**
     * Sistem ayarından ondalık basamak sayısını getir
     */
    function getDecimalPlaces()
    {
        return Setting::getDecimalPlaces();
    }
}


