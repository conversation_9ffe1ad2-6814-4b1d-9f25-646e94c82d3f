<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Spatie\Activitylog\LogOptions;

class Invoice extends BaseModel
{
    use SoftDeletes;

    protected $table = 'invoices';

    protected $guarded = [];

    protected $casts = [
        'invoice_date' => 'datetime',
        'due_date' => 'datetime',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }
    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }

    public function stock()
    {
        return $this->hasMany(Product::class, 'product_id', 'id');
    }

    public function balance()
    {
        return $this->hasMany(Balance::class);
    }
    public function balanceCurrencies()
    {
        return $this->hasMany(BalanceCurrency::class, 'balance_id', 'id');
    }
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }
    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }
    public function invoiceStatus()
    {
        return $this->belongsTo(InvoiceStatus::class, 'invoice_status_id');
    }

    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class);
    }
    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Stok hareket filtreleme scope'u
    public function scopeStockMovementFilter($query, $request)
    {
        // Temel filtreleri uygula
        $query = $this->scopeFinancialTransactionFilter($query, $request);

        // Ürün filtresi - invoice_items tablosunda arama yap
        if ($request->product_id) {
            $query->whereHas('invoiceItems', function ($q) use ($request) {
                $q->where('product_id', $request->product_id);
            });
        }

        return $query;
    }
    public function scopeFinancialTransactionFilter($query, $filter)
    {
        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }

        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('invoice_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        }

        return $query;
    }

    // Stok hareket verilerini formatla
    public function getStockMovementItems($productId = null)
    {
        $query = $this->invoiceItems();

        if ($productId) {
            $query->where('product_id', $productId);
        }

        $invoiceItems = $query->get();

        // Ürün ID'lerini topla ve ürün bilgilerini getir
        $productIds = $invoiceItems->pluck('product_id')->filter()->unique()->values();
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

        return $invoiceItems->map(function ($item) use ($products) {
            $productId = $item->product_id;
            $product = $products->get($productId);

            $transactionType = '';
            $transactionTypeName = '';
            
            switch($this->invoiceType?->id) {
                case 1:
                    $transactionType = 'purchase';
                    $transactionTypeName = 'Alış';
                    break;
                case 2:
                    $transactionType = 'sales';
                    $transactionTypeName = 'Satış';
                    break;
                case 3:
                    $transactionType = 'purchase_return';
                    $transactionTypeName = 'Alış İade';
                    break;
                case 4:
                    $transactionType = 'sales_return';
                    $transactionTypeName = 'Satış İade';
                    break;
                default:
                    $transactionType = 'other';
                    $transactionTypeName = 'Diğer';
            }

            // Ürün adı ve varyant adı birleştirme
            $productName = $product ? $product->name : $item->product_name;
            if ($item->product_variant_id) {
                $variant = ProductVariant::find($item->product_variant_id);
                if ($variant && $variant->name) {
                    $productName .= ' / ' . $variant->name;
                }
            }

            return [
                'invoice_id' => $this->id,
                'invoice_no' => $this->invoice_no,
                'invoice_date' => $this->invoice_date->format('d.m.Y'),
                'current_id' => $this->current_id,
                'current_name' => $this->current->title ?? 'Bilinmeyen Cari',
                'product_id' => $productId,
                'product_name' => $productName,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'total' => $item->total_price,
                'transaction_type' => $transactionType,
                'transaction_type_name' => $transactionTypeName
            ];
        });
    }
    // Önizleme fatura numarası oluştur (counter'ı artırmadan)
    public static function generatePreviewInvoiceNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');

        $lastInvoice = Invoice::withTrashed() // <--- Silinen kayıtlar da dahil edilir
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('invoice_no', 'like', "FTR/{$month}/{$year}/%")
            ->orderBy('invoice_no', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumberPart = substr($lastInvoice->invoice_no, strrpos($lastInvoice->invoice_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "FTR/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
    public static function generatePreviewPurchesInvoiceNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');
        // Burada fatura numarası "FTR/MM/YYYY/0000001" formatında olduğu varsayılıyor
        $lastInvoice = Invoice::withTrashed()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('invoice_no', 'like', "ALF/{$month}/{$year}/%")
            ->orderBy('invoice_no', 'desc')
            ->first();

        if ($lastInvoice) {
            // Son numarayı parçala ve 1 ekle
            $lastNumberPart = substr($lastInvoice->invoice_no, strrpos($lastInvoice->invoice_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "ALF/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
    public static function bootHook()
    {
        static::deleting(function ($invoice) {
            // Eğer soft delete (force delete değilse)
            if (! $invoice->isForceDeleting()) {

                // Durum kontrolü: 2, 4, 5 ise silme işlemi iptal edilir
                if (in_array($invoice->invoice_status_id, [2, 4, 5])) {
                    return false; // Silme işlemi gerçekleşmez
                }

                // Eğer durum 1 veya 3 ise balance'dan total_amount'ı geri al
                if (in_array($invoice->invoice_status_id, [1, 3])) {
                    $current = $invoice->current;
                    if ($current && $invoice->total_amount > 0) {
                        $balance = $current->balance()->where('current_id', $current->id)->first();
                        // Döviz kodu ve kuru exchange_rate_id üzerinden alınacak
                        $exchangeRate = $invoice->exchangeRate;
                        $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
                        $exchangeRateValue = $exchangeRate ? $exchangeRate->selling_rate : 1.0000;
                        $amount = $invoice->total_amount;
                        $amountFX = $currencyCode === 'TRY' ? $amount : ($exchangeRateValue > 0 ? $amount / $exchangeRateValue : $amount);
                        if ($balance) {
                            // Fatura tipine göre balance güncelleme
                            if ($invoice->invoice_type_id == 1) {
                                // Alış faturası (invoice_type_id = 1) - credit_balance'dan çıkar
                                if ($currencyCode === 'TRY') {
                                    $balance->decrement('credit_balance', $amount);
                                    // --- EKLENDİ: balance_currencies tablosunda TRY satırından da düş ---
                                    $balanceCurrencyTRY = $balance->balanceCurrencies()->where('currency_code', 'TRY')->where('current_id', $current->id)->first();
                                    if ($balanceCurrencyTRY) {
                                        $balanceCurrencyTRY->decrement('credit_balance', $amount);
                                        $balanceCurrencyTRY->save();
                                    }
                                    // --- SON ---
                                } else {
                                    $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $currencyCode)->where('current_id', $current->id)->first();
                                    if ($balanceCurrency) {
                                        $balanceCurrency->decrement('credit_balance', $amountFX);
                                        $balanceCurrency->save();
                                    }
                                }
                            } else {
                                // Satış faturası (invoice_type_id = 2) - debit_balance'dan çıkar
                                if ($currencyCode === 'TRY') {
                                    $balance->decrement('debit_balance', $amount);
                                    // --- EKLENDİ: balance_currencies tablosunda TRY satırından da düş ---
                                    $balanceCurrencyTRY = $balance->balanceCurrencies()->where('currency_code', 'TRY')->where('current_id', $current->id)->first();
                                    if ($balanceCurrencyTRY) {
                                        $balanceCurrencyTRY->decrement('debit_balance', $amount);
                                        $balanceCurrencyTRY->save();
                                    }
                                    // --- SON ---
                                } else {
                                    $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $currencyCode)->where('current_id', $current->id)->first();
                                    if ($balanceCurrency) {
                                        $balanceCurrency->decrement('debit_balance', $amountFX);
                                        $balanceCurrency->save();
                                    }
                                }
                            }
                            $balance->save();
                        }
                    }
                    $invoice->deleteRelatedStockMovements();
                    $invoice->deleteRelatedStockReservations();
                }

                // Eğer durum 1 veya 3 ise ilişkili invoiceItem'lar soft delete edilir
                $invoice->invoiceItems()->delete();
            }
        });
    }

    // --- EKLENDİ: İlgili stok hareketlerini ve item'larını soft delete yapan fonksiyon ---
    public function deleteRelatedStockMovements()
    {
        // Sadece bu faturaya ait stock movement'ları bul
        $stockMovements = StockMovement::where('invoice_id', $this->id)->get();
        foreach ($stockMovements as $stockMovement) {
            $stockMovement->items()->delete(); // Soft delete StockMovementItems
            $stockMovement->delete();          // Soft delete StockMovement
        }
    }
    public function deleteRelatedStockReservations()
    {
        $stockReservations = StockReservation::where('invoice_id', $this->id)->get();
        foreach ($stockReservations as $stockReservation) {
            $stockReservation->delete();
        }
    }
    // Silme kontrolü için static metod
    public static function canDelete($invoice)
    {
        // Durum kontrolü: 2, 4, 5 ise silme işlemi iptal edilir
        if (in_array($invoice->invoice_status_id, [2, 4, 5])) {
            return false; // Silme işlemi gerçekleşmez
        }

        return true; // 1 veya 3 durumunda silinebilir
    }

    public function paymentTerm()
    {
        return $this->belongsTo(PaymentTerm::class);
    }
    public static function generatePreviewReturnPurchaseInvoiceNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');
        // Burada fatura numarası "FTR/MM/YYYY/0000001" formatında olduğu varsayılıyor
        $lastInvoice = Invoice::withTrashed()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('invoice_no', 'like', "ALIF/{$month}/{$year}/%")
            ->orderBy('invoice_no', 'desc')
            ->first();

        if ($lastInvoice) {
            // Son numarayı parçala ve 1 ekle
            $lastNumberPart = substr($lastInvoice->invoice_no, strrpos($lastInvoice->invoice_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "ALIF/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
    public static function generatePreviewReturnSaleInvoiceNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');
        // Burada fatura numarası "FTR/MM/YYYY/0000001" formatında olduğu varsayılıyor
        $lastInvoice = Invoice::withTrashed()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('invoice_no', 'like', "FITR/{$month}/{$year}/%")
            ->orderBy('invoice_no', 'desc')
            ->first();

        if ($lastInvoice) {
            // Son numarayı parçala ve 1 ekle
            $lastNumberPart = substr($lastInvoice->invoice_no, strrpos($lastInvoice->invoice_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "FITR/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
}
