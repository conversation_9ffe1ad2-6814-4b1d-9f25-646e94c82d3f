<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductVariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createProductVariants();
        });

        $this->command->info('Product varyant verileri başarıyla oluşturuldu.');
    }

    /**
     * Ürün varyantlarını oluştur
     */
    private function createProductVariants(): void
    {
        // Telefonlar için varyantlar
        $this->createPhoneVariants();

        // Bilgisayarlar için varyantlar
        $this->createComputerVariants();

        // Giyim ürünleri için varyantlar
        $this->createClothingVariants();

        // Bisikletler için varyantlar
        $this->createBicycleVariants();

        // Ev eşyaları için varyantlar
        $this->createHomeApplianceVariants();

        // Gıda ürünleri için varyantlar (genelde tek varyant)
        $this->createFoodVariants();

        // Kozmetik ürünleri için varyantlar
        $this->createCosmeticVariants();
    }

    /**
     * Telefon varyantları oluştur
     */
    private function createPhoneVariants(): void
    {
        // iPhone 15 Pro Max varyantları
        $iphone = Product::where('sku', 'LIKE', '%IPHONE%')->first();
        if ($iphone) {
            $colors = ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'];
            $storages = ['256GB', '512GB', '1TB'];

            foreach ($colors as $color) {
                foreach ($storages as $storage) {
                    $variantName = "{$color} - {$storage}";
                    $priceMultiplier = 1;

                    // Depolama alanına göre fiyat artışı
                    if ($storage === '512GB') $priceMultiplier = 1.15;
                    if ($storage === '1TB') $priceMultiplier = 1.35;

                    $this->createVariant($iphone, $variantName, [
                        'purchase_price' => $iphone->purchase_price * $priceMultiplier,
                        'sale_price' => $iphone->sale_price * $priceMultiplier,
                    ]);
                }
            }
        }

        // Samsung Galaxy S24 Ultra varyantları
        $samsung = Product::where('sku', 'LIKE', '%GALAXY%')->first();
        if ($samsung) {
            $colors = ['Titanium Gray', 'Titanium Black', 'Titanium Violet', 'Titanium Yellow'];
            $storages = ['256GB', '512GB', '1TB'];

            foreach ($colors as $color) {
                foreach ($storages as $storage) {
                    $variantName = "{$color} - {$storage}";
                    $priceMultiplier = 1;

                    if ($storage === '512GB') $priceMultiplier = 1.12;
                    if ($storage === '1TB') $priceMultiplier = 1.30;

                    $this->createVariant($samsung, $variantName, [
                        'purchase_price' => $samsung->purchase_price * $priceMultiplier,
                        'sale_price' => $samsung->sale_price * $priceMultiplier,
                    ]);
                }
            }
        }
    }

    /**
     * Bilgisayar varyantları oluştur
     */
    private function createComputerVariants(): void
    {
        // MacBook Pro varyantları
        $macbook = Product::where('sku', 'LIKE', '%MACBOOK%')->first();
        if ($macbook) {
            $configs = [
                ['cpu' => 'M3', 'ram' => '18GB', 'storage' => '512GB', 'multiplier' => 1.0],
                ['cpu' => 'M3 Pro', 'ram' => '18GB', 'storage' => '512GB', 'multiplier' => 1.15],
                ['cpu' => 'M3 Pro', 'ram' => '36GB', 'storage' => '1TB', 'multiplier' => 1.35],
                ['cpu' => 'M3 Max', 'ram' => '36GB', 'storage' => '1TB', 'multiplier' => 1.55],
                ['cpu' => 'M3 Max', 'ram' => '48GB', 'storage' => '2TB', 'multiplier' => 1.85],
            ];

            $colors = ['Space Black', 'Silver'];

            foreach ($configs as $config) {
                foreach ($colors as $color) {
                    $variantName = "{$config['cpu']} {$config['ram']} {$config['storage']} - {$color}";

                    $this->createVariant($macbook, $variantName, [
                        'purchase_price' => $macbook->purchase_price * $config['multiplier'],
                        'sale_price' => $macbook->sale_price * $config['multiplier'],
                    ]);
                }
            }
        }

        // ASUS ROG Strix varyantları
        $asus = Product::where('sku', 'LIKE', '%ASUS%ROG%')->first();
        if ($asus) {
            $configs = [
                ['gpu' => 'RTX 4060', 'ram' => '16GB', 'storage' => '512GB SSD', 'multiplier' => 1.0],
                ['gpu' => 'RTX 4070', 'ram' => '16GB', 'storage' => '1TB SSD', 'multiplier' => 1.25],
                ['gpu' => 'RTX 4080', 'ram' => '32GB', 'storage' => '1TB SSD', 'multiplier' => 1.60],
            ];

            foreach ($configs as $config) {
                $variantName = "{$config['gpu']} {$config['ram']} {$config['storage']}";

                $this->createVariant($asus, $variantName, [
                    'purchase_price' => $asus->purchase_price * $config['multiplier'],
                    'sale_price' => $asus->sale_price * $config['multiplier'],
                ]);
            }
        }
    }

    /**
     * Giyim varyantları oluştur
     */
    private function createClothingVariants(): void
    {
        // Nike Air Max varyantları
        $nikeShoes = Product::where('sku', 'LIKE', '%NIKE%AIR%')->first();
        if ($nikeShoes) {
            $colors = ['Siyah/Beyaz', 'Beyaz/Siyah', 'Kırmızı/Beyaz', 'Mavi/Beyaz'];
            $sizes = ['38', '39', '40', '41', '42', '43', '44', '45'];

            foreach ($colors as $color) {
                foreach ($sizes as $size) {
                    $variantName = "{$color} - Numara {$size}";

                    $this->createVariant($nikeShoes, $variantName);
                }
            }
        }

        // Adidas Ultraboost varyantları
        $adidasShoes = Product::where('sku', 'LIKE', '%ADIDAS%ULTRA%')->first();
        if ($adidasShoes) {
            $colors = ['Core Black', 'Cloud White', 'Solar Red', 'Night Navy'];
            $sizes = ['38', '39', '40', '41', '42', '43', '44', '45', '46'];

            foreach ($colors as $color) {
                foreach ($sizes as $size) {
                    $variantName = "{$color} - Numara {$size}";

                    $this->createVariant($adidasShoes, $variantName);
                }
            }
        }

        // Zara Blazer varyantları
        $blazer = Product::where('sku', 'LIKE', '%BLAZER%')->first();
        if ($blazer) {
            $colors = ['Siyah', 'Lacivert', 'Bej', 'Gri'];
            $sizes = ['XS', 'S', 'M', 'L', 'XL'];

            foreach ($colors as $color) {
                foreach ($sizes as $size) {
                    $variantName = "{$color} - Beden {$size}";

                    $this->createVariant($blazer, $variantName);
                }
            }
        }

        // H&M Pantolon varyantları
        $pants = Product::where('sku', 'LIKE', '%PANTOLON%')->first();
        if ($pants) {
            $colors = ['Siyah', 'Lacivert', 'Gri', 'Haki'];
            $sizes = ['28/30', '30/30', '30/32', '32/32', '32/34', '34/32', '34/34', '36/32', '36/34'];

            foreach ($colors as $color) {
                foreach ($sizes as $size) {
                    $variantName = "{$color} - Beden {$size}";

                    $this->createVariant($pants, $variantName);
                }
            }
        }
    }

    /**
     * Bisiklet varyantları oluştur
     */
    private function createBicycleVariants(): void
    {
        // Bianchi yol bisikleti varyantları
        $bianchi = Product::where('sku', 'LIKE', '%BIANCHI%')->first();
        if ($bianchi) {
            $sizes = ['47cm', '50cm', '53cm', '55cm', '57cm', '59cm'];
            $colors = ['Celeste', 'Black Matt', 'White Glossy'];

            foreach ($sizes as $size) {
                foreach ($colors as $color) {
                    $variantName = "{$size} - {$color}";

                    $this->createVariant($bianchi, $variantName);
                }
            }
        }

        // Giant dağ bisikleti varyantları
        $giant = Product::where('sku', 'LIKE', '%GIANT%TALON%')->first();
        if ($giant) {
            $sizes = ['S', 'M', 'L', 'XL'];
            $colors = ['Metallic Black', 'Concrete', 'Iris'];

            foreach ($sizes as $size) {
                foreach ($colors as $color) {
                    $variantName = "Beden {$size} - {$color}";

                    $this->createVariant($giant, $variantName);
                }
            }
        }

        // Trek şehir bisikleti varyantları
        $trek = Product::where('sku', 'LIKE', '%TREK%FX%')->first();
        if ($trek) {
            $sizes = ['S', 'M', 'L', 'XL'];
            $colors = ['Lithium Grey', 'Viper Red', 'Alpine Blue'];

            foreach ($sizes as $size) {
                foreach ($colors as $color) {
                    $variantName = "Beden {$size} - {$color}";

                    $this->createVariant($trek, $variantName);
                }
            }
        }
    }

    /**
     * Ev eşyaları varyantları oluştur
     */
    private function createHomeApplianceVariants(): void
    {
        // IKEA kanepe varyantları
        $sofa = Product::where('sku', 'LIKE', '%EKTORP%')->first();
        if ($sofa) {
            $colors = ['Hallarp Bej', 'Lofallet Bej', 'Virestad Kırmızı', 'Hillared Antrasit'];

            foreach ($colors as $color) {
                $variantName = $color;

                $this->createVariant($sofa, $variantName);
            }
        }

        // Bosch bulaşık makinesi varyantları
        $bosch = Product::where('sku', 'LIKE', '%BOSCH%')->first();
        if ($bosch) {
            $models = [
                ['model' => 'SMV46KX01E', 'capacity' => '13 Kişilik', 'multiplier' => 1.0],
                ['model' => 'SMV68TX06E', 'capacity' => '14 Kişilik', 'multiplier' => 1.15],
                ['model' => 'SMS88TI01E', 'capacity' => '14 Kişilik', 'multiplier' => 1.25],
            ];

            foreach ($models as $model) {
                $variantName = "{$model['model']} - {$model['capacity']}";

                $this->createVariant($bosch, $variantName, [
                    'purchase_price' => $bosch->purchase_price * $model['multiplier'],
                    'sale_price' => $bosch->sale_price * $model['multiplier'],
                ]);
            }
        }

        // Arçelik buzdolabı varyantları
        $arcelik = Product::where('sku', 'LIKE', '%ARÇEL%BUZDO%')->first();
        if ($arcelik) {
            $models = [
                ['model' => '574561 EI', 'capacity' => '574L', 'color' => 'Inox', 'multiplier' => 1.0],
                ['model' => '584611 EI', 'capacity' => '584L', 'color' => 'Inox', 'multiplier' => 1.08],
                ['model' => '591620 MB', 'capacity' => '591L', 'color' => 'Siyah', 'multiplier' => 1.12],
            ];

            foreach ($models as $model) {
                $variantName = "{$model['model']} - {$model['capacity']} {$model['color']}";

                $this->createVariant($arcelik, $variantName, [
                    'purchase_price' => $arcelik->purchase_price * $model['multiplier'],
                    'sale_price' => $arcelik->sale_price * $model['multiplier'],
                ]);
            }
        }
    }

    /**
     * Gıda varyantları oluştur
     */
    private function createFoodVariants(): void
    {
        // Nestle Fitness varyantları
        $fitness = Product::where('sku', 'LIKE', '%FITNESS%')->first();
        if ($fitness) {
            $types = [
                ['name' => 'Original', 'weight' => '400g'],
                ['name' => 'Çikolatalı', 'weight' => '400g'],
                ['name' => 'Meyveli', 'weight' => '400g'],
                ['name' => 'Yoğurtlu', 'weight' => '400g'],
            ];

            foreach ($types as $type) {
                $variantName = "{$type['name']} - {$type['weight']}";

                $this->createVariant($fitness, $variantName);
            }
        }

        // Ülker Çikolatalı Gofret varyantları
        $ulker = Product::where('sku', 'LIKE', '%ÜLKER%GOFRET%')->first();
        if ($ulker) {
            $types = [
                ['name' => 'Sütlü Çikolata', 'pack' => 'Tekli'],
                ['name' => 'Bitter Çikolata', 'pack' => 'Tekli'],
                ['name' => 'Beyaz Çikolata', 'pack' => 'Tekli'],
                ['name' => 'Sütlü Çikolata', 'pack' => '5\'li Paket'],
            ];

            foreach ($types as $type) {
                $variantName = "{$type['name']} - {$type['pack']}";
                $multiplier = $type['pack'] === '5\'li Paket' ? 4.5 : 1;

                $this->createVariant($ulker, $variantName, [
                    'purchase_price' => $ulker->purchase_price * $multiplier,
                    'sale_price' => $ulker->sale_price * $multiplier,
                ]);
            }
        }
    }

    /**
     * Kozmetik varyantları oluştur
     */
    private function createCosmeticVariants(): void
    {
        // L'Oreal krem varyantları
        $loreal = Product::where('sku', 'LIKE', '%LOREAL%REVITALIFT%')->first();
        if ($loreal) {
            $types = [
                ['type' => 'Gündüz Kremi', 'spf' => 'SPF 30', 'size' => '50ml'],
                ['type' => 'Gece Kremi', 'spf' => null, 'size' => '50ml'],
                ['type' => 'Göz Kremi', 'spf' => null, 'size' => '15ml'],
            ];

            foreach ($types as $type) {
                $variantName = $type['type'];
                if ($type['spf']) {
                    $variantName .= " {$type['spf']}";
                }
                $variantName .= " - {$type['size']}";

                $multiplier = $type['size'] === '15ml' ? 0.7 : 1;

                $this->createVariant($loreal, $variantName, [
                    'purchase_price' => $loreal->purchase_price * $multiplier,
                    'sale_price' => $loreal->sale_price * $multiplier,
                ]);
            }
        }

        // Maybelline fondöten varyantları
        $maybelline = Product::where('sku', 'LIKE', '%MAYBELLINE%FIT%')->first();
        if ($maybelline) {
            $shades = [
                ['code' => '110', 'name' => 'Porcelain'],
                ['code' => '115', 'name' => 'Ivory'],
                ['code' => '120', 'name' => 'Classic Ivory'],
                ['code' => '125', 'name' => 'Nude Beige'],
                ['code' => '130', 'name' => 'Buff Beige'],
                ['code' => '220', 'name' => 'Natural Beige'],
                ['code' => '228', 'name' => 'Soft Tan'],
                ['code' => '230', 'name' => 'Natural Buff'],
            ];

            foreach ($shades as $shade) {
                $variantName = "{$shade['code']} {$shade['name']}";

                $this->createVariant($maybelline, $variantName);
            }
        }
    }

    /**
     * Hacim hesapla (m³)
     */
    private function calculateVolume(?float $width, ?float $height, ?float $length): ?float
    {
        if ($width === null || $height === null || $length === null) {
            return null;
        }

        // Hacim = genişlik × yükseklik × uzunluk / 1.000.000 (cm³'ten m³'e çevrim)
        return ($width * $height * $length) / 1000000;
    }

    /**
     * Varyant oluştur
     */
    private function createVariant(Product $product, string $variantName, array $options = []): void
    {
        $sku = ProductVariant::generateSku($product->id, $variantName);

        $variantData = [
            'product_id' => $product->id,
            'name' => $variantName,
            'sku' => $sku,
            'description' => "{$product->name} - {$variantName}",
            'purchase_price' => $options['purchase_price'] ?? $product->purchase_price,
            'sale_price' => $options['sale_price'] ?? $product->sale_price,
            'purchase_currency_code' => $product->purchase_currency_code,
            'sale_currency_code' => $product->sale_currency_code,
            'critical_stock_level' => $product->critical_stock_level,
            'weight' => $product->weight,
            'width' => $product->width,
            'height' => $product->height,
            'length' => $product->length,
            'volume' => $this->calculateVolume($product->width, $product->height, $product->length),
            'is_active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ];

        ProductVariant::updateOrCreate(
            ['sku' => $sku],
            $variantData
        );
    }
}
