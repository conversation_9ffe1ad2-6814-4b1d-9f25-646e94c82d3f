@extends('layout.layout')

@php
$title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
$subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON>üzenle');
$tryRate = $exchangeRate->where('code', 'TRY')->first();
$hasLockedProducts = isset($item->invoice_status_id) && in_array($item->invoice_status_id, [2, 4, 5]);
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">{{ $container->title }} Fatura</h5>
            </div>
            <div class="card-body">
                <form id="invoiceForm" action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                    method="POST">
                    @csrf
                    <!-- <PERSON><PERSON> hesap ID'si -->
                    <input type="hidden" name="invoice_type_id" value="2">
                    <input type="hidden" id="js_invoice_no" value="{{ $item->invoice_no }}">
                    <input type="hidden" name="net_amount_fx" id="net_amount_fx_hidden">
                    <input type="hidden" name="tax_amount_fx" id="tax_amount_fx_hidden">
                    <input type="hidden" name="total_amount_fx" id="total_amount_fx_hidden">
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <h6 class="mb-0">Fatura Ekle</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse"
                                    data-bs-target="#faturaBilgileri" aria-expanded="true"
                                    aria-controls="faturaBilgileri">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="faturaBilgileri">
                                <div class="col-12 col-md-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Fatura Bilgileri</h6>
                                            <div class="row gy-3">
                                                <!-- Fatura No -->
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Fatura No</label>
                                                    <input type="text" class="form-control" id="invoice_no" name="invoice_no"
                                                        value="{{ $item->invoice_no }}" readonly>
                                                    <x-form-error field="invoice_no" />
                                                </div>
                                                <!-- Depo Seçimi -->
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Depo</label>
                                                    <select class="form-select select2" name="warehouse_id" id="warehouse_id">
                                                        <option value="">Depo Seçiniz</option>
                                                        @foreach($warehouse as $wh)
                                                        <option value="{{ $wh->id }}" {{ (old('warehouse_id', $item->
                                                            warehouse_id ?? '') == $wh->id) ? 'selected' : '' }}>{{ $wh->name }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="warehouse_id" />
                                                </div>
                                                <!-- Fatura Tarihi -->
                                                <div class="col-12 col-lg-6">
                                                    <label class="form-label">Fatura Tarihi</label>
                                                    <input type="datetime-local" class="form-control" name="invoice_date" id="invoice_date"
                                                        value="{{ old('invoice_date', $item->id && $item->invoice_date ? date('Y-m-d\\TH:i', strtotime($item->invoice_date)) : '') }}">
                                                    <x-form-error field="invoice_date" />
                                                </div>
                                                <!-- Vade Tarihi -->
                                                <div class="col-12 col-lg-6">
                                                    <label class="form-label">Vade Tarihi</label>
                                                    <input class="form-control radius-8 bg-base" name="due_date" id="due_date"
                                                        type="datetime-local"
                                                        value="{{ old('due_date', $item->id && $item->due_date ? date('Y-m-d\\TH:i', strtotime($item->due_date)) : '') }}">
                                                    <x-form-error field="due_date" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Vade Tanımı</label>
                                                    <select class="form-select select2" name="payment_term_id" id="payment_term_id">
                                                        <option value="">Vade Tanımı Seçiniz...</option>
                                                        @if(isset($paymentTerms))
                                                            @foreach ($paymentTerms as $term)
                                                                <option value="{{ $term->id }}" data-day-count="{{ $term->day_count }}"
                                                                    {{ old('payment_term_id', $item->payment_term_id ?? '') == $term->id ? 'selected' : '' }}>
                                                                    {{ $term->day_count }} Gün {{ $term->is_active ? '' : '(Pasif)' }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Döviz</label>
                                                    <div class="input-group">
                                                        <select class="form-select select2" name="exchange_rate_id"
                                                            id="exchange_rate_id">
                                                            <option value="">Döviz Seçiniz...</option>
                                                            @foreach ($exchangeRate as $rate)
                                                            <option value="{{ $rate->id }}"
                                                                data-currency-code="{{ $rate->code }}"
                                                                data-selling-rate="{{ $rate->selling_rate }}"
                                                                data-symbol="{{ $rate->symbol }}" {{ old('exchange_rate_id',
                                                                $item->exchange_rate_id) == $rate->id ? 'selected' :
                                                                ($rate->code == 'TRY' && !old('exchange_rate_id') &&
                                                                !$item->exchange_rate_id ? 'selected' : '') }}>
                                                                {{ $rate->code }} ({{ $rate->symbol }}) = {{ $rate->selling_rate }}
                                                            </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <x-form-error field="exchange_rate_id" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-8">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Cari Hesap Bilgileri</h6>
                                            <div class="row g-3">
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Cari Kodu-Unvanı</label>
                                                    <select class="form-select select2" name="current_id" id="current_id">
                                                        <option value="">Cari Seçiniz...</option>
                                                        @foreach ($current as $c)
                                                        <option value="{{ $c->id }}" data-current-code="{{ $c->title }}"
                                                            data-address="{{ $c->address ?? '' }}, {{ $c->district->name ?? '' }}, {{ $c->city->name ?? '' }}, {{ $c->country->name ?? '' }}"
                                                            title="{{ $c->title }} - {{ $c->current_type_id }}" {{
                                                            old('current_id', $item->current_id) == $c->id ? 'selected' : '' }}
                                                            {{ $c->deleted_at ? 'style=color:red;font-style:italic;' : '' }}>
                                                            {{ $c->title }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="current_id" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Cari Adresi</label>
                                                    <input type="text" class="form-control" id="address" readonly>
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Sevkiyat Adres</label>
                                                    <input type="text" class="form-control" name="shipping_address"
                                                        id="shipping_address"
                                                        value="{{ old('shipping_address', $item->shipping_address ?? '') }}">
                                                    <x-form-error field="shipping_address" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Ödeme Planı</label>
                                                    <select class="form-select select2" name="payment_type_id"
                                                        id="payment_type_id">
                                                        <option value="">Ödeme Planı Seçiniz...</option>
                                                        @foreach ($paymentType as $plan)
                                                        <option value="{{ $plan->id }}"
                                                            data-branch-id="{{ $plan->branch_id ?? '' }}" {{--
                                                            data-branch-id="{{ $plan->branch_id ?? '' }}" --}} {{
                                                            old('payment_type_id', $item->payment_type_id) == $plan->id ?
                                                            'selected' : '' }}>
                                                            {{ $plan->name }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="payment_type_id" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <h6 class="mb-0">Ürünler</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse"
                                    data-bs-target="#ürünler" aria-expanded="true" aria-controls="ürünler">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="ürünler">
                                @include('backend.invoices.partials.product_rows', ['products' => $products, 'exchangeRates' =>
                                $exchangeRate])
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <button type="button" class="btn btn-sm btn-outline-primary ms-auto" data-bs-toggle="collapse"
                                    data-bs-target="#açıklama" aria-expanded="true" aria-controls="açıklama">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="açıklama">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <label class="form-label">Açıklama</label>
                                            <div class="row g-2">
                                                <div class="col-12">
                                                    <textarea class="form-control" name="description" rows="5"
                                                        placeholder="Açıklamanızı yazın...">{{ old('description', $item->description ?? '') }}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Alt Toplamlar</h6>
                                            <div class="row g-2">
                                                <div class="col-4 text-strat">Toplam</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end" id="net_amount"
                                                            name="net_amount"
                                                            value="{{ old('net_amount', $item->net_amount ?? '0,00') }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_total"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end" id="net_amount_fx"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_net_amount_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-strat">KDV Tutarı</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end" id="tax_amount"
                                                            name="tax_amount"
                                                            value="{{ old('tax_amount', $item->tax_amount ?? '0,00') }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_vat"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end" id="tax_amount_fx"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_vat_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-strat">KDV Dahil Toplam</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end" id="total_amount"
                                                            name="total_amount"
                                                            value="{{ old('total_amount', $item->total_amount ?? '0,00') }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_total_amount"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end" id="total_amount_fx"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_total_amount_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 text-end"> <a href="{{ route('backend.' . $container->page . '_list') }}"
                            class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>İptal
                        </a>
                        <button type="submit" class="btn btn-primary" id="invoiceSaveBtn">
                            <i class="fas fa-save me-1"></i> Kaydet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="stockProductModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen-lg-down modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Stok Ürünleri</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table id="stockProductModalTable" datatable class="table bordered-table mb-0 dataTable"
                        width="100%">
                        <thead>
                            <tr>
                                <th scope="col">
                                    <input type="checkbox" id="selectAllStockProducts" class="form-check-input">
                                </th>
                                <th scope="col">Kategori</th>
                                <th scope="col">Stok Kodu</th>
                                <th scope="col">Ürün Adı</th>
                                <th scope="col">Varyant</th>
                                <th scope="col">Stok</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-primary" id="addSelectedProducts">Seçilen Ürünleri Ekle</button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    $(document).ready(function() {
        // Fatura Tarihi otomatik doldurma
        var $invoiceDate = $('input[name="invoice_date"]');
        if (!$invoiceDate.val()) {
            var now = new Date();
            var year = now.getFullYear();
            var month = ('0' + (now.getMonth() + 1)).slice(-2);
            var day = ('0' + now.getDate()).slice(-2);
            var hours = ('0' + now.getHours()).slice(-2);
            var minutes = ('0' + now.getMinutes()).slice(-2);
            var formatted = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
            $invoiceDate.val(formatted);
        }
        const $currentId = $('#current_id');
        const $exchangeRateId = $('#exchange_rate_id');
        const $warehouseId = $('#warehouse_id');
        const $addProductBtn = $('#addProductBtn');
        const hasApprovedProducts = @json($hasLockedProducts);
        if (hasApprovedProducts) {
            disableFormForApprovedProducts();
        }
        updateExchangeRateDisplay();
        updateCurrentAddress();
        setTimeout(() => calculateAllProducts(), 100);
        $currentId.on('change', updateCurrentAddress);
        $exchangeRateId.on('change', function() {
            updateExchangeRateDisplay();
            calculateAllProducts();
        });
        
        $addProductBtn.on('click', handleAddProduct);
        $('#addSelectedProducts').on('click', handleAddSelectedProducts);
        $('#stockProductModal').on('hidden.bs.modal', function() {
            $('.select-product-checkbox').prop('checked', false);
            $('#selectAllStockProducts').prop('checked', false);
        });
        
        $(document).on('change', '#productTable .product-quantity, #productTable .product-price, #productTable input[name$="[vat_rate]"], #productTable select[name$="[vat_status]"], #productTable .product-currency', function() {
            const $row = $(this).closest('tr');
            const exchangeRate = parseFloat($row.find('.product-currency option:selected').data('selling-rate')) || 1;
            $row.find('.product-exchange-rate').val(exchangeRate);
            calculateAllProducts();
        });
        
        $(document).on('click', '#productTable .remove-product', function() {
            $(this).closest('tr').remove();
            calculateAllProducts();
        });
        
        $('#invoiceForm').on('submit', handleFormSubmit);
        
        function disableFormForApprovedProducts() {
            const fieldsToDisable = ['#warehouse_id', '#invoice_date', '#exchange_rate_id', '#current_id', '#shipping_address','#payment_term_id' ,'#payment_type_id', '#due_date', 'textarea[name="description"]','#invoiceSaveBtn'];
            fieldsToDisable.forEach(field => $(field).prop('disabled', true));
            $addProductBtn.prop('disabled', true).css({'opacity': '0.5', 'cursor': 'not-allowed'});
            
            // Ürün tablosundaki tüm input ve select alanlarını devre dışı bırak
            $('#productTable input, #productTable select, #productTable textarea').prop('disabled', true);
            $('#productTable .remove-product').prop('disabled', true).css({'opacity': '0.5', 'cursor': 'not-allowed'});
            
            Swal.fire({
                text: 'Bu satış faturasında onaylanmış kısmi muhasebelendi yada muhasebelenmiş olduğu için fatura bilgileri değiştirilemez.',
                icon: 'warning',
                confirmButtonText: 'Tamam'
            });
        }
        
        function updateExchangeRateDisplay() {
            const $selected = $exchangeRateId.find('option:selected');
            const rate = $selected.data('selling-rate') || '';
            const symbol = $selected.data('symbol') || '';
            
            $('#selling_rate').val(rate ? parseFloat(rate).toFixed(4).replace('.', ',') : '');
            $('#currency_symbol').text(symbol);
            
            $('#currency_symbol_total_fx, #currency_symbol_vat_fx, #currency_symbol_total_amount_fx').text(symbol);
        }
        
        function updateCurrentAddress() {
            const $selected = $currentId.find('option:selected');
            const address = $selected.data('address') || '';
            $('#address').val(address);
        }
        

        
        function calculateAllProducts() {
            const formData = collectFormData();
            
            $.ajax({
                url: "{{ route('backend.invoices_calculate_totals') }}",
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        updateTotalsDisplay(response.data);

                        // Satır güncelleme
                        if (response.data.product_rows) {
                            Object.keys(response.data.product_rows).forEach(function(stockId) {
                                var row = $('#productTable tr[data-id="' + stockId + '"]');
                                var rowData = response.data.product_rows[stockId];
                                // Gizli inputlar
                                row.find('input[name="products['+stockId+'][amount]"]').val(rowData.amount_raw);
                                row.find('input[name="products['+stockId+'][vat_amount]"]').val(rowData.vat_amount_raw);
                                row.find('input[name="products['+stockId+'][total_amount]"]').val(rowData.total_amount_raw);
                                // Görünen tutar inputu
                                row.find('.product-amount').val(rowData.total_amount);
                            });
                        }
                    } else {
                        console.error('Hesaplama hatası:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hatası:', error);
                }
            });
        }
        
        function collectFormData() {
            const products = {};
            const exchangeRateId = $exchangeRateId.val();
            
            $('#productTable tbody tr').each(function() {
                const $row = $(this);
                const stockId = $row.data('id');
                
                if (stockId) {
                    products[stockId] = {
                        quantity: $row.find('.product-quantity').val() || 1,
                        price: ($row.find('.product-price').val() || '0.00').replace('.', ','),
                        vat_rate: $row.find('input[name$="[vat_rate]"]').val() || 0,
                        vat_status: $row.find('select[name$="[vat_status]"]').val() || 0,
                        exchange_rate_id: $row.find('.product-currency').val() || exchangeRateId
                    };
                }
            });
            
            return {
                products: products,
                exchange_rate_id: exchangeRateId,
                _token: $('meta[name="csrf-token"]').attr('content')
            };
        }
        
        function updateTotalsDisplay(data) {
            $('#net_amount').val(data.net_amount);
            $('#tax_amount').val(data.tax_amount);
            $('#total_amount').val(data.total_amount);
            
            $('#net_amount_fx').val(data.net_amount_fx);
            $('#tax_amount_fx').val(data.tax_amount_fx);
            $('#total_amount_fx').val(data.total_amount_fx);
            
            $('#currency_symbol_net_amount_fx, #currency_symbol_vat_fx, #currency_symbol_total_amount_fx').text(data.currency_symbol);
            
            const convertForHidden = (value) => (value || '0,00').replace(/\./g, '').replace(',', '.');
            $('#net_amount_fx_hidden').val(convertForHidden(data.net_amount_fx));
            $('#tax_amount_fx_hidden').val(convertForHidden(data.tax_amount_fx));
            $('#total_amount_fx_hidden').val(convertForHidden(data.total_amount_fx));
        }
        
        function handleAddProduct() {
            const selectedWarehouseId = $warehouseId.val();
            if (!selectedWarehouseId) {
                Swal.fire({
                    text: 'Lütfen depo seçiniz',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                });
                return;
            }
            
            loadStockProducts();
            $('#stockProductModal').modal('show');
        }
        
        function handleAddSelectedProducts() {
            const $selectedCheckboxes = $('.select-product-checkbox:checked');
            if ($selectedCheckboxes.length === 0) {
                Swal.fire({
                    text: 'Lütfen en az bir ürün seçiniz.',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                });
                return;
            }
            
            const selectedIds = [];
            $selectedCheckboxes.each(function() {
                selectedIds.push($(this).val());
            });
            
            $.ajax({
                url: "{{ route('backend.add_selected_invoice_products') }}",
                type: 'POST',
                data: {
                    selected_ids: selectedIds,
                    invoice_no: $('#js_invoice_no').val(),
                    current_product_count: $('#productTable tbody tr').length,
                    exchange_rate_id: $exchangeRateId.val(),
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#productTable tbody').append(response.html);
                        
                        calculateAllProducts();
                        
                        $('#stockProductModal').modal('hide');
                        $('.select-product-checkbox').prop('checked', false);
                        $('#selectAllStockProducts').prop('checked', false);
                    } else {
                        Swal.fire({
                            text: response.message || 'Ürünler eklenirken bir hata oluştu.',
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        text: 'Ürünler eklenirken bir hata oluştu.',
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        }
        

        
        function loadStockProducts() {
            const selectedWarehouseId = $warehouseId.val();

            if ($.fn.DataTable.isDataTable('#stockProductModalTable')) {
                $('#stockProductModalTable').DataTable().destroy();
            }
            
            BaseCRUD.selector = "#stockProductModalTable";
            BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.general_stock_list') }}?datatable=true",
                    type: 'POST',
                    data: {
                        warehouse_id: selectedWarehouseId
                    },
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }
                },
                columns: [
                    { 
                        data: null,
                        orderable: false,
                        className: 'text-center',
                        render: function(data, type, row) {
                            return `<input type="checkbox" class="form-check-input select-product-checkbox" value="${row.id}">`;
                        }
                    },
                    { data: 'category', name: 'category', className: 'text-center' },
                    { data: 'stock_code', name: 'stock_code', className: 'text-center' },
                    { data: 'product_name', name: 'product_name', className: 'text-center' },
                    { data: 'variant_name', name: 'variant_name', className: 'text-center' },
                    { data: 'available_stock', name: 'available_stock', className: 'text-center' }
                ],
                order: [[1, 'desc']],
                pageLength: 5,
                lengthChange: false,
                initComplete: function() {
                    // Hepsini seç checkbox'ı için event handler
                    $('#selectAllStockProducts').on('change', function() {
                        const isChecked = $(this).prop('checked');
                        $('.select-product-checkbox').prop('checked', isChecked);
                    });
                    
                    // Tek tek checkbox'lar için event handler
                    $(document).on('change', '.select-product-checkbox', function() {
                        const totalCheckboxes = $('.select-product-checkbox').length;
                        const checkedCheckboxes = $('.select-product-checkbox:checked').length;
                        $('#selectAllStockProducts').prop('checked', totalCheckboxes === checkedCheckboxes);
                    });
                }
            });
        }
        
        function handleFormSubmit(e) {
            const selectedCurrency = $exchangeRateId.find('option:selected').data('currency-code');
            if (selectedCurrency !== 'TRY') {
                $('#net_amount_fx_hidden').val($('#net_amount_fx').val().replace(/\./g, '').replace(',', '.'));
                $('#tax_amount_fx_hidden').val($('#tax_amount_fx').val().replace(/\./g, '').replace(',', '.'));
                $('#total_amount_fx_hidden').val($('#total_amount_fx').val().replace(/\./g, '').replace(',', '.'));
            }
        }
        
        @if (old('products'))
            setTimeout(() => calculateAllProducts(), 200);
        @endif

        var $invoiceDate = $('input[name="invoice_date"]');
        var $dueDate = $('input[name="due_date"]');
        var $paymentTerm = $('#payment_term_id');

        function setDueDateToInvoiceDate() {
            $dueDate.val($invoiceDate.val());
        }

        // Fatura tarihi değişirse, vade tanımı seçili değilse vade tarihini eşitle
        $invoiceDate.on('change', function() {
            if (!$paymentTerm.val()) {
                setDueDateToInvoiceDate();
            } else {
                $paymentTerm.trigger('change');
            }
        });

        // Vade tanımı değişirse, gün ekle
        $paymentTerm.on('change', function() {
            var dayCount = parseInt($(this).find('option:selected').data('day-count')) || 0;
            if (dayCount > 0) {
                var invoiceDateVal = $invoiceDate.val();
                if (!invoiceDateVal) return;
                var baseDate = new Date(invoiceDateVal);
                baseDate.setDate(baseDate.getDate() + dayCount);

                var year = baseDate.getFullYear();
                var month = ('0' + (baseDate.getMonth() + 1)).slice(-2);
                var day = ('0' + baseDate.getDate()).slice(-2);
                var hours = ('0' + baseDate.getHours()).slice(-2);
                var minutes = ('0' + baseDate.getMinutes()).slice(-2);
                var formatted = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;

                $dueDate.val(formatted);
            } else {
                setDueDateToInvoiceDate();
            }
        });

        var warningShown = false;
        // Kullanıcı vade tarihini elle değiştirmek isterse ve vade tanımı seçili değilse uyarı göster
        $dueDate.on('focus', function() {
            if (!$paymentTerm.val() && !warningShown) {
                warningShown = true;
                Swal.fire({
                    text: 'Vade tarihi değiştirmek için önce bir vade tanımı seçmelisiniz. Aksi halde vade tarihi fatura tarihi ile aynı kalacaktır.',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                }).then(function(){
                    warningShown = false;
                    setDueDateToInvoiceDate();
                    $dueDate.blur(); // inputun focus'unu kaldır
                });
            }
        });

        // Sayfa ilk açıldığında da kontrol et
        if (!$paymentTerm.val()) {
            setDueDateToInvoiceDate();
        }
    });
</script>
@endsection
