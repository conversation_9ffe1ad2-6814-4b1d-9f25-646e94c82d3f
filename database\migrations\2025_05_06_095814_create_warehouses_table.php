<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->comment('Depo adı');
            $table->string('code', 50)->unique()->comment('Depo kodu');
            $table->integer('warehouse_type_id')->nullable()->comment('Depo tipi ID');
            $table->longText('description')->nullable()->comment('Açıklama');
            $table->longText('address')->nullable()->comment('Adres');
            $table->string('phone', 50)->nullable()->comment('Telefon');
            $table->string('email', 255)->nullable()->comment('E-posta');
            $table->decimal('max_weight_capacity', 15, 3)->default(0)->nullable()->comment('Maksimum ağırlık kapasitesi (kg)');
            $table->decimal('max_volume_capacity', 15, 4)->default(0)->nullable()->comment('Maksimum hacim kapasitesi (m³)');
            $table->decimal('current_weight', 15, 3)->default(0)->nullable()->comment('Mevcut ağırlık (kg)');
            $table->decimal('current_volume', 15, 4)->default(0)->nullable()->comment('Mevcut hacim (m³)');
            $table->decimal('temperature_min', 5, 2)->nullable()->comment('Minimum sıcaklık (°C)');
            $table->decimal('temperature_max', 5, 2)->nullable()->comment('Maksimum sıcaklık (°C)');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
