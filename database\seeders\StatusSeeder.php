<?php

namespace Database\Seeders;

use App\Models\Status;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $statuses = [
                [
                    'name' => 'Beklemede',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Onaylandı',
                    'is_active' => 1,
                ],
                [
                    'name' => 'İptal Edildi',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Tamamlandı',
                    'is_active' => 1,
                ],
            ];

            foreach ($statuses as $status) {
                Status::updateOrCreate(
                    ['name' => $status['name']],
                    $status
                );
            }
        });

        $this->command->info('Status verileri başarıyla oluşturuldu.');
    }
}
