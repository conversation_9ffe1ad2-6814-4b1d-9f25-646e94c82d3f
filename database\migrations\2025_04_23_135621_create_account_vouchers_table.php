<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_vouchers', function (Blueprint $table) {
            $table->id();
            $table->string('voucher_no')->unique()->comment('Fiş numarası (CHF/AY/YIL/00001)');
            $table->integer('current_id')->comment('Cari hesap ID');
            $table->date('voucher_date')->comment('Fiş tarihi');
            $table->integer('payment_type_id');
            $table->decimal('amount', 20, 2)->comment('Fiş tutarı');
            $table->integer('exchange_rate_id')->comment('Döviz kuru');
            $table->text('description')->nullable()->comment('Açıklama');
            $table->string('reference_no')->nullable()->comment('Referans numarası (çek/senet no vb.)');
            $table->date('due_date')->nullable()->comment('Vade tarihi');
            $table->string('bank_name')->nullable()->comment('Banka adı');
            $table->string('bank_branch')->nullable()->comment('Banka şubesi');
            $table->string('account_no')->nullable()->comment('Hesap numarası');
            $table->string('document_file')->nullable()->comment('Belge dosyası');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_vouchers');
    }
};
