<?php

namespace App\Models;

use App\Models\Balance;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class AccountVoucher extends BaseModel
{
    use SoftDeletes;

    protected $table = 'account_vouchers';

    protected $guarded = [];

    protected $casts = ['voucher_date' => 'date', 'due_date' => 'date'];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function voucherType()
    {
        return $this->belongsTo(VoucherType::class, 'voucher_type', 'code');
    }
    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }
    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }
    public static function generatePreviewAccountVoucherNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');

        // <PERSON><PERSON><PERSON> ka<PERSON>ar da dahil, en yüksek fiş numarasını al
        $lastVoucher = AccountVoucher::withTrashed()
            ->whereYear('voucher_date', $year)
            ->whereMonth('voucher_date', $month)
            ->where('voucher_no', 'like', "CHF/{$month}/{$year}/%")
            ->orderBy('voucher_no', 'desc')
            ->first();

        if ($lastVoucher) {
            $lastNumberPart = substr($lastVoucher->voucher_no, strrpos($lastVoucher->voucher_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "CHF/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }

    public static function bootHook() 
    {
        static::deleting(function ($voucher) {
            if (!$voucher->isForceDeleting()) {
                $balance = Balance::where('current_id', $voucher->current_id)->first();
                if ($balance) {
                    $balance->credit_balance -= $voucher->amount;
                    $balance->save();

                    // BalanceCurrency'dan da düş
                    $exchangeRate = $voucher->exchangeRate;
                    $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
                    
                    $balanceCurrency = $balance->balanceCurrencies()
                        ->where('currency_code', $currencyCode)
                        ->first();
                    
                    if ($balanceCurrency) {
                        $balanceCurrency->credit_balance -= $voucher->amount;
                        $balanceCurrency->save();
                    }
                }
            }
        });
    }
}
