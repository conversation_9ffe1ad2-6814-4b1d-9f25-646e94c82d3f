<?php $__env->startSection('content'); ?>
<style>
    .status-row td {
        border-color: rgba(0, 0, 0, 0.1) !important;
    }
    .form-check-input {
        opacity: 1 !important;
        background-color: #fff !important;
        border-color: #aaa !important;
    }
    tr[style] td {
        background-color: inherit !important;
        color: inherit !important;
    }
    .info-card {
        height: 100%;
    }
    .address-wrapper {
        max-width: 100%;
        word-wrap: break-word;
        display: inline;
    }
</style>
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fs-6">
                        <?php echo e($subTitle); ?>

                    </h5>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('backend.' . $container->page . '_list')); ?>"
                            class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                            <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card info-card">
                                <div class="card-body">
                                    <h6 class="mb-3">Teklif Bilgileri</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif No:</strong> <?php echo e($item->offer_number); ?>

                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif Tarihi:</strong> <?php echo e(\Carbon\Carbon::parse($item->offer_date)->format('d.m.Y')); ?>

                                        </div>
                                        
                                        <div class="col-md-6 mb-2">
                                            <strong>Depo:</strong> <?php echo e($item->warehouse->name ?? '-'); ?>

                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif Son Tarihi:</strong> <?php echo e($item->offer_deadline ? \Carbon\Carbon::parse($item->offer_deadline)->format('d.m.Y') : '-'); ?>

                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Döviz Türü:</strong>
                                            <?php echo e($item->exchangeRate->currency_code ?? '-'); ?>

                                            <?php if(!empty($item->exchangeRate->symbol)): ?>
                                                (<?php echo e($item->exchangeRate->symbol); ?>)
                                            <?php endif; ?>
                                            <?php if(!empty($item->exchangeRate->selling_rate)): ?>
                                                - Kur: <?php echo e(number_format($item->exchangeRate->selling_rate, 4, ',', '.')); ?>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card info-card">
                                <div class="card-body">
                                    <h6 class="mb-3">Cari Bilgileri</h6>
                                    <div class="row">
                                        
                                        <div class="col-md-6 mb-2">
                                            <strong>Cari Adı:</strong> <?php echo e($item->current->title ?? '-'); ?>

                                            <?php if($item->current && $item->current->deleted_at): ?>
                                                <span class="badge bg-danger">Silinmiş</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Telefon:</strong> <?php echo e($item->current->phone ?? '-'); ?>

                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Adres:</strong>
                                            <span class="address-wrapper"><?php if(!empty($item->shipping_address)): ?><?php echo e($item->shipping_address); ?><?php else: ?><?php echo e($item->current->address  ?? '-'); ?> <?php echo e($item->current->city->name ?? '-'); ?> <?php echo e($item->current->country->name ?? '-'); ?><?php endif; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h6 class="mb-3">Teklif Ürünleri</h6>
                            <div class="table-responsive">
                                <table class="table vertical-striped-table mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 40px;">
                                                <div class="form-check style-check d-flex align-items-center">
                                                    <input type="checkbox" class="form-check-input" id="selectAllProducts">
                                                </div>
                                            </th>
                                            <th class="text-center">Sıra No</th>
                                            <th class="text-center">Ürün Kodu</th>
                                            <th class="text-center">Ürün Adı</th>
                                            <th class="text-center">Miktar</th>
                                            <th class="text-center">Birim</th>
                                            <th class="text-center">Birim Fiyat</th>
                                            <th class="text-center">KDV</th>
                                            <th class="text-center">Toplam</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                            $fxRate = $item->exchangeRate->selling_rate ?? 1;
                                            $fxSymbol = $item->exchangeRate->symbol ?? $item->exchangeRate->currency_code ?? '';
                                            $totalPriceFX = $fxRate != 0 ? $item->total_price / $fxRate : 0;
                                            $vatAmountFX = $fxRate != 0 ? $item->vat_amount / $fxRate : 0;
                                            $totalAmountFX = $fxRate != 0 ? $item->total_amount / $fxRate : 0;
                                        ?>
                                        <?php $__currentLoopData = $item->offerProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $rowStyle = '';

                                                switch($product->status) {
                                                    case 0: // Beklemede
                                                        $rowStyle = 'background-color: #FFC02D6B !important; color: #000 !important;';
                                                        break;
                                                    case 1: // Onaylandı
                                                        $rowStyle = 'background-color: #45b36966 !important; color: #000 !important;';
                                                        break;
                                                    case 2: // Reddedildi
                                                        $rowStyle = 'background-color: #ef47704a !important; color: #000 !important;';
                                                        break;
                                                }
                                                $productTotalTL = $product->total_price;
                                                $productTotalFX = $fxRate != 0 ? $productTotalTL / $fxRate : 0;
                                            ?>
                                            <tr class="status-row">
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <div class="form-check style-check d-flex align-items-center">
                                                        <input type="checkbox" class="form-check-input product-checkbox" value="<?php echo e($product->id); ?>">
                                                    </div>
                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>"><?php echo e($product->item_no ?? '-'); ?></td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <?php if($product->stock && $product->stock->variant): ?>
                                                        <?php echo e($product->stock->variant->sku); ?>

                                                    <?php elseif($product->stock && $product->stock->product): ?>
                                                        <?php echo e($product->stock->product->sku); ?>

                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <?php if($product->stock && $product->stock->product): ?>
                                                        <?php echo e($product->stock->product->name); ?>

                                                        <?php if($product->stock->variant): ?>
                                                            / <?php echo e($product->stock->variant->name); ?>

                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>"><?php echo e($product->quantity); ?></td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <?php if($product->stock && $product->stock->product && $product->stock->product->unit): ?>
                                                        <?php echo e($product->stock->product->unit->name); ?>

                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <?php echo e(number_format($product->unit_price, 2, ',', '.')); ?> <?php echo e($product->currency_type); ?>

                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    %<?php echo e($product->vat_rate); ?> (<?php echo e($product->vat_status == 0 ? 'Hariç' : 'Dahil'); ?>)
                                                </td>
                                                <td class="text-center" style="<?php echo e($rowStyle); ?>">
                                                    <span><?php echo e(number_format($productTotalTL, 2, ',', '.')); ?> ₺</span>
                                                    <span style="margin-left:8px; color:#888;"><?php echo e(number_format($productTotalFX, 2, ',', '.')); ?> <?php echo e($fxSymbol); ?></span>
                                                </td>
                                            </tr>                                           
                                             <?php if($product->status == 2 && $product->reason_for_cancellation): ?>
                                                <tr>
                                                    <td colspan="9" class="text-start">
                                                        <strong>İptal Sebebi:</strong> <?php echo e($product->reason_for_cancellation); ?>

                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                    <tbody>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>Ara Toplam:</strong></td>
                                            <td class="text-center">
                                                <span><?php echo e(number_format($item->total_price, 2, ',', '.')); ?> ₺</span>
                                                <span style="margin-left:8px; color:#888;"><?php echo e(number_format($totalPriceFX, 2, ',', '.')); ?> <?php echo e($fxSymbol); ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>KDV:</strong></td>
                                            <td class="text-center">
                                                <span><?php echo e(number_format($item->vat_amount, 2, ',', '.')); ?> ₺</span>
                                                <span style="margin-left:8px; color:#888;"><?php echo e(number_format($vatAmountFX, 2, ',', '.')); ?> <?php echo e($fxSymbol); ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>Genel Toplam:</strong></td>
                                            <td class="text-center">
                                                <span><?php echo e(number_format($item->total_amount, 2, ',', '.')); ?> ₺</span>
                                                <span style="margin-left:8px; color:#888;"><?php echo e(number_format($totalAmountFX, 2, ',', '.')); ?> <?php echo e($fxSymbol); ?></span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-primary" id="updateStatusBtn" disabled>
                            Seçili Ürünlerin Durumunu Güncelle
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusUpdateModalLabel">Durum Güncelleme</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="statusSelect" class="form-label">Yeni Durum</label>
                        <div class="d-flex flex-column gap-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusPending" value="0">
                                <label class="form-check-label" for="statusPending">
                                    <span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Beklemede</span>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusApproved" value="1">
                                <label class="form-check-label" for="statusApproved">
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusRejected" value="2">
                                <label class="form-check-label" for="statusRejected">
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">İptal Edildi</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3" id="cancellationReasonDiv" style="display: none;">
                        <label for="reasonForCancellation" class="form-label">İptal Sebebi</label>
                        <textarea class="form-control" id="reasonForCancellation" name="reason_for_cancellation" rows="3" placeholder="İptal sebebini yazınız"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="button" class="btn btn-primary" id="saveStatusBtn">Kaydet</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $(document).ready(function() {
            $('.status-row').each(function() {
                var style = $(this).attr('style');
                $(this).find('td').attr('style', style);
            });
            $('#selectAllProducts').change(function() {
                $('.product-checkbox').prop('checked', $(this).prop('checked'));
                updateButtonState();
            });
            $('.product-checkbox').change(function() {
                updateButtonState();
                var allChecked = $('.product-checkbox:checked').length === $('.product-checkbox').length;
                $('#selectAllProducts').prop('checked', allChecked);
            });

            function updateButtonState() {
                var anyChecked = $('.product-checkbox:checked').length > 0;
                $('#updateStatusBtn').prop('disabled', !anyChecked);
            }
            $('#updateStatusBtn').click(function() {
                var modal = $('#statusUpdateModal');
                modal.modal('show');
                modal.removeAttr('aria-hidden');
                modal.attr('aria-modal', 'true');
            });
            $('#statusUpdateModal').on('hidden.bs.modal', function() {
                $(this).find('button, input, select').blur();
                $(this).attr('aria-hidden', 'true');
                $(this).attr('aria-modal', 'false');
                $('#updateStatusBtn').focus();
                // Modal kapandığında formu temizle
                $('input[name="status"]').prop('checked', false);
                $('#reasonForCancellation').val('');
                $('#cancellationReasonDiv').hide();
            });
            
            // Durum seçimi değiştiğinde iptal sebebi alanını göster/gizle
            $('input[name="status"]').change(function() {
                if ($(this).val() == '2') {
                    $('#cancellationReasonDiv').show();
                } else {
                    $('#cancellationReasonDiv').hide();
                }
            });
            
            $('#saveStatusBtn').click(function() {
                var selectedStatus = $('input[name="status"]:checked').val();
                if (!selectedStatus) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Lütfen bir durum seçin',
                        icon: 'warning',
                        confirmButtonText: 'Tamam',
                        willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                        willClose: function() { $('body').attr('aria-hidden', 'false'); }
                    });
                    return;
                }
                sendStatusUpdate(selectedStatus);
            });
            function sendStatusUpdate(selectedStatus) {
                var selectedProducts = [];
                $('.product-checkbox:checked').each(function() {
                    selectedProducts.push($(this).val());
                });
                
                var data = {
                    _token: '<?php echo e(csrf_token()); ?>',
                    product_ids: selectedProducts,
                    status: selectedStatus
                };
                
                // İptal durumu için sebep ekle
                if (selectedStatus == '2') {
                    data.reason_for_cancellation = $('#reasonForCancellation').val().trim();
                }
                
                $.ajax({
                    url: '<?php echo e(route("backend.offer_given_update_status")); ?>',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            $('#statusUpdateModal').modal('hide');
                            Swal.fire({
                                title: 'Başarılı!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'Tamam',
                                willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                                willClose: function() { $('body').attr('aria-hidden', 'false'); }
                            }).then((result) => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Hata!',
                                text: 'Durum güncellenirken bir hata oluştu: ' + response.message,
                                icon: 'error',
                                confirmButtonText: 'Tamam',
                                willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                                willClose: function() { $('body').attr('aria-hidden', 'false'); }
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Hata!',
                            text: 'Durum güncellenirken bir hata oluştu',
                            icon: 'error',
                            confirmButtonText: 'Tamam',
                            willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                            willClose: function() { $('body').attr('aria-hidden', 'false'); }
                        });
                        console.error(xhr);
                    }
                });
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\erp_web\resources\views/backend/offer_given/detail.blade.php ENDPATH**/ ?>