<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Spatie\Activitylog\LogOptions;

class Waybill extends BaseModel
{
    use SoftDeletes;

    protected $guarded = [];
    protected $table = 'waybills';

    protected $casts = ['waybill_date' => 'date'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }

    public function waybillItems()
    {
        return $this->hasMany(WaybillItem::class);
    }


    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function waybillType()
    {
        return $this->belongsTo(WaybillType::class);
    }
    public function waybillStatu()
    {
        return $this->belongsTo(WaybillStatu::class);
    }
    public static function generatePreviewSaleWaybillNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');

        // Silinen kayıtlar da dahil, en yüksek fiş numarasını al
        $lastVoucher = Waybill::withTrashed()
            ->whereYear('waybill_date', $year)
            ->whereMonth('waybill_date', $month)
            ->where('waybill_no', 'like', "SIR/{$month}/{$year}/%")
            ->orderBy('waybill_no', 'desc')
            ->first();

        if ($lastVoucher) {
            $lastNumberPart = substr($lastVoucher->waybill_no, strrpos($lastVoucher->waybill_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "SIR/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
    public static function generatePreviewPurchaseWaybillNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');

        // Silinen kayıtlar da dahil, en yüksek fiş numarasını al
        $lastVoucher = Waybill::withTrashed()
            ->whereYear('waybill_date', $year)
            ->whereMonth('waybill_date', $month)
            ->where('waybill_no', 'like', "AIR/{$month}/{$year}/%")
            ->orderBy('waybill_no', 'desc')
            ->first();

        if ($lastVoucher) {
            $lastNumberPart = substr($lastVoucher->waybill_no, strrpos($lastVoucher->waybill_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "AIR/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }

    public function scopeFilter($query, $filter)
    {
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('waybill_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        }
        // waybill_statu_id (ayrı bağımsız filtre)
        if (isset($filter->waybill_statu_id) && !empty($filter->waybill_statu_id)) {
            $query->where('waybill_statu_id', $filter->waybill_statu_id);
        }
    }
    
    public static function bootHook()
    {

        static::deleting(function ($waybill) {
            // Onaylanmış irsaliyelerin silinmesini engelle
            if ($waybill->waybill_statu_id == 2) {
                throw new \Exception('Onaylanmış irsaliyeler silinemez.');
            }
            
            // Eğer soft delete (yani normal delete) ise
            if (! $waybill->isForceDeleting()) {
                $waybill->waybillItem()->delete();  // İlişkili kayıtları soft delete et
            }
        });
    }
}
