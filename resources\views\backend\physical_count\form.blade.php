@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? '<PERSON>ziksel Sayım Düzenle' : '<PERSON><PERSON>';
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                @csrf

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Sayım Kodu</label>
                        <input type="text" name="count_code" class="form-control"
                            value="{{ old('count_code', $item->count_code) }}" placeholder="Otomatik oluşturulacak"
                            {{ $item->id ? 'readonly' : '' }}>
                        @if($item->id)
                            <input type="hidden" name="count_code" value="{{ $item->count_code }}">
                        @endif
                        @error('count_code')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Depo <span class="text-danger">*</span></label>
                        <select name="warehouse_id" class="form-select select2" required>
                            <option value="">Seçiniz</option>
                            @foreach ($warehouses as $warehouse)
                                <option value="{{ $warehouse->id }}"
                                    {{ old('warehouse_id', $item->warehouse_id) == $warehouse->id ? 'selected' : '' }}>
                                    {{ $warehouse->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('warehouse_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Lokasyon</label>
                        <select name="location_id" class="form-select select2">
                            <option value="">Tüm Depo</option>
                            @if ($item->warehouse_id)
                                @foreach ($locations->where('warehouse_id', $item->warehouse_id) as $location)
                                    <option value="{{ $location->id }}"
                                        {{ old('location_id', $item->location_id) == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('location_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Sayım Tarihi <span class="text-danger">*</span></label>
                        <input type="date" name="count_date" class="form-control"
                            value="{{ old('count_date', $item->count_date ? $item->count_date->format('Y-m-d') : date('Y-m-d')) }}"
                            required>
                        @error('count_date')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-12">
                        <label class="form-label">Açıklama</label>
                        <textarea name="description" class="form-control" rows="3">{{ old('description', $item->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                    <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">İptal</a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var allLocations = [
                @foreach ($locations as $location)
                    {
                        id: {{ $location->id }},
                        name: "{{ $location->name }}",
                        warehouse_id: {{ $location->warehouse_id }}
                    },
                @endforeach
            ];

            $('select[name="warehouse_id"]').on('change', function() {
                var warehouseId = $(this).val();
                var locationSelect = $('select[name="location_id"]');

                locationSelect.html('<option value="">Tüm Depo</option>');

                if (warehouseId) {
                    var filteredLocations = allLocations.filter(function(location) {
                        return location.warehouse_id == warehouseId;
                    });

                    filteredLocations.forEach(function(location) {
                        locationSelect.append('<option value="' + location.id + '">' + location
                            .name + '</option>');
                    });
                }
            });

            var initialWarehouseId = $('select[name="warehouse_id"]').val();

            if (initialWarehouseId) {
                var currentLocationId = $('select[name="location_id"]').val();

                $('select[name="warehouse_id"]').trigger('change');

                if (currentLocationId) {
                    $('select[name="location_id"] option[value="' + currentLocationId + '"]').prop('selected',
                    true);
                }
            }
        });
    </script>
@endsection
