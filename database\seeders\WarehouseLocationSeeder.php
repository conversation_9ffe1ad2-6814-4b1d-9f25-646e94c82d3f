<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WarehouseLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createWarehouseLocations();
        });

        $this->command->info('WarehouseLocation verileri başarıyla oluşturuldu.');
    }

    /**
     * Depo lokasyonlarını oluştur
     */
    private function createWarehouseLocations(): void
    {
        // Ana Depo için lokasyonlar
        $mainWarehouse = Warehouse::where('code', 'ANA-01')->first();
        
        if ($mainWarehouse) {
            $mainLocations = [
                [
                    'name' => 'A Blok',
                    'code' => 'A',
                    'description' => 'Ana depo A blok',
                    'children' => [
                        [
                            'name' => 'A1 Koridor',
                            'code' => 'A1',
                            'description' => 'A Blok 1. Koridor',
                            'children' => [
                                ['name' => 'A1-01', 'code' => 'A1-01', 'description' => 'A1 Koridor 1. Raf'],
                                ['name' => 'A1-02', 'code' => 'A1-02', 'description' => 'A1 Koridor 2. Raf'],
                                ['name' => 'A1-03', 'code' => 'A1-03', 'description' => 'A1 Koridor 3. Raf'],
                            ]
                        ],
                        [
                            'name' => 'A2 Koridor',
                            'code' => 'A2',
                            'description' => 'A Blok 2. Koridor',
                            'children' => [
                                ['name' => 'A2-01', 'code' => 'A2-01', 'description' => 'A2 Koridor 1. Raf'],
                                ['name' => 'A2-02', 'code' => 'A2-02', 'description' => 'A2 Koridor 2. Raf'],
                                ['name' => 'A2-03', 'code' => 'A2-03', 'description' => 'A2 Koridor 3. Raf'],
                            ]
                        ],
                    ]
                ],
                [
                    'name' => 'B Blok',
                    'code' => 'B',
                    'description' => 'Ana depo B blok',
                    'children' => [
                        [
                            'name' => 'B1 Koridor',
                            'code' => 'B1',
                            'description' => 'B Blok 1. Koridor',
                            'children' => [
                                ['name' => 'B1-01', 'code' => 'B1-01', 'description' => 'B1 Koridor 1. Raf'],
                                ['name' => 'B1-02', 'code' => 'B1-02', 'description' => 'B1 Koridor 2. Raf'],
                                ['name' => 'B1-03', 'code' => 'B1-03', 'description' => 'B1 Koridor 3. Raf'],
                            ]
                        ],
                    ]
                ],
            ];
            
            $this->createLocations($mainLocations, $mainWarehouse->id);
        }
        
        // İzmir Depo için lokasyonlar
        $izmirWarehouse = Warehouse::where('code', 'IZM-01')->first();
        
        if ($izmirWarehouse) {
            $izmirLocations = [
                [
                    'name' => 'Raf Bölgesi',
                    'code' => 'RAF',
                    'description' => 'İzmir depo raf bölgesi',
                    'children' => [
                        ['name' => 'RAF-01', 'code' => 'RAF-01', 'description' => '1. Raf'],
                        ['name' => 'RAF-02', 'code' => 'RAF-02', 'description' => '2. Raf'],
                        ['name' => 'RAF-03', 'code' => 'RAF-03', 'description' => '3. Raf'],
                    ]
                ],
                [
                    'name' => 'Paletli Alan',
                    'code' => 'PAL',
                    'description' => 'İzmir depo paletli alan',
                    'children' => [
                        ['name' => 'PAL-01', 'code' => 'PAL-01', 'description' => '1. Palet Alanı'],
                        ['name' => 'PAL-02', 'code' => 'PAL-02', 'description' => '2. Palet Alanı'],
                    ]
                ],
            ];
            
            $this->createLocations($izmirLocations, $izmirWarehouse->id);
        }
        
        // Ankara Depo için lokasyonlar
        $ankaraWarehouse = Warehouse::where('code', 'ANK-01')->first();
        
        if ($ankaraWarehouse) {
            $ankaraLocations = [
                [
                    'name' => 'Ana Alan',
                    'code' => 'ANA',
                    'description' => 'Ankara depo ana alan',
                    'children' => [
                        ['name' => 'ANA-01', 'code' => 'ANA-01', 'description' => 'Ana Alan 1. Bölge'],
                        ['name' => 'ANA-02', 'code' => 'ANA-02', 'description' => 'Ana Alan 2. Bölge'],
                        ['name' => 'ANA-03', 'code' => 'ANA-03', 'description' => 'Ana Alan 3. Bölge'],
                    ]
                ],
                [
                    'name' => 'Soğuk Hava',
                    'code' => 'SH',
                    'description' => 'Ankara depo soğuk hava deposu',
                    'children' => [
                        ['name' => 'SH-01', 'code' => 'SH-01', 'description' => 'Soğuk Hava 1. Bölge'],
                        ['name' => 'SH-02', 'code' => 'SH-02', 'description' => 'Soğuk Hava 2. Bölge'],
                    ]
                ],
            ];
            
            $this->createLocations($ankaraLocations, $ankaraWarehouse->id);
        }

        // Diğer depolar için de basit lokasyonlar oluştur
        $otherWarehouses = Warehouse::whereNotIn('code', ['ANA-01', 'IZM-01', 'ANK-01'])->get();
        
        foreach ($otherWarehouses as $warehouse) {
            $this->createLocations([
                [
                    'name' => 'Ana Alan',
                    'code' => 'ANA',
                    'description' => $warehouse->name . ' ana alan',
                    'children' => [
                        ['name' => 'ANA-01', 'code' => 'ANA-01', 'description' => 'Ana Alan 1. Bölge'],
                        ['name' => 'ANA-02', 'code' => 'ANA-02', 'description' => 'Ana Alan 2. Bölge'],
                    ]
                ],
            ], $warehouse->id);
        }
    }

    /**
     * Lokasyonları ve alt lokasyonları oluşturur
     */
    private function createLocations(array $locations, int $warehouseId, ?int $parentId = null): void
    {
        foreach ($locations as $locationData) {
            $location = WarehouseLocation::updateOrCreate(
                [
                    'code' => $locationData['code'],
                    'warehouse_id' => $warehouseId,
                    'parent_id' => $parentId,
                ],
                [
                    'name' => $locationData['name'],
                    'description' => $locationData['description'] ?? null,
                    'is_active' => 1,
                ]
            );

            if (isset($locationData['children']) && is_array($locationData['children'])) {
                $this->createLocations($locationData['children'], $warehouseId, $location->id);
            }
        }
    }
}
