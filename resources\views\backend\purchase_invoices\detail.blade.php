@extends('layout.layout')
@php
$currencyCode = $item->exchangeRate ? $item->exchangeRate->code : 'TRY';
$fxSymbol = $item->exchangeRate ? $item->exchangeRate->symbol : $item->exchangeRate->currency_code ?? '';
@endphp
@section('content')
<style>
    .status-row td {
        border-color: rgba(0, 0, 0, 0.1) !important;
    }

    .form-check-input {
        opacity: 1 !important;
        background-color: #fff !important;
        border-color: #aaa !important;
    }

    tr[style] td {
        background-color: inherit !important;
        color: inherit !important;
    }

    .info-card {
        height: 100%;
    }

    .address-wrapper {
        max-width: 100%;
        word-wrap: break-word;
        display: inline;
    }
</style>
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fs-6">
                    {{ $subTitle }}
                </h5>
                <a href="{{ route('backend.' . $container->page . '_list') }}"
                    class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                    <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                </a>
            </div>
            <div class="card-body">
                <!-- Fatura Bilgileri -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class=" card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Fatura Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Fatura No:</strong> {{ $item->invoice_no }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Fatura Tarihi:</strong> {{ $item->invoice_date ?
                                        \Carbon\Carbon::parse($item->invoice_date)->format('d.m.Y') : '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Depo:</strong> {{ $item->warehouse->name ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Vade Tarihi:</strong> {{ $item->due_date ?
                                        \Carbon\Carbon::parse($item->due_date)->format('d.m.Y') : '-' }}
                                        @if($item->payment_term_id)
                                        ({{ $item->paymentTerm->day_count }} Gün)
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Döviz Türü:</strong>
                                        {{ $item->exchangeRate->currency_code ?? '-' }}
                                        @if(!empty($item->exchangeRate->symbol))
                                        ({{ $item->exchangeRate->symbol }})
                                        @endif
                                        @if(!empty($item->exchangeRate->selling_rate))
                                        - Kur: {{ number_format($item->exchangeRate->selling_rate, 4, ',', '.') }}
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Cari Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Cari Adı:</strong> {{ $item->current->title ?? '-' }}
                                        @if($item->current && $item->current->deleted_at)
                                        <span class="badge bg-danger">Silinmiş</span>
                                        @endif
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Telefon:</strong> {{ $item->current->phone ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Adres:</strong>
                                        <span class="address-wrapper">@if(!empty($item->shipping_address)){{
                                            $item->shipping_address }}@else{{ $item->current->address ?? '-' }} {{
                                            $item->current->city->name ?? '-' }} {{ $item->current->country->name ?? '-'
                                            }}@endif</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="col-md-12">
                            <h6 class="mb-3">Ödeme Bilgileri</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Fatura Tutarı:</strong></div>
                                        <div class="col-6">
                                            <span>{{ number_format($item->total_amount, 2, ',', '.') }} ₺</span>
                                            @if($currencyCode !== 'TRY')
                                            <span style="margin-left:8px; color:#888;">{{ number_format($item->total_amount_fx,
                                                2, ',', '.') }}{{$fxSymbol }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Tahsil Edilen:</strong></div>
                                        <div class="col-6">
                                            @if($currencyCode === 'TRY')
                                            {{ number_format($item->collected ?? 0, 2, ',', '.') }} ₺
                                            @else
                                            {{ number_format($item->collected_fx ?? 0, 2, ',', '.') }} {{ $fxSymbol }}
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Kalan Tutar:</strong></div>
                                        <div class="col-6">
                                            @php
                                            $remainingTRY = ($item->total_amount ?? 0) - ($item->collected ?? 0);
                                            $remainingFX = ($item->total_amount_fx ?? 0) - ($item->collected_fx ?? 0);
                                            @endphp
                                            <span>{{ number_format($remainingTRY, 2, ',', '.') }} ₺</span>
                                            @if($currencyCode !== 'TRY')
                                            <span style="margin-left:8px; color:#888;">{{ number_format($remainingFX, 2,
                                                ',', '.') }} {{$fxSymbol }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        @if($item->invoice_status_id == 3 && $item->reason_for_cancellation)
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">İptal Sebebi</h6>
                                <p class="mb-0">{{ $item->reason_for_cancellation }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <h6 class="mb-3">Fatura Ürünleri</h6>
                        <div class="table-responsive">
                            <table class="table vertical-striped-table mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-center">Sıra No</th>
                                        <th class="text-center">Ürün Kodu</th>
                                        <th class="text-center">Ürün Adı</th>
                                        <th class="text-center">Miktar</th>
                                        <th class="text-center">Birim</th>
                                        <th class="text-center">Birim Fiyat</th>
                                        <th class="text-center">KDV</th>
                                        <th class="text-center">Toplam</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                    $fxRate = $item->exchangeRate->selling_rate ?? 1;
                                    $fxSymbol = $item->exchangeRate->symbol ?? $item->exchangeRate->currency_code ?? '';
                                    $totalPriceFX = $fxRate != 0 ? $item->net_amount / $fxRate : 0;
                                    $taxtAmountFX = $fxRate != 0 ? $item->tax_amount / $fxRate : 0;
                                    $totalAmountFX = $fxRate != 0 ? $item->total_amount / $fxRate : 0;
                                    @endphp
                                    @foreach($item->invoiceItems as $invoiceItem)
                                    @php
                                    $rowStyle = '';
                                    switch($item->invoice_status_id) {
                                    case 1: // Beklemede
                                    $rowStyle = 'background-color: #FFC02D6B !important; color: #000 !important;';
                                    break;
                                    case 2: // Onaylandı
                                    $rowStyle = 'background-color: #45b36966 !important; color: #000 !important;';
                                    break;
                                    case 3: // Reddedildi
                                    $rowStyle = 'background-color: #ef47704a !important; color: #000 !important;';
                                    break;
                                    case 4: // Kısmi Muhasebelendi
                                    $rowStyle = 'background-color: #0d6efd85 !important; color: #000 !important;';
                                    break;
                                    case 5: // Muhasebelendi Aktarıldı
                                    $rowStyle = 'background-color: #17a2b866 !important; color: #000 !important;';
                                    break;
                                    }
                                    $productTotalTL = $invoiceItem->total_price;
                                    $productTotalFX = $fxRate != 0 ? $productTotalTL / $fxRate : 0;
                                    @endphp
                                    <tr class="status-row">
                                        <td class="text-center" style="{{ $rowStyle }}">{{ $invoiceItem->item_no ?? '-'
                                            }}</td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($invoiceItem->stock && $invoiceItem->stock->variant &&
                                            !empty($invoiceItem->stock->variant->sku))
                                            {{ $invoiceItem->stock->variant->sku }}
                                            @elseif($invoiceItem->stock && $invoiceItem->stock->product &&
                                            !empty($invoiceItem->stock->product->sku))
                                            {{ $invoiceItem->stock->product->sku }}
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($invoiceItem->stock && $invoiceItem->stock->product)
                                            {{ $invoiceItem->stock->product->name }}
                                            @if($invoiceItem->stock->variant)
                                            / {{ $invoiceItem->stock->variant->name }}
                                            @endif
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">{{ $invoiceItem->quantity }}
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            @if($invoiceItem->stock && $invoiceItem->stock->product &&
                                            $invoiceItem->stock->product->unit)
                                            {{ $invoiceItem->stock->product->unit->name }}
                                            @else
                                            -
                                            @endif
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            {{ number_format($invoiceItem->unit_price, 2, ',', '.') }} {{
                                            $invoiceItem->currency_type }}
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            %{{ $invoiceItem->vat_rate }} ({{ $invoiceItem->vat_status == 0 ? 'Hariç' :
                                            'Dahil' }})
                                        </td>
                                        <td class="text-center" style="{{ $rowStyle }}">
                                            <span>{{ number_format($productTotalTL, 2, ',', '.') }} ₺</span>
                                            <span style="margin-left:8px; color:#888;">{{ number_format($productTotalFX,
                                                2, ',', '.') }} {{ $fxSymbol }}</span>
                                        </td>
                                    </tr>
                                    @if($invoiceItem->status == 2 && $invoiceItem->reason_for_cancellation)
                                    <tr>
                                        <td colspan="9" class="text-start">
                                            <strong>İptal Sebebi:</strong> {{ $product->reason_for_cancellation }}
                                        </td>
                                    </tr>
                                    @endif
                                    @endforeach
                                </tbody>
                                <tbody>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>Ara Toplam:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->net_amount, 2, ',', '.') }} ₺</span>
                                            <span style="margin-left:8px; color:#888;">{{ number_format($totalPriceFX,
                                                2, ',', '.') }} {{ $fxSymbol }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>KDV:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->tax_amount, 2, ',', '.') }} ₺</span>
                                            <span style="margin-left:8px; color:#888;">{{ number_format($taxtAmountFX,
                                                2, ',', '.') }} {{ $fxSymbol }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>Genel Toplam:</strong></td>
                                        <td class="text-center">
                                            <span>{{ number_format($item->total_amount, 2, ',', '.') }} ₺</span>
                                            <span style="margin-left:8px; color:#888;">{{ number_format($totalAmountFX,
                                                2, ',', '.') }} {{ $fxSymbol }}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @if(isset($transactions) && count($transactions) > 0)
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6 class="mb-3">Tahsilat Geçmişi</h6>
                        <div class="table-responsive">
                            <table class="table vertical-striped-table mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tarih</th>
                                        <th>Tutar</th>
                                        <th>Para Birimi</th>
                                        <th>Ödeme Yöntemi</th>
                                        <th>Açıklama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($transactions as $transaction)
                                    @php
                                    $currencySymbol = $transaction->exchangeRate ? $transaction->exchangeRate->symbol :
                                    '₺';
                                    $paymentMethod = $transaction->paymentType ? $transaction->paymentType->name : '-';
                                    @endphp
                                    <tr>
                                        <td>{{ $transaction->transaction_date ?
                                            $transaction->transaction_date->format('d.m.Y H:i') :
                                            '-' }}</td>
                                        <td>{{ number_format($transaction->amount, 2, ',', '.') }} {{ $currencySymbol }}
                                        </td>
                                        <td>{{ $transaction->exchangeRate ? $transaction->exchangeRate->code : 'TRY' }}
                                        </td>
                                        <td>{{ $paymentMethod }}</td>
                                        <td>{{ $transaction->description ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif
                <div class="text-end mt-3">
                    <button type="button" class="btn btn-primary" id="updateStatusBtn"
                        @if(in_array($item->invoice_status_id, [2,4,5])) disabled @endif>
                        Durumunu Güncelle
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusUpdateModalLabel">Durum Güncelleme</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="statusSelect" class="form-label">Yeni Durum</label>
                    <div class="d-flex flex-column gap-2">
                        @foreach($invoiceStatusList as $status)
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="status" id="status_{{ $status->id }}"
                                value="{{ $status->id }}">
                            <label class="form-check-label" for="status_{{ $status->id }}">
                                <span class="
                                        @if($status->id == 1) bg-warning-focus text-warning-600 border border-warning-main
                                        @elseif($status->id == 2) bg-success-focus text-success-600 border border-success-main
                                        @elseif($status->id == 3) bg-danger-focus text-danger-600 border border-danger-main
                                        @endif
                                        px-24 py-4 radius-4 fw-medium text-sm">
                                    {{ $status->name }}
                                </span>
                            </label>
                        </div>
                        @endforeach
                    </div>
                </div>
                <div class="mb-3" id="cancellationReasonDiv" style="display: none;">
                    <label for="reasonForCancellation" class="form-label">İptal Sebebi</label>
                    <textarea class="form-control" id="reasonForCancellation" name="reason_for_cancellation" rows="3"
                        placeholder="İptal sebebini yazınız"></textarea>
                    <div id="cancellationError" class="text-danger mt-1"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" id="saveStatusBtn">Kaydet</button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Durum Güncelle butonuna tıklanınca modalı aç
        document.getElementById('updateStatusBtn').addEventListener('click', function () {
            // Eğer buton disabled ise modal açılmasın
            if (this.hasAttribute('disabled')) return;
            var statusModal = new bootstrap.Modal(document.getElementById('statusUpdateModal'));
            statusModal.show();
        });

        // Modalda: İptal Edildi seçilirse iptal sebebi alanı göster/gizle
        const statusRadios = document.querySelectorAll('input[name="status"]');
        const cancellationDiv = document.getElementById('cancellationReasonDiv');
        const cancellationTextarea = document.getElementById('reasonForCancellation');
        statusRadios.forEach(function(radio) {
            radio.addEventListener('change', function() {
                if (parseInt(this.value) === 3) {
                    cancellationDiv.style.display = 'block';
                    cancellationTextarea.setAttribute('required', 'required');
                } else {
                    cancellationDiv.style.display = 'none';
                    cancellationTextarea.removeAttribute('required');
                    cancellationTextarea.value = '';
                }
            });
        });

        // Kaydet butonuna tıklanınca AJAX ile durum güncelle
        document.getElementById('saveStatusBtn').addEventListener('click', function () {
            const selectedStatus = document.querySelector('input[name="status"]:checked');
            const errorDiv = document.getElementById('cancellationError');
            errorDiv.textContent = '';

            if (!selectedStatus) {
                alert('Lütfen bir durum seçiniz.');
                return;
            }
            const statusId = parseInt(selectedStatus.value);
            let reasonForCancellation = '';
            if (statusId === 3) {
                reasonForCancellation = cancellationTextarea.value.trim();
                if (!reasonForCancellation) {
                    errorDiv.textContent = 'İptal sebebi zorunludur.';
                    return;
                }
                if (reasonForCancellation.length < 5) {
                    errorDiv.textContent = 'Lütfen iptal sebebi en az 5 karakter olmalıdır.';
                    return;
                }
                if (reasonForCancellation.length > 500) {
                    errorDiv.textContent = 'İptal sebebi 500 karakterden uzun olamaz.';
                    return;
                }
            }
            fetch('{{ route("backend.invoices_update_status") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    id: {{ $item->id }},
                    status_id: statusId,
                    reason_for_cancellation: reasonForCancellation
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message || 'Durum başarıyla güncellendi.');
                    location.reload();
                } else {
                    if (statusId === 3) {
                        errorDiv.textContent = data.message || 'Durum güncellenemedi.';
                    } else {
                        alert(data.message || 'Durum güncellenemedi.');
                    }
                }
            })
            .catch(error => {
                console.error('Hata:', error);
                alert('Durum güncellenirken bir hata oluştu.');
            });
        });
    });
</script>
@endsection