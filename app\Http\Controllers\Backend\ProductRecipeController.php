<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductRecipe;
use App\Models\ProductRecipeItem;

class ProductRecipeController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ürün Tarifleri';
        $this->page = 'product_recipe';
        $this->model = new ProductRecipe();
        $this->relation = ['products','productRecipeItems'];

        $this->view = (object) array(
            'breadcrumb' => array(
                'Ürün Tarifleri' => route('backend.product_recipe_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|max:255',
                'product_id' => 'required|exists:products,id',
                'product_recipe_items' => 'required',
                'product_recipe_items.*.product_id' => 'required|exists:products,id',
                'product_recipe_items.*.quantity' => 'required|numeric',
            ],
            [
                'name.required' => 'Ürün tarifi adı zorunludur.',
                'name.string' => 'Ürün tarifi adı metin formatında olmalıdır.',
                'name.max' => 'Ürün tarifi adı en fazla 255 karakter olmalıdır.',
                'product_id.required' => 'Ürün seçilmedi.',
                'product_id.exists' => 'Ürün bulunamadı.',
                'product_recipe_items.required' => 'Ürün tarifi öğeleri zorunludur.',
                'product_recipe_items.*.product_id.required' => 'Ürün tarifi öğesi seçilmedi.',
                'product_recipe_items.*.product_id.exists' => 'Ürün tarifi öğesi bulunamadı.',
                'product_recipe_items.*.quantity.required' => 'Ürün tarifi öğesi miktarı zorunludur.',
                'product_recipe_items.*.quantity.numeric' => 'Ürün tarifi öğesi miktarı sayısal olmalıdır.',
            ]
        );
        view()->share('products', Product::active()->get());
        parent::__construct();
    }
}
