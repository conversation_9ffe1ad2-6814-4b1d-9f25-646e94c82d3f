<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class Transaction extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $table = 'transactions';
    protected $guarded = [];

    protected $casts = [
        'transaction_date' => 'datetime',
    ];

    public static function bootHook()
    {
        static::deleted(function ($transaction) {
            // Exchange rate bilgisini al
            $exchangeRate = ExchangeRate::find($transaction->exchange_rate_id);
            $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
            
            // Balance tablosundan transaction_type_id'ye göre doğru alanı güncelle
            $balance = Balance::where('current_id', $transaction->current_id)->first();
            if ($balance) {
                if ($transaction->transaction_type_id == 1) {
                    // Ödeme işlemi - debit_balance'dan çıkar
                    $balance->debit_balance -= $transaction->amount;
                } elseif ($transaction->transaction_type_id == 2) {
                    // Tahsilat işlemi - credit_balance'dan ç<PERSON>
                    $balance->credit_balance -= $transaction->amount;
                }
                $balance->save();
                
                // BalanceCurrency'dan da çıkar
                $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $currencyCode)->first();
                if ($balanceCurrency) {
                    if ($transaction->transaction_type_id == 1) {
                        // Ödeme işlemi - debit_balance'dan çıkar
                        $balanceCurrency->debit_balance -= $transaction->amount;
                    } elseif ($transaction->transaction_type_id == 2) {
                        // Tahsilat işlemi - credit_balance'dan çıkar
                        $balanceCurrency->credit_balance -= $transaction->amount;
                    }
                    $balanceCurrency->save();
                }
            }

            // Invoice tablosundaki collected değerini güncelle
            $invoice = Invoice::find($transaction->invoice_id);
            if ($invoice) {
                $invoice->collected -= $transaction->amount;
                
                // Fatura durumunu yeniden hesapla
                if ($invoice->collected == 0) {
                    $invoice->invoice_status_id = 2; // Ödenmedi
                } elseif ($invoice->collected < $invoice->total_amount) {
                    $invoice->invoice_status_id = 4; // Kısmi Muhasebelendi
                } elseif ($invoice->collected >= $invoice->total_amount) {
                    $invoice->invoice_status_id = 5; // Muhasebelendi
                }
                
                $invoice->save();
            }
        });
    }

    public function transactionType()
    {
        return $this->belongsTo(TransactionType::class);
    }
    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class, 'payment_method');
    }
    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }
    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }
    public function scopeFilter($query, $filter)
    {
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('transaction_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        }

        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }
        //Fatura Numarası Filtresi
        if (isset($filter->invoice_no) && !empty($filter->invoice_no)) {
            $query->whereHas('invoice', function ($q) use ($filter) {
                $q->where('invoice_no', $filter->invoice_no);
            });
        }
        return $query;
    }
}
