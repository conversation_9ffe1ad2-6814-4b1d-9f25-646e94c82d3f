@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf

                        <!-- İşlem Tipi (hidden) -->
                        <input type="hidden" name="transaction_type_id" value="1">

                        <div class="row mb-3">
                            <!-- İşlem Tarihi -->
                            <div class="col-md-4">
                                <label for="transaction_date" class="form-label">İşlem Tarihi</label>
                                <input type="datetime-local" class="form-control" id="transaction_date" name="transaction_date" value="{{ old('transaction_date', $item->transaction_date ? $item->transaction_date->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i')) }}" required>
                            </div>

                            <!-- Fatura Tipi -->
                            <div class="col-md-4">
                                <label for="invoice-type" class="form-label">Fatura Tipi</label>
                                <select class="form-control" name="invoice_type" id="invoice-type">
                                    <option value="">Fatura Tipi Seçin</option>
                                    @foreach($invoiceTypes as $key => $value)
                                        <option value="{{ $key }}" {{ old('invoice_type', $item->invoice_type) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-form-error field="invoice_type" />
                            </div>

                            <!-- Fatura -->
                            <div class="col-md-4">
                                <label for="invoice-id" class="form-label">Fatura</label>
                                <select class="form-select select2" name="invoice_id" id="invoice-id" >
                                    <option value="">Fatura Seçin</option>
                                    @foreach($filteredInvoices as $invoice)
                                        @php
                                            $currencyCode = $invoice->exchangeRate ? $invoice->exchangeRate->code : 'TRY';
                                            $displayAmount = $currencyCode === 'TRY' ? $invoice->total_amount : ($invoice->total_amount_fx ?? $invoice->total_amount);
                                            $displayCollected = $currencyCode === 'TRY' ? ($invoice->collected ?? 0) : ($invoice->collected_fx ?? $invoice->collected ?? 0);
                                            $displayRemaining = $displayAmount - $displayCollected;
                                        @endphp
                                        <option value="{{ $invoice->id }}"
                                            data-current-id="{{ $invoice->current_id }}"
                                            data-current-name="{{ $invoice->current?->title }}"
                                            data-invoice-no="{{ $invoice->invoice_no }}"
                                            data-invoice-date="{{ $invoice->invoice_date?->format('Y-m-d') }}"
                                            data-total-amount="{{ number_format($displayAmount, 2, ',', '.') }}"
                                            data-collected="{{ number_format($displayCollected, 2, ',', '.') }}"
                                            data-remaining-amount="{{ number_format($displayRemaining, 2, ',', '.') }}"
                                            data-currency="{{ $currencyCode }}"
                                            data-exchange-rate="{{ $invoice->exchange_rate }}"
                                            data-exchange-rate-id="{{ $invoice->exchange_rate_id ?? '' }}"
                                            data-invoice-status="{{ $invoice->invoiceStatus?->name ?? '-' }}"
                                            data-payment-method="{{ $invoice->payment_type_id ?? '' }}"
                                            data-invoice-type-id="{{ $invoice->invoice_type_id ?? '' }}"
                                            {{ old('invoice_id', $item->invoice_id) == $invoice->id ? 'selected' : '' }}>
                                            {{ $invoice->invoice_no }} - {{ $invoice->current?->title ?? 'Cari Yok' }} - {{ number_format($displayAmount, 2, ',', '.') }} {{ $currencyCode }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-form-error field="invoice_id" />
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Cari -->
                            <div class="col-md-4">
                                <label for="current-name" class="form-label">Cari</label>
                                <input type="text" class="form-control" id="current-name" readonly>
                                <input type="hidden" name="current_id" id="current-id" value="{{ old('current_id', $item->current_id) }}" >
                            </div>

                            <!-- Ödeme Yöntemi -->
                            <div class="col-md-4">
                                <label for="payment_method" class="form-label">Ödeme Yöntemi</label>
                                <select class="form-select select2" name="payment_method" id="payment_method" >
                                    <option value="">Ödeme Yöntemi Seçin</option>
                                    @foreach($paymentMethods as $key => $value)
                                        <option value="{{ $key }}" {{ old('payment_method', $item->payment_method) == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                                <x-form-error field="payment_method" />
                            </div>

                            <!-- Tutar -->
                            <div class="col-md-4">
                                <label for="amount" class="form-label">Tutar</label>
                                <input type="number" class="form-control" name="amount" id="amount" value="{{ old('amount', $item->amount) }}" step="0.01" min="0.01" >
                                <x-form-error field="amount"/>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Para Birimi -->
                                <div class="col-md-4">
                                    <label class="form-label">Para Birimi</label>
                                    <select class="form-control" name="exchange_rate_id" id="exchange_rate_id" >
                                        @foreach ($exchangeRate as $rate)
                                            <option value="{{ $rate->id }}"
                                                    data-currency-code="{{ $rate->code }}"
                                                    data-selling-rate="{{ $rate->selling_rate }}"
                                                    data-symbol="{{ $rate->symbol }}"
                                                    {{ old('exchange_rate_id', $item->exchange_rate_id) == $rate->id ? 'selected' : ($rate->code == 'TRY' && !old('exchange_rate_id') && !$item->exchange_rate_id ? 'selected' : '') }}>
                                                {{ $rate->code }} ({{ $rate->symbol }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Kur (Sadece Gösterim İçin) -->
                                <div class="col-md-4">
                                    <label for="exchange_rate_display" class="form-label">Kur</label>
                                    <input type="number" class="form-control" id="exchange-rate" step="0.0001" min="0.0001" readonly>
                                </div>

                                 <!-- Belge No -->
                                <div class="col-md-4">
                                    <label for="document_no" class="form-label">Belge No</label>
                                    <input type="text" class="form-control" name="document_no" id="document_no" value="{{ old('document_no', $item->document_no) }}">
                                </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Banka Hesabı -->
                            <div class="col-md-4">
                                <label for="bank_account" class="form-label">Banka Hesabı</label>
                                <input type="number" class="form-control" name="bank_account" id="bank_account" value="{{ old('bank_account', $item->bank_account) }}">
                            </div>

                            <!-- Vade Tarihi -->
                            <div class="col-md-4">
                                <label for="due_date" class="form-label">Vade Tarihi</label>
                                <input type="date" class="form-control" name="due_date" id="due_date" value="{{ old('due_date', $item->due_date ? $item->due_date->format('Y-m-d') : '') }}">
                            </div>

                            <!-- Aktif/Pasif -->
                            <div class="col-4">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">

                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Açıklama -->
                            <div class="col-md-12">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" name="description" id="description" rows="3">{{ old('description', $item->description) }}</textarea>
                            </div>
                        </div>

                        <!-- Fatura Bilgileri -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Fatura Bilgileri</h5>
                                    </div>
                                    <div class="card-body" id="invoice-details">
                                        <div class="alert alert-info">
                                            Fatura seçildiğinde detaylar burada görüntülenecektir.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 text-end">
                                <a href="{{ route('backend.'.$container->page.'_list') }}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const exchangeSelect = document.getElementById('exchange_rate_id');
        const exchangeInput = document.getElementById('exchange-rate');
        const invoiceSelect = document.getElementById('invoice-id');
        const currentNameInput = document.getElementById('current-name');
        const currentIdInput = document.getElementById('current-id');
        const invoiceDetailsDiv = document.getElementById('invoice-details');
        const amountInput = document.getElementById('amount');
        const form = amountInput.closest('form');

        // Kur oranını güncelle
        exchangeSelect.addEventListener('change', function () {
            const selected = this.options[this.selectedIndex];
            document.getElementById('exchange-rate').value = selected.getAttribute('data-selling-rate');
            
            // Para birimi değiştiğinde tutar validasyonunu tekrar yap
            if (amountInput.value) {
                validateAmount();
            }
        });

        // Sayfa yüklenirken kur değerini doldur
        const initialOption = exchangeSelect.options[exchangeSelect.selectedIndex];
        if (initialOption) {
            document.getElementById('exchange-rate').value = initialOption.getAttribute('data-selling-rate');
        }

        // Fatura seçildiğinde detayları doldur
        function fillInvoiceDetails() {
            const selected = invoiceSelect.options[invoiceSelect.selectedIndex];
            if (!selected || !selected.value) {
                // Fatura seçili değilse alanları temizle
                currentNameInput.value = '';
                currentIdInput.value = '';
                invoiceDetailsDiv.innerHTML = '<div class="alert alert-info">Fatura seçildiğinde detaylar burada görüntülenecektir.</div>';
                return;
            }            const invoiceNo = selected.getAttribute('data-invoice-no') || '-';
            const invoiceDate = selected.getAttribute('data-invoice-date') || '-';
            const currentId = selected.getAttribute('data-current-id') || '';
            const currentName = selected.getAttribute('data-current-name') || '-';
            const totalAmount = selected.getAttribute('data-total-amount') || '0,00';
            const collected = selected.getAttribute('data-collected') || '0,00';
            const remainingAmount = selected.getAttribute('data-remaining-amount') || '0,00';
            const currency = selected.getAttribute('data-currency') || 'TRY';
            const exchangeRate = selected.getAttribute('data-exchange-rate') || '1.0000';
            const invoiceStatus = selected.getAttribute('data-invoice-status') || '-';
            const exchangeRateId = selected.getAttribute('data-exchange-rate-id');
            const paymentMethod = selected.getAttribute('data-payment-method');
            const invoiceTypeId = selected.getAttribute('data-invoice-type-id');
            
            // Cari bilgilerini doldur
            currentNameInput.value = currentName;
            currentIdInput.value = currentId;

            // Fatura tipini otomatik seç
            if (invoiceTypeId) {
                const invoiceTypeSelect = document.getElementById('invoice-type');
                if (invoiceTypeSelect) {
                    invoiceTypeSelect.value = invoiceTypeId;
                    // Select2 kullanılıyorsa güncelleme yap
                    if (invoiceTypeSelect.classList.contains('select2-hidden-accessible')) {
                        $(invoiceTypeSelect).trigger('change');
                    }
                }
            }

            // Ödeme yöntemini otomatik seç
            if (paymentMethod) {
                const paymentMethodSelect = document.getElementById('payment_method');
                if (paymentMethodSelect) {
                    paymentMethodSelect.value = paymentMethod;
                    // Select2 kullanılıyorsa güncelleme yap
                    if (paymentMethodSelect.classList.contains('select2-hidden-accessible')) {
                        $(paymentMethodSelect).trigger('change');
                    }
                }
            }

            // Para birimi ve exchange_rate_id'ye göre otomatik seç
            if (exchangeRateId) {
                for (let i = 0; i < exchangeSelect.options.length; i++) {
                    if (exchangeSelect.options[i].value === exchangeRateId) {
                        exchangeSelect.selectedIndex = i;
                        document.getElementById('exchange-rate').value = exchangeSelect.options[i].getAttribute('data-selling-rate');
                        // Select2 kullanılıyorsa güncelleme yap
                        if (exchangeSelect.classList.contains('select2-hidden-accessible')) {
                            $(exchangeSelect).trigger('change');
                        }
                        break;
                    }
                }
            } else {
                selectExchangeRateByCurrency(currency, exchangeRate);
            }

            // Symbol'i exchange_rate_id'ye göre bul
            let symbol = currency;
            if (exchangeRateId) {
                for (let i = 0; i < exchangeSelect.options.length; i++) {
                    if (exchangeSelect.options[i].value === exchangeRateId) {
                        symbol = exchangeSelect.options[i].getAttribute('data-symbol') || currency;
                        break;
                    }
                }
            }

            // Tutar alanını boş bırak (kullanıcı manuel girecek)
            amountInput.value = '';

            // HTML içeriği oluştur
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Fatura No:</strong> ${invoiceNo}</p>
                        <p><strong>Fatura Tarihi:</strong> ${invoiceDate}</p>
                        <p><strong>Cari:</strong> ${currentName}</p>
                        <p><strong>Durum:</strong> ${invoiceStatus}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Toplam Tutar:</strong> ${totalAmount} ${symbol}</p>
                        <p><strong>Ödenen Tutar:</strong> ${collected} ${symbol}</p>
                        <p><strong>Kalan Tutar:</strong> ${remainingAmount} ${symbol}</p>
                    </div>
                </div>
            `;

            invoiceDetailsDiv.innerHTML = html;
        }
                // Para birimine göre exchange rate seç
        function selectExchangeRateByCurrency(currency, exchangeRate) {
            const options = exchangeSelect.options;
            let selectedIndex = 0; // Varsayılan olarak ilk seçenek

            for (let i = 0; i < options.length; i++) {
                const option = options[i];
                const currencyCode = option.getAttribute('data-currency-code');
                
                if (currencyCode === currency) {
                    selectedIndex = i;
                    break;
                }
            }

            exchangeSelect.selectedIndex = selectedIndex;
            
            // Kur değerini güncelle
            const selectedOption = exchangeSelect.options[selectedIndex];
            if (selectedOption) {
                document.getElementById('exchange-rate').value = selectedOption.getAttribute('data-selling-rate');
                // Select2 kullanılıyorsa güncelleme yap
                if (exchangeSelect.classList.contains('select2-hidden-accessible')) {
                    $(exchangeSelect).trigger('change');
                }
            }
        }

        // Güncelleme işleminde mevcut değerleri koru
        function preserveExistingValues() {
            const currentExchangeRateId = '{{ old("exchange_rate_id", $item->exchange_rate_id) }}';
            if (currentExchangeRateId) {
                // Mevcut exchange_rate_id varsa, onu seç
                for (let i = 0; i < exchangeSelect.options.length; i++) {
                    const option = exchangeSelect.options[i];
                    if (option.value === currentExchangeRateId) {
                        exchangeSelect.selectedIndex = i;
                        document.getElementById('exchange-rate').value = option.getAttribute('data-selling-rate');
                        break;
                    }
                }
            }
        }



        invoiceSelect.addEventListener('change', fillInvoiceDetails);

        // Sayfa yüklenirken otomatik göster
        if (invoiceSelect.selectedIndex > 0) {
            fillInvoiceDetails();
        } else {
            // Eğer fatura seçili değilse mevcut değerleri koru
            preserveExistingValues();
        }
        // Select2 kullanılıyorsa change event'ini dinle
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('#invoice-id').on('select2:select', function() {
                fillInvoiceDetails();
            });
        }
        // Kalan tutarı faturadan al, Türkçe biçimi ondalıklı sayıya çevir
        function getRemainingAmount() {
            const selectedOption = invoiceSelect.options[invoiceSelect.selectedIndex];
            if (!selectedOption) return 0;

            let remainingAmountText = selectedOption.getAttribute('data-remaining-amount') || '0,00';
            // Örn: "1.234,56" → "1234.56"
            remainingAmountText = remainingAmountText.replace(/\./g, '').replace(',', '.');
            return parseFloat(remainingAmountText);
        }
        // Seçilen para birimine göre kalan tutarı hesapla
        function getRemainingAmountByCurrency() {
            const selectedOption = invoiceSelect.options[invoiceSelect.selectedIndex];
            const exchangeOption = exchangeSelect.options[exchangeSelect.selectedIndex];
            
            if (!selectedOption || !exchangeOption) return 0;

            const invoiceCurrency = selectedOption.getAttribute('data-currency') || 'TRY';
            const paymentCurrency = exchangeOption.getAttribute('data-currency-code') || 'TRY';
            
            // Aynı para birimi ise direkt hesapla
            if (invoiceCurrency === paymentCurrency) {
                return getRemainingAmount();
            }
            
            // Farklı para birimi ise dönüşüm yap
            const remainingAmount = getRemainingAmount();
            const invoiceExchangeRate = parseFloat(selectedOption.getAttribute('data-exchange-rate') || '1.0000');
            const paymentExchangeRate = parseFloat(exchangeOption.getAttribute('data-selling-rate') || '1.0000');
            
            if (paymentExchangeRate > 0) {
                return remainingAmount * (invoiceExchangeRate / paymentExchangeRate);
            }
            
            return remainingAmount;
        }
        // Tutarı kontrol et, kalan tutardan fazla ise false döndür
        function validateAmount() {
            const remainingAmount = getRemainingAmountByCurrency();
            let enteredAmount = parseFloat(amountInput.value);
            if (isNaN(enteredAmount)) enteredAmount = 0;

            if (enteredAmount > remainingAmount) {
                const selectedOption = exchangeSelect.options[exchangeSelect.selectedIndex];
                const currencySymbol = selectedOption ? selectedOption.getAttribute('data-symbol') || '₺' : '₺';
                alert(`Girilen tutar kalan tutardan fazla olamaz! (Kalan: ${remainingAmount.toFixed(2)} ${currencySymbol})`);
                return false;
            }
            return true;
        }

        // Form submit olmadan önce kontrol et, hata varsa engelle
        form.addEventListener('submit', function(e) {
            if (!validateAmount()) {
                e.preventDefault();  // Form gönderimini engelle
                amountInput.focus();
            }
        });

        // İstersen input anlık kontrol yapabilirsin (zorunlu değil)
        amountInput.addEventListener('input', function() {
            validateAmount();
        });
    });
</script>
@endsection
