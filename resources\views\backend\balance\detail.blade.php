@extends('layout.layout')

@php
    $title = $item->current->title . ' - Bakiye Detayları';
    $subTitle = 'Cari Bakiye Detayları';
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $title }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light">
                                <strong>Bo<PERSON><PERSON> (TL):</strong>
                                <span class="float-end text-danger fw-bold">{{ number_format($item->debit_balance, 2, ',', '.') }} ₺</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light">
                                <strong>Alacak Bakiyesi (TL):</strong>
                                <span class="float-end text-success fw-bold">{{ number_format($item->credit_balance, 2, ',', '.') }} ₺</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light">
                                <strong>Net Bakiye (TL):</strong>
                                @php $net = $item->debit_balance - $item->credit_balance; @endphp
                                <span class="float-end fw-bold {{ $net > 0 ? 'text-danger' : ($net < 0 ? 'text-success' : 'text-secondary') }}">
                                    {{ number_format($net, 2, ',', '.') }} ₺
                                </span>
                            </div>
                        </div>
                    </div>

                    <h6 class="mt-4 mb-3">Döviz Bakiyeleri</h6>
                    @if($currencies->count())
                        <div class="table-responsive">
                            <table class="table table-bordered align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Döviz Cinsi</th>
                                        <th>Borç</th>
                                        <th>Alacak</th>
                                        <th>Net Bakiye</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($currencies as $currency)
                                        @php $net = $currency->debit_balance - $currency->credit_balance; @endphp
                                        <tr>
                                            <td><strong>{{ $currency->currency_code }}</strong></td>
                                            <td class="text-danger">{{ number_format($currency->debit_balance, 2, ',', '.') }}</td>
                                            <td class="text-success">{{ number_format($currency->credit_balance, 2, ',', '.') }}</td>
                                            <td class="fw-bold {{ $net > 0 ? 'text-danger' : ($net < 0 ? 'text-success' : 'text-secondary') }}">
                                                {{ number_format($net, 2, ',', '.') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">Bu carinin döviz bakiyesi bulunmamaktadır.</div>
                    @endif
                    <div class="mt-4">
                        <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">Listeye Dön</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
