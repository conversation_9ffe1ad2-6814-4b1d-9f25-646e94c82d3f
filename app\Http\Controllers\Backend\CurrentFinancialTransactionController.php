<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportCurrentFinancialTransaction;
use App\Models\AccountVoucher;
use App\Models\Current;
use App\Models\ExpenseVoucher;
use App\Models\Invoice;
use App\Models\Transaction;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CurrentFinancialTransactionController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Finansal Hareketler';
        $this->page = 'current_financial_transactions';

        $this->view = (object)[
            'breadcrumb' => [
                'Finans' => '#',
                'Cari Finansal Hareketler' => route('backend.current_financial_transactions_list'),
            ],
        ];
        view()->share('currents', Current::where('is_active', 1)->get());
        parent::__construct();
    }

    public function list(Request $request)
    {
        if ($request->has('datatable')) {
            $data = collect();

            // Faturalar
            $invoices = Invoice::with('current', 'invoiceType', 'exchangeRate')
                ->when($request->current_id, function($q) use ($request) {
                    $q->where('current_id', $request->current_id);
                })
                ->when($request->start_date, function($q) use ($request) {
                    $q->whereDate('invoice_date', '>=', $request->start_date);
                })
                ->when($request->end_date, function($q) use ($request) {
                    $q->whereDate('invoice_date', '<=', $request->end_date);
                })
                ->get()
                ->map(function($item) {
                    $islemTipi = '';
                    switch($item->invoice_type_id) {
                        case 1:
                            $islemTipi = 'Alış Faturası';
                            break;
                        case 2:
                            $islemTipi = 'Satış Faturası';
                            break;
                        case 3:
                            $islemTipi = 'Alış İade Faturası';
                            break;
                        case 4:
                            $islemTipi = 'Satış İade Faturası';
                            break;
                        default:
                            $islemTipi = 'Fatura';
                    }
                    
                    // TRY karşılığını hesapla
                    $currencyCode = $item->exchangeRate ? $item->exchangeRate->code : 'TRY';
                    
                    if ($currencyCode !== 'TRY') {
                        // Dövizli işlem - total_amount_fx kullan
                        $tutar = $item->total_amount_fx ?? 0;
                        $tryKarsiligi = $tutar * $item->exchangeRate->selling_rate;
                    } else {
                        // TRY işlem - total_amount kullan
                        $tutar = $item->total_amount ?? 0;
                        $tryKarsiligi = $tutar;
                    }
                    
                    return [
                        'islem_no' => $item->invoice_no,
                        'cari' => $item->current->title ?? '-',
                        'tarih' => $item->invoice_date ? $item->invoice_date->format('d.m.Y H:i') : '-',
                        'islem_tipi' => $islemTipi,
                        'tutar' => $tutar,
                        'payment_type_name' => '',
                        'kaynak' => 'invoice',
                        'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                        'currency_code' => $currencyCode,
                        'try_karsiligi' => $tryKarsiligi,
                    ];
                });

            // Cari Hesap Fişleri
            $accountVouchers = AccountVoucher::with('current', 'voucherType', 'paymentType', 'exchangeRate')
                ->when($request->current_id, function($q) use ($request) {
                    $q->where('current_id', $request->current_id);
                })
                ->when($request->start_date, function($q) use ($request) {
                    $q->whereDate('voucher_date', '>=', $request->start_date);
                })
                ->when($request->end_date, function($q) use ($request) {
                    $q->whereDate('voucher_date', '<=', $request->end_date);
                })
                ->get()
                ->map(function($item) {
                    // TRY karşılığını hesapla
                    $tutar = $item->amount ?? $item->total ?? 0;
                    $tryKarsiligi = 0;
                    $currencyCode = 'TRY';
                    
                    if ($item->exchangeRate && $item->exchangeRate->code !== 'TRY') {
                        $currencyCode = $item->exchangeRate->code;
                        $tryKarsiligi = $tutar * $item->exchangeRate->selling_rate;
                    } else {
                        $tryKarsiligi = $tutar;
                    }
                    
                    return [
                        'islem_no' => $item->voucher_no,
                        'cari' => $item->current->title ?? '-',
                        'tarih' => $item->voucher_date ? $item->voucher_date->format('d.m.Y H:i') : '-',
                        'islem_tipi' => $item->voucherType->name ?? 'Cari Hesap Fişi',
                        'tutar' => $tutar,
                        'payment_type_name' => $item->paymentType->name ?? '',
                        'kaynak' => 'account_voucher',
                        'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                        'currency_code' => $currencyCode,
                        'try_karsiligi' => $tryKarsiligi,
                    ];
                });

            // Tahsilat ve Ödeme İşlemleri
            $transactions = Transaction::with('current', 'transactionType', 'paymentType', 'exchangeRate')
                ->when($request->current_id, function($q) use ($request) {
                    $q->where('current_id', $request->current_id);
                })
                ->when($request->start_date, function($q) use ($request) {
                    $q->whereDate('transaction_date', '>=', $request->start_date);
                })
                ->when($request->end_date, function($q) use ($request) {
                    $q->whereDate('transaction_date', '<=', $request->end_date);
                })
                ->get()
                ->map(function($item) {
                    // TRY karşılığını hesapla
                    $tutar = $item->amount ?? 0;
                    $tryKarsiligi = 0;
                    $currencyCode = 'TRY';
                    
                    if ($item->exchangeRate && $item->exchangeRate->code !== 'TRY') {
                        $currencyCode = $item->exchangeRate->code;
                        $tryKarsiligi = $tutar * $item->exchangeRate->selling_rate;
                    } else {
                        $tryKarsiligi = $tutar;
                    }
                    
                    return [
                        'islem_no' => $item->id,
                        'cari' => $item->current->title ?? '-',
                        'tarih' => $item->transaction_date ? $item->transaction_date->format('d.m.Y H:i') : '-',
                        'islem_tipi' => $item->transaction_type_id == 1 ? 'Ödeme' : 'Tahsilat',
                        'tutar' => $tutar,
                        'payment_type_name' => $item->paymentType->name ?? '',
                        'kaynak' => $item->transaction_type_id == 1 ? 'payment' : 'collection',
                        'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                        'currency_code' => $currencyCode,
                        'try_karsiligi' => $tryKarsiligi,
                    ];
                });

            // Hepsini birleştir
            $data = $data->merge($invoices)
                         ->merge($accountVouchers)
                         ->merge($transactions);

            // İşlem tipi filtresi uygula
            if ($request->transaction_type) {
                if ($request->transaction_type === 'purchase_invoice') {
                    $data = $data->where('kaynak', 'invoice')->where('islem_tipi', 'Alış Faturası');
                } elseif ($request->transaction_type === 'sales_invoice') {
                    $data = $data->where('kaynak', 'invoice')->where('islem_tipi', 'Satış Faturası');
                } elseif ($request->transaction_type === 'purchase_return_invoice') {
                    $data = $data->where('kaynak', 'invoice')->where('islem_tipi', 'Alış İade Faturası');
                } elseif ($request->transaction_type === 'sales_return_invoice') {
                    $data = $data->where('kaynak', 'invoice')->where('islem_tipi', 'Satış İade Faturası');
                } else {
                    $data = $data->where('kaynak', $request->transaction_type);
                }
            }

            // Tarihe göre sıralama (en yeni en üstte)
            $data = $data->sortByDesc('tarih')->values();

            // DataTable ile döndür
            return datatables()->of($data)->make(true);
        }

        return view("backend.$this->page.list");
    }
    public function exportExcel(Request $request, $unique = null)
    {
        $data = collect();

        // Faturalar
        $invoices = Invoice::with('current', 'invoiceType', 'exchangeRate')
            ->get()
            ->map(function($item) {
                $islemTipi = '';
                switch($item->invoice_type_id) {
                    case 1:
                        $islemTipi = 'Alış Faturası';
                        break;
                    case 2:
                        $islemTipi = 'Satış Faturası';
                        break;
                    case 3:
                        $islemTipi = 'Alış İade Faturası';
                        break;
                    case 4:
                        $islemTipi = 'Satış İade Faturası';
                        break;
                    default:
                        $islemTipi = 'Fatura';
                }
                
                // TRY karşılığını hesapla
                $currencyCode = $item->exchangeRate ? $item->exchangeRate->code : 'TRY';
                
                if ($currencyCode !== 'TRY') {
                    // Dövizli işlem - total_amount_fx kullan
                    $tutar = $item->total_amount_fx ?? 0;
                    $tryKarsiligi = $item->total_amount ?? 0;
                } else {
                    // TRY işlem - total_amount kullan
                    $tutar = $item->total_amount ?? 0;
                    $tryKarsiligi = $tutar;
                }
                
                return [
                    'invoice_no' => $item->invoice_no,
                    'current_name' => $item->current->title ?? '-',
                    'invoice_date' => $item->invoice_date ? $item->invoice_date->format('Y-m-d H:i:s') : '',
                    'transaction_type_name' => $islemTipi,
                    'total_amount' => $tutar,
                    'transaction_type' => 'invoice',
                    'try_karsiligi' => $tryKarsiligi,
                    'currency_code' => $currencyCode,
                    'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                ];
            });

        // Cari Hesap Fişleri
        $accountVouchers = AccountVoucher::with('current', 'voucherType', 'paymentType', 'exchangeRate')
            ->get()
            ->map(function($item) {
                // TRY karşılığını hesapla
                $tutar = $item->amount ?? $item->total ?? 0;
                $tryKarsiligi = 0;
                $currencyCode = 'TRY';
                
                if ($item->exchangeRate && $item->exchangeRate->code !== 'TRY') {
                    $currencyCode = $item->exchangeRate->code;
                    $tryKarsiligi = $tutar * $item->exchangeRate->selling_rate;
                } else {
                    $tryKarsiligi = $tutar;
                }
                
                return [
                    'invoice_no' => $item->voucher_no,
                    'current_name' => $item->current->title ?? '-',
                    'invoice_date' => $item->voucher_date ? $item->voucher_date->format('Y-m-d H:i:s') : '',
                    'transaction_type_name' => $item->voucherType->name ?? 'Cari Hesap Fişi',
                    'total_amount' => $item->amount ?? $item->total ?? 0,
                    'transaction_type' => 'account_voucher',
                    'try_karsiligi' => $tryKarsiligi,
                    'currency_code' => $currencyCode,
                    'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                ];
            });

        // Tahsilat ve Ödeme İşlemleri
        $transactions = Transaction::with('current', 'transactionType', 'paymentType', 'exchangeRate')
            ->get()
            ->map(function($item) {
                // TRY karşılığını hesapla
                $tutar = $item->amount ?? 0;
                $tryKarsiligi = 0;
                $currencyCode = 'TRY';
                
                if ($item->exchangeRate && $item->exchangeRate->code !== 'TRY') {
                    $currencyCode = $item->exchangeRate->code;
                    $tryKarsiligi = $tutar * $item->exchangeRate->selling_rate;
                } else {
                    $tryKarsiligi = $tutar;
                }
                
                return [
                    'invoice_no' => $item->id,
                    'current_name' => $item->current->title ?? '-',
                    'invoice_date' => $item->transaction_date ? $item->transaction_date->format('Y-m-d H:i:s') : '',
                    'transaction_type_name' => $item->transaction_type_id == 1 ? 'Ödeme' : 'Tahsilat',
                    'total_amount' => $item->amount ?? 0,
                    'transaction_type' => $item->transaction_type_id == 1 ? 'payment' : 'collection',
                    'try_karsiligi' => $tryKarsiligi,
                    'currency_code' => $currencyCode,
                    'currency_symbol' => $item->exchangeRate ? $item->exchangeRate->symbol : '₺',
                ];
            });

        $data = $data->merge($invoices)
                     ->merge($accountVouchers)
                     ->merge($transactions);

        $exporter = new ExportCurrentFinancialTransaction($data);
        $spreadsheet = $exporter->export();

        // Excel çıktısı için response döndür
        $writer = new Xlsx($spreadsheet);
        $filename = 'cari_finasal_hareketleri.xlsx';

        // Response ile dosya indirme
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        $writer->save('php://output');
        exit;
    }
}
