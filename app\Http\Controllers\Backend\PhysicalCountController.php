<?php

namespace App\Http\Controllers\Backend;

use App\Models\PhysicalCount;
use App\Models\PhysicalCountItem;
use App\Models\Product;
use App\Models\Status;
use App\Models\Stock;
use App\Models\StockMovement;
use App\Models\StockMovementReason;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Services\ExchangeRateService;
use App\Models\ExchangeRate;

class PhysicalCountController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Fiziksel Sayımlar';
        $this->page = 'physical_count';
        $this->model = new PhysicalCount();
        $this->relation = ['items', 'warehouse', 'location', 'status', 'approver'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Yönetimi' => '#',
                'Fiziksel Sayımlar' => route('backend.physical_count_list'),
            ),
        );

        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('statuses', Status::active()->get());
        view()->share('products', Product::active()->get());

        parent::__construct();
    }

    public function status(Request $request)
    {
        $count = PhysicalCount::find($request->id);

        if (!$count) {
            return response()->json(['status' => false, 'message' => 'Sayım bulunamadı']);
        }

        DB::beginTransaction();

        try {
            $previousStatus = $count->status_id;
            $count->status_id = $request->status_id;

            if ($request->status_id == 2 && $previousStatus != 2) {
                $count->approver_id = Auth::user()->id;
                $count->approval_date = now();
            }

            $count->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Durum güncellendi']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Fiziksel sayım işlemi sırasında bir hata oluştu']);
        }
    }

    public function detail(Request $request, $physical_count_id = null, $unique = null)
    {
        $physicalCount = PhysicalCount::with(['items.product', 'items.variant', 'warehouse', 'location'])->find($physical_count_id);

        if (!$physicalCount) {
            return redirect()->route('backend.physical_count_list')->with('error', 'Sayım bulunamadı');
        }

        if ($physicalCount->items->count() == 0) {
            $this->createCountItems($physicalCount);

            $physicalCount->load(['items.product', 'items.variant']);
        }

        return view("backend.$this->page.detail", compact('physicalCount'));
    }

    private function createCountItems($physicalCount)
    {
        $query = Stock::where('warehouse_id', $physicalCount->warehouse_id)
            ->where('is_active', 1);

        if ($physicalCount->location_id) {
            $query->where('warehouse_location_id', $physicalCount->location_id);
        }

        $stocks = $query->with(['product', 'variant'])->get();

        if ($stocks->count() == 0) {
            $products = Product::where('is_active', 1)->get();

            foreach ($products as $product) {
                PhysicalCountItem::create([
                    'physical_count_id' => $physicalCount->id,
                    'product_id' => $product->id,
                    'variant_id' => null,
                    'system_quantity' => 0,
                    'counted_quantity' => 0,
                    'difference' => 0,
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        } else {
            foreach ($stocks as $stock) {
                PhysicalCountItem::create([
                    'physical_count_id' => $physicalCount->id,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id,
                    'system_quantity' => $stock->quantity,
                    'counted_quantity' => 0,
                    'difference' => -$stock->quantity,
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        }
    }

    public function refreshStock($id)
    {
        DB::statement('SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED');

        $physicalCount = PhysicalCount::with(['items.product', 'items.variant'])->find($id);

        if (!$physicalCount) {
            Log::error('Sayım bulunamadı', ['id' => $id]);
            return response()->json(['status' => false, 'message' => 'Sayım bulunamadı']);
        }

        if ($physicalCount->status_id != 2) {
            Log::error('Sayım onaylanmamış', ['status_id' => $physicalCount->status_id, 'id' => $id]);
            return response()->json(['status' => false, 'message' => 'Sadece onaylanmış sayımlar için stok güncellemesi yapılabilir']);
        }

        $updatedCount = 0;
        $errorCount = 0;
        $errorMessages = [];

        DB::beginTransaction();
        try {
            $itemsWithDifference = $physicalCount->items->filter(function($item) {
                return $item->difference != 0;
            });

            if ($itemsWithDifference->count() > 0) {
                $girisItems = $itemsWithDifference->filter(function($item) { return $item->difference > 0; });
                $cikisItems = $itemsWithDifference->filter(function($item) { return $item->difference < 0; });

                $girisTotalAmount = 0;
                if ($girisItems->count() > 0) {
                                        foreach ($girisItems as $item) {
                        $priceInfo = $this->getPriceInfo($item);
                        $purchasePrice = $priceInfo['price'];
                        $currencyCode = $priceInfo['currency_code'];
                        $exchangeRate = $this->getExchangeRate($currencyCode);

                        $itemAmountInTry = ($item->difference * $purchasePrice) * $exchangeRate;
                        $girisTotalAmount += $itemAmountInTry;
                    }



                    $girisMovementId = DB::table('stock_movements')->insertGetId([
                        'movement_no' => $physicalCount->count_code . '-Giriş-',
                        'movement_date' => now(),
                        'status_id' => 2,
                        'stock_movement_type_id' => 1,
                        'stock_movement_reason_id' => 4,
                        'warehouse_id' => $physicalCount->warehouse_id,
                        'location_id' => $physicalCount->location_id,
                        'starter_id' => Auth::user()->id,
                        'approver_id' => Auth::user()->id,
                        'approval_date' => now(),
                        'total_amount' => $girisTotalAmount,
                        'currency_code' => 'TRY',
                        'notes' => 'Fiziksel sayım düzeltmesi (Giriş) - Sayım No: ' . $physicalCount->count_code,
                        'is_active' => 1,
                        'created_by' => Auth::user()->id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }

                $cikisTotalAmount = 0;
                if ($cikisItems->count() > 0) {
                                        foreach ($cikisItems as $item) {
                        $priceInfo = $this->getPriceInfo($item);
                        $purchasePrice = $priceInfo['price'];
                        $currencyCode = $priceInfo['currency_code'];
                        $exchangeRate = $this->getExchangeRate($currencyCode);

                        $itemAmountInTry = (abs($item->difference) * $purchasePrice) * $exchangeRate;
                        $cikisTotalAmount += $itemAmountInTry;
                    }



                    $cikisMovementId = DB::table('stock_movements')->insertGetId([
                        'movement_no' => $physicalCount->count_code . '-Çıkış-',
                        'movement_date' => now(),
                        'status_id' => 2,
                        'stock_movement_type_id' => 2,
                        'stock_movement_reason_id' => 9,
                        'warehouse_id' => $physicalCount->warehouse_id,
                        'location_id' => $physicalCount->location_id,
                        'starter_id' => Auth::user()->id,
                        'approver_id' => Auth::user()->id,
                        'approval_date' => now(),
                        'total_amount' => $cikisTotalAmount,
                        'currency_code' => 'TRY',
                        'notes' => 'Fiziksel sayım düzeltmesi (Çıkış) - Sayım No: ' . $physicalCount->count_code,
                        'is_active' => 1,
                        'created_by' => Auth::user()->id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            foreach ($physicalCount->items as $item) {
                if ($item->difference == 0) {
                    continue;
                }

                try {
                    $stockRow = DB::table('stocks')
                        ->select('id', 'quantity')
                        ->where('product_id', $item->product_id)
                        ->where('warehouse_id', $physicalCount->warehouse_id)
                        ->where(function($q) use ($item) {
                            if ($item->variant_id) {
                                $q->where('variant_id', $item->variant_id);
                            } else {
                                $q->whereNull('variant_id');
                            }
                        })
                        ->where(function($q) use ($physicalCount) {
                            if ($physicalCount->location_id) {
                                $q->where('warehouse_location_id', $physicalCount->location_id);
                            } else {
                                $q->whereNull('warehouse_location_id');
                            }
                        })
                        ->first();

                    $stockId = null;

                    if (!$stockRow) {
                        $stockId = DB::table('stocks')->insertGetId([
                            'product_id' => $item->product_id,
                            'variant_id' => $item->variant_id,
                            'warehouse_id' => $physicalCount->warehouse_id,
                            'warehouse_location_id' => $physicalCount->location_id,
                            'quantity' => $item->counted_quantity,
                            'is_active' => 1,
                            'created_by' => Auth::user()->id,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    } else {
                        $stockId = $stockRow->id;

                        DB::table('stocks')
                            ->where('id', $stockId)
                            ->update([
                                'quantity' => $item->counted_quantity,
                                'updated_at' => now(),
                                'updated_by' => Auth::user()->id
                            ]);
                    }

                    $movementId = $item->difference > 0 ? $girisMovementId : $cikisMovementId;

                    if (isset($movementId)) {
                        $priceInfo = $this->getPriceInfo($item);
                        $purchasePrice = $priceInfo['price'];
                        $currencyCode = $priceInfo['currency_code'];
                        $quantity = abs($item->difference);
                        $exchangeRate = $this->getExchangeRate($currencyCode);
                        $totalPrice = $quantity * $purchasePrice;

                        DB::table('stock_movement_items')->insert([
                            'stock_movement_id' => $movementId,
                            'stock_id' => $stockId,
                            'product_id' => $item->product_id,
                            'variant_id' => $item->variant_id,
                            'quantity' => $quantity,
                            'unit_price' => $purchasePrice,
                            'total_price' => $totalPrice,
                            'currency_code' => $currencyCode,
                            'exchange_rate' => $exchangeRate,
                            'created_by' => Auth::user()->id,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);


                    }

                    $updatedCount++;
                } catch (\Exception $itemError) {
                    $errorCount++;
                    $errorMessages[] = "Ürün ID: {$item->product_id} için hata: " . $itemError->getMessage();
                    continue;
                }
            }

            if ($errorCount > 0) {
                $message = "İşlem kısmen tamamlandı. $updatedCount kalem başarıyla güncellendi, $errorCount kalemde hata oluştu.";
                $status = false;
            } else {
                $message = "Stok güncelleme işlemi başarıyla tamamlandı. $updatedCount kalem güncellendi.";
                $status = true;
            }

            DB::commit();

            return response()->json([
                'status' => $status,
                'message' => $message,
                'updated_count' => $updatedCount,
                'error_count' => $errorCount,
                'error_details' => $errorMessages
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => false,
                'message' => 'Stok güncelleme işlemi sırasında bir hata oluştu'
            ]);
        }
    }

    private function getPriceInfo($item)
    {
        if ($item->variant_id && $item->variant) {
            return [
                'price' => $item->variant->purchase_price ?? 0,
                'currency_code' => $item->variant->purchase_currency_code ?? 'TRY'
            ];
        }

        return [
            'price' => $item->product->purchase_price ?? 0,
            'currency_code' => $item->product->purchase_currency_code ?? 'TRY'
        ];
    }

    private function getExchangeRate($currencyCode)
    {
        if ($currencyCode === 'TRY') {
            return 1.0;
        }

        $exchangeRate = ExchangeRate::where('code', $currencyCode)
            ->where('is_active', true)
            ->orderBy('rate_date', 'desc')
            ->first();

        if ($exchangeRate) {
            return $exchangeRate->buying_rate;
        }

        Log::warning('Döviz kuru bulunamadı', ['currency_code' => $currencyCode]);
        return 1.0;
    }

    private function verifyStockUpdates($physicalCount)
    {
        DB::statement('COMMIT');

        foreach ($physicalCount->items as $item) {
            if ($item->difference == 0) continue;

            $stockFromDB = DB::table('stocks')
                ->select('id', 'product_id', 'variant_id', 'quantity')
                ->where('product_id', $item->product_id)
                ->where(function($q) use ($item) {
                    if ($item->variant_id) {
                        $q->where('variant_id', $item->variant_id);
                    } else {
                        $q->whereNull('variant_id');
                    }
                })
                ->where('warehouse_id', $physicalCount->warehouse_id)
                ->where(function($q) use ($physicalCount) {
                    if ($physicalCount->location_id) {
                        $q->where('warehouse_location_id', $physicalCount->location_id);
                    } else {
                        $q->whereNull('warehouse_location_id');
                    }
                })
                ->first();

            if ($stockFromDB) {

                if ($stockFromDB->quantity != $item->counted_quantity) {
                    Log::warning('ZORLA GÜNCELLEME: Stok miktarı eşleşmiyor!', [
                        'stock_id' => $stockFromDB->id,
                        'mevcut_miktar' => $stockFromDB->quantity,
                        'olması_gereken' => $item->counted_quantity
                    ]);

                    DB::statement('UPDATE stocks SET quantity = ? WHERE id = ?', [
                        $item->counted_quantity,
                        $stockFromDB->id
                    ]);

                    $finalCheck = DB::table('stocks')->where('id', $stockFromDB->id)->first();
                }
            }
        }
    }

    public function save(Request $request, $unique = NULL)
    {
        if ($request->expectsJson() || $request->ajax() || $request->wantsJson()) {
            DB::beginTransaction();
            try {
                if (isset($request->items) && is_array($request->items)) {
                    foreach ($request->items as $itemId => $itemData) {
                        $countItem = PhysicalCountItem::find($itemId);
                        if ($countItem) {
                            $countItem->counted_quantity = $itemData['counted_quantity'] ?? $countItem->counted_quantity;
                            $countItem->notes = $itemData['notes'] ?? $countItem->notes;

                            $countItem->difference = $countItem->counted_quantity - $countItem->system_quantity;
                            $countItem->save();
                        }
                    }
                }

                $count = PhysicalCount::find($request->id);
                if ($count) {
                    if (isset($request->description)) {
                        $count->description = $request->description;
                    }
                    $count->save();
                }

                DB::commit();

                return response()->json([
                    'status' => true,
                    'message' => 'Sayım başarıyla güncellendi'
                ]);
            } catch (\Exception $e) {
                DB::rollback();

                Log::error('Physical Count Save Error', [
                    'id' => $request->id,
                    'error' => 'Fiziksel sayım kaydedilirken bir hata oluştu',
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'status' => false,
                    'message' => 'Fiziksel sayım kaydedilirken bir hata oluştu'
                ]);
            }
        }

        DB::beginTransaction();
        try {
            $params = $request->all();

            if (isset($params['items']) && is_array($params['items'])) {
                foreach ($params['items'] as $itemId => $itemData) {
                    $countItem = PhysicalCountItem::find($itemId);
                    if ($countItem) {
                        $countItem->counted_quantity = $itemData['counted_quantity'] ?? $countItem->counted_quantity;
                        $countItem->notes = $itemData['notes'] ?? $countItem->notes;

                        $countItem->difference = $countItem->counted_quantity - $countItem->system_quantity;
                        $countItem->save();
                    }
                }
                unset($params['items']);
            }

            if (is_null($unique) && (!isset($params['count_code']) || empty($params['count_code']))) {
                $params['count_code'] = 'SAYIM-' . date('YmdHis') . '-';
            }

            if (!is_null($unique) && (!isset($params['count_code']) || empty($params['count_code']))) {
                $existingRecord = PhysicalCount::find($unique);
                if ($existingRecord) {
                    $params['count_code'] = $existingRecord->count_code;
                }
            }

            if (!isset($params['status_id'])) {
                $params['status_id'] = 1;
            }

            if ($unique) {
                $physicalCount = PhysicalCount::find($unique);
                if ($physicalCount) {
                    $physicalCount->update($params);
                } else {
                    $physicalCount = PhysicalCount::create($params);
                }
            } else {
                $physicalCount = PhysicalCount::create($params);
            }

            if ($physicalCount->items->count() == 0) {
                $this->createCountItems($physicalCount);
            }

            DB::commit();

            return redirect()->route("backend.physical_count_detail", $physicalCount->id)
                ->with('success', 'Sayım başarıyla kaydedildi.');

        } catch (\Exception $e) {
            DB::rollback();

            Log::error('Physical Count Save Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Fiziksel sayım kaydedilirken bir hata oluştu.');
        }
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('count_date', function ($item) {
            if ($item->count_date instanceof \Carbon\Carbon) {
                return $item->count_date->format('d.m.Y H:i');
            } elseif (is_string($item->count_date)) {
                return \Carbon\Carbon::parse($item->count_date)->format('d.m.Y H:i');
            }
            return $item->count_date;
        });
    }
}
