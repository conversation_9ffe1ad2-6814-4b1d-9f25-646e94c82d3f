<?php

namespace Database\Seeders;

use App\Models\StockReservationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StockReservationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                ['name' => 'Satış ', 'description' => 'Satış siparişi için rezervasyon'],
                ['name' => 'Alış', 'description' => 'Alış siparişi için beklenen miktar'],
                ['name' => 'Satış İade', 'description' => 'Satış iade için rezervasyon'],
                ['name' => 'Alış İade', 'description' => 'Alış iade için rezervasyon'],
                ['name' => 'Üretim', 'description' => 'Üretim için rezervasyon'],
                ['name' => 'Transfer', 'description' => 'Transfer için rezervasyon'],
                ['name' => 'Diğer', 'description' => 'Diğer rezervasyonlar'],
            ];

            foreach ($types as $type) {
                StockReservationType::updateOrCreate(
                    ['name' => $type['name']],
                    $type
                );
            }
        });

        $this->command->info('StockReservationType verileri başarıyla oluşturuldu.');
    }
}
