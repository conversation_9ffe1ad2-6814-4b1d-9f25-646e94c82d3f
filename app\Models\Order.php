<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Order extends  BaseModel
{
    use SoftDeletes;

    protected $table = 'orders';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }

    public function current()
    {
        return $this->belongsTo(Current::class)->withTrashed();
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }

    public function orderType()
    {
        return $this->belongsTo(OrderType::class);
    }

    public static function generateOrderNumber($orderTypeId = null)
    {
        $datePrefix = Carbon::now()->format('Ymd'); // 20250429
        $maxSequence = 0;
        $orders = self::withTrashed()
            ->where('order_number', 'like', $datePrefix . '%')
            ->get(['order_number']);

        foreach ($orders as $order) {
            $sequence = (int)substr($order->order_number, -2);
            if ($sequence > $maxSequence) {
                $maxSequence = $sequence;
            }
        }

        $nextSequence = str_pad($maxSequence + 1, 5, '0', STR_PAD_LEFT);

        return $datePrefix . $nextSequence;
    }

    public function orderProducts()
    {
        return $this->hasMany(OrderProduct::class);
    }

    public function processOrderProducts(array $products): void
    {
        $existingProducts = OrderProduct::withTrashed()
            ->where('order_id', $this->id)
            ->get()
            ->keyBy('stock_id');

        $incomingStockIds = [];
        foreach ($products as $stockId => $line) {
            // Stock ID artık array key'i olarak geliyor
            $stockId = $line['stock_id'] ?? $stockId;
            $incomingStockIds[] = $stockId;
            $stock = Stock::find($stockId);
            
            if (!$stock) {
                continue; // Stock bulunamazsa atla
            }
            
            $exchangeRate = ExchangeRate::find($line['exchange_rate_id']);
            $currencyType = $exchangeRate ? ($exchangeRate->currency_code ?? $exchangeRate->code) : 'TRY';
            $orderNumber = $this->order_number;
            $itemNo = $line['item_no'] ?? ($existingProducts[$stockId]->item_no ?? OrderProduct::generateItemNo($orderNumber));
            
            $orderProductData = [
                'order_id' => $this->id,
                'stock_id' => $stockId,
                'product_id' => $stock->product_id,
                'product_variant_id' => $stock->variant_id,
                'quantity' => $line['quantity'],
                'unit_price' => $line['price'],
                'exchange_rate_id' => $line['exchange_rate_id'],
                'exchange_rate' => $line['exchange_rate'],
                'currency_type' => $currencyType,
                'vat_rate' => $line['vat_rate'],
                'vat_status' => $line['vat_status'] ?? 0, // vat_included yerine vat_status
                'total_price' => $line['amount'],
                'item_no' => $itemNo,
                'is_active' => 1
            ];
            
            if (isset($existingProducts[$stockId])) {
                $existingProduct = $existingProducts[$stockId];
                $existingProduct->fill($orderProductData);
                $existingProduct->deleted_at = null;
                $existingProduct->save();
            } else {
                OrderProduct::create($orderProductData);
            }
        }
        
        $toDelete = $existingProducts->keys()->diff($incomingStockIds);
        foreach ($toDelete as $stockId) {
            $product = $existingProducts[$stockId];
            if (!$product->deleted_at) {
                $product->delete();
            }
        }
    }

    // Onaylanmış veya faturalı ürün içeriyor mu?
    public function hasApprovedProducts(): bool
    {
        return $this->orderProducts()
            ->whereIn('status', [1, 3])
            ->exists();
    }

    // Deleting event hook - boot() içinde
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($order) {
            if ($order->hasApprovedProducts()) {
                throw new \Exception('Onaylanmış veya faturaya aktarılmış ürün içeren sipariş silinemez.');
            }

            foreach ($order->orderProducts as $product) {
                $product->delete();
            }
        });
    }
    public function scopeFilter($query, $filter)
    {
        // Sadece order_date'e göre filtreleme, saat aralığı ile
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('order_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        } elseif (!is_null($filter->start_date) && !empty($filter->start_date)) {
            $query->whereDate('order_date', Carbon::parse($filter->start_date)->toDateString());
        }

        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }
        return $query;
    }
}
