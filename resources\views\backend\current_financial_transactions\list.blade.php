@extends('layout.layout')

@php
    $title = $container->title ?? 'Cari Finansal Hareketler';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <button type="button" id="btn-export-excel"
                    class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                    Excel
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- Filtreler -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">Cari</label>
                    <select class="form-control select2" id="filter-current" filter-name="filter-current">
                        <option value="">Cari <PERSON></option>
                        @foreach($currents as $cari)
                            <option value="{{ $cari->id }}">{{ $cari->title }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">İşlem Tipi</label>
                    <select class="form-control select2" id="filter-transaction-type">
                        <option value="">Lütfen İşlem Tipi Seçiniz</option>
                        <option value="purchase_invoice">Alış Faturası</option>
                        <option value="sales_invoice">Satış Faturası</option>
                        <option value="purchase_return_invoice">Alış İade Faturası</option>
                        <option value="sales_return_invoice">Satış İade Faturası</option>
                        <option value="account_voucher">Cari Hesap Fişi</option>
                        <option value="payment">Ödeme</option>
                        <option value="collection">Tahsilat</option>
                    </select>
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="25">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">İşlem No</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center">Tarih</th>
                            <th scope="col" class="text-center">İşlem Tipi</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">İşlem</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">İşlem Detayı</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div><b>Belge No:</b> <span id="modal_islem_no"></span></div>
                <div><b>Belge Tarihi:</b> <span id="modal_tarih"></span></div>
                <div><b>Cari Hesap:</b> <span id="modal_cari"></span></div>
                <div><b>İşlem Tipi:</b> <span id="modal_islem_tipi"></span><span id="modal_payment_type_name"
                        style="font-weight:normal;"></span></div>
                <div><b>Tutar:</b> <span id="modal_tutar"></span></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>
@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        d.current_id = $('#filter-current').val();
                        d.start_date = $('#filter-start-date').val();
                        d.end_date = $('#filter-end-date').val();
                        d.transaction_type = $('#filter-transaction-type').val();
                        return d;
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'islem_no',
                        name: 'islem_no',
                        className: 'text-center'
                    },
                    {
                        data: 'cari',
                        name: 'cari',
                        className: 'text-center'
                    },
                    {
                        data: 'tarih',
                        name: 'tarih',
                        className: 'text-center'
                    },
                    {
                        data: 'islem_tipi',
                        name: 'islem_tipi',
                        className: 'text-center'
                    },
                    {
                        data: 'tutar',
                        name: 'tutar',
                        className: 'text-center',
                        render: function(data, type, row) {
                            var symbol = row.currency_symbol ? row.currency_symbol : '';
                            return formatCurrency(data) + ' ' + symbol;
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center d-flex align-items-center justify-content-center',
                        render: function(data, type, row) {
                            return `<button type="button" class="bg-primary-light text-primary-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle btn-view-detail" 
                                data-islem_no="${row.islem_no ?? ''}"
                                data-tarih="${row.tarih ?? ''}"
                                data-cari="${row.cari ?? ''}"
                                data-islem_tipi="${row.islem_tipi ?? ''}"
                                data-tutar="${formatCurrency(row.tutar ?? '')} ${row.currency_symbol ?? '₺'}"
                                data-payment_type_name="${row.payment_type_name ?? ''}"
                                data-try_karsiligi="${formatCurrency(row.try_karsiligi ?? '')} ₺"
                                data-currency_code="${row.currency_code ?? 'TRY'}">
                                <iconify-icon icon="mdi:eye" class="menu-icon"></iconify-icon>
                            </button>`;
                        }
                    }
                ],
                order: [
                    [2, 'desc']
                ],
                pageLength: 15,
            });
            // Para birimi formatla
            function formatCurrency(value) {
                return parseFloat(value).toLocaleString('tr-TR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
            // Filtre değişikliklerinde tabloyu yenile
            $('#filter-current,#filter-start-date, #filter-end-date, #filter-transaction-type').on('change', function() {
                table.ajax.reload();
            });
        });

        $(document).on('click', '.btn-view-detail', function() {
            const islemNo = $(this).data('islem_no');
            const tarih = $(this).data('tarih');
            const cari = $(this).data('cari');
            const islemTipi = $(this).data('islem_tipi') + ($(this).data('payment_type_name') ? ' - ' + $(this).data('payment_type_name') : '');
            const tutar = $(this).data('tutar');
            const tryKarsiligi = $(this).data('try_karsiligi');
            const currencyCode = $(this).data('currency_code');
            
            const fields = [
                ['Belge No', islemNo],
                ['Belge Tarihi', tarih],
                ['Cari Hesap', cari],
                ['İşlem Tipi', islemTipi],
                ['Tutar', tutar]
            ];
            
            // Eğer döviz TRY'den farklıysa TRY karşılığını da göster
            if (currencyCode && currencyCode !== 'TRY') {
                fields.push(['TRY Karşılığı', tryKarsiligi]);
            }
            const content = '<table class="table basic-border-table mb-0">' +
                fields.map(([label, value]) =>
                    `<tr><td width="30%" class="fw-bold">${label}</td><td>${value}</td></tr>`
                ).join('') +
                '</table>';
            const modalId = 'detailModal';
            const title = 'İşlem Detayı';
            // Modal içeriğini güncelle
            $('#' + modalId + ' .modal-title').text(title);
            $('#' + modalId + ' .modal-body').html(content);
            $('#' + modalId).modal('show');
        });

        $('#btn-export-excel').on('click', function() {
            window.location.href = "{{ route('backend.current_financial_transactions_export_excel') }}";
        });
    </script>
@endsection
