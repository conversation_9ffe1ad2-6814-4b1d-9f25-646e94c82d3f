<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InstitutionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                [
                    'name' => '<PERSON>ahıs',
                    'is_active' => true,
                ],
                [
                    'name' => 'Kuruluş',
                    'is_active' => true,
                ],
            ];

            DB::table('institution_types')->upsert(
                $types,
                ['name'],
                ['is_active']
            );
        });

        $this->command->info('InstitutionType verileri başarıyla oluşturuldu.');
    }
}
