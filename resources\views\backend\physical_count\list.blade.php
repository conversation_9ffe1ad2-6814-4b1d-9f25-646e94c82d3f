@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . $container->page . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Sayım Kodu</th>
                            <th scope="col">Depo</th>
                            <th scope="col">Lokasyon</th>
                            <th scope="col"><PERSON><PERSON><PERSON></th>
                            <th scope="col" class="text-center"><PERSON><PERSON></th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'count_code',
                        name: 'count_code',
                        className: 'text-center',
                    },
                    {
                        data: 'warehouse.name',
                        name: 'warehouse.name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return row.location ? row.location.name : 'Tüm Depo';
                        },
                        className: 'text-center',
                    },
                    {
                        data: 'count_date',
                        name: 'count_date',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return row.items ? row.items.length : 0;
                        },
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            if (row.status_id == 1) {
                                return '<span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm"> Taslak </span>';
                            } else if (row.status_id == 2) {
                                return '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Onaylandı </span>';
                            } else {
                                return '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> İptal </span>';
                            }
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            var buttons = `
                            <td class="text-center">
                                <div class="d-flex align-items-center gap-10 justify-content-center">
                                    <a href="{{ route('backend.' . $container->page . '_detail') }}/${row.id}" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                        <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                    </a>`;

                            if (row.status_id == 1) {
                                buttons += `
                                    <button type="button" class="btn-status bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-id="${row.id}" data-status="2" title="Onayla">
                                        <iconify-icon icon="lucide:check" class="menu-icon"></iconify-icon>
                                    </button>`;
                            }

                            if (row.status_id == 2) {
                                buttons += `
                                    <button type="button" class="btn-update-stock bg-primary-focus text-primary-600 bg-hover-primary-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-id="${row.id}" title="Stokları Güncelle">
                                        <iconify-icon icon="lucide:refresh-cw" class="menu-icon"></iconify-icon>
                                    </button>`;
                            }

                            buttons += `
                                    <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-primary-focus text-primary-600 bg-hover-primary-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                    </a>
                                    <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                        <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                    </button>
                                </div>
                            </td>`;

                            return buttons;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            });

            $('[filter-name]').change(function() {
                $("[datatable]").DataTable().ajax.reload();
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

            $(document).on('click', '.btn-status', function() {
                var id = $(this).data('id');
                var status = $(this).data('status');
                var statusText = status == 2 ? 'onaylamak' : 'iptal etmek';

                Swal.fire({
                    title: 'Emin misiniz?',
                    text: `Bu fiziksel sayımı ${statusText} istediğinize emin misiniz?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Evet',
                    cancelButtonText: 'Hayır'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('backend.physical_count_status') }}",
                            type: 'POST',
                            data: {
                                id: id,
                                status_id: status,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Başarılı!', response.message, 'success');
                                    table.ajax.reload(null, false);
                                } else {
                                    Swal.fire('Hata!', response.message, 'error');
                                }
                            },
                            error: function(xhr, status, error) {
                                var errorMessage = 'Bir hata oluştu';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMessage = xhr.responseJSON.message;
                                }
                                Swal.fire('Hata!', errorMessage, 'error');
                            }
                        });
                    }
                });
            });

            $(document).on('click', '.btn-update-stock', function() {
                var id = $(this).data('id');

                Swal.fire({
                    title: 'Stokları Güncelle',
                    text: 'Fiziksel sayım sonuçlarına göre stokları güncellemek istediğinize emin misiniz?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Evet, Güncelle',
                    cancelButtonText: 'Hayır'
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: 'İşleniyor...',
                            text: 'Stoklar güncelleniyor, lütfen bekleyin.',
                            icon: 'info',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        $.ajax({
                            url: "{{ route('backend.physical_count_refresh_stock', '') }}/" + id,
                            type: 'POST',
                            data: {
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Başarılı!', response.message, 'success');
                                    table.ajax.reload(null, false);
                                } else {
                                    Swal.fire('Hata!', response.message, 'error');
                                }
                            },
                            error: function(xhr, status, error) {
                                var errorMessage = 'Bir hata oluştu';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMessage = xhr.responseJSON.message;
                                }
                                Swal.fire('Hata!', errorMessage, 'error');
                            }
                        });
                    }
                });
            });
        });
    </script>
@endsection
