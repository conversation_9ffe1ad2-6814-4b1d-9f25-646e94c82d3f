<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->comment('Birim adı');
            $table->string('code', 20)->comment('Birim kodu');
            $table->string('symbol', 10)->comment('Birim sembolü');
            $table->longText('description')->nullable()->comment('Açıklama');
            $table->integer('unit_type_id')->comment('Birim tipi');
            $table->tinyInteger('allow_decimal')->default(1)->comment('Ondalık değer kabul eder mi?');
            $table->integer('decimal_places')->default(4)->comment('Ondalık basamak sayısı');
            $table->tinyInteger('is_active')->default(1)->comment('Aktif mi?');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
