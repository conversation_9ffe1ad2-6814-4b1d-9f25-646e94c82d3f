<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;

class InvoiceItem extends BaseModel
{
    use SoftDeletes;

    protected $table = 'invoice_items';

    protected $guarded = [];
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
    public function stock()
    {
        return $this->belongsTo(Stock::class, 'stock_id');
    }
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
    public function productVariant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id');
    }
    public static function generateItemNo($invoiceNo)
    {
        $prefix = $invoiceNo . '-';
        $lastItemNo = self::withTrashed()->where('item_no', 'like', $prefix . '%')->latest('item_no')->first();
        if ($lastItemNo) {
            $lastSequence = (int)substr($lastItemNo->item_no, strrpos($lastItemNo->item_no, '-') + 1);
            $nextSequence = str_pad($lastSequence + 1, 2, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '01';
        }

        return $prefix . $nextSequence;
    }
}
