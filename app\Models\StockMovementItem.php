<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockMovementItem extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_movement_items';

    protected $guarded = [];

    public function stockMovement()
    {
        return $this->belongsTo(StockMovement::class, 'stock_movement_id');
    }

    public function stock()
    {
        return $this->belongsTo(Stock::class, 'stock_id');
    }

    public function stockBatch()
    {
        return $this->belongsTo(StockBatch::class, 'stock_batch_id');
    }



    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class, 'currency_code');
    }
}
