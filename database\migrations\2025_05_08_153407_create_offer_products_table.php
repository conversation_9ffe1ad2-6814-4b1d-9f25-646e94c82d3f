<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_products', function (Blueprint $table) {
            $table->id();
            $table->integer('offer_id');
            $table->string('item_no', 255)->unique();
            $table->integer('stock_id');
            $table->integer('exchange_rate_id');
            $table->integer('product_id');
            $table->integer('product_variant_id')->nullable();
            $table->integer('quantity');
            $table->decimal('unit_price', 20, 8);
            $table->tinyInteger('status')->default(0)->comment('0: Bekliyor, 1: Onaylandı, 2: İptal Edildi');
            $table->tinyInteger('vat_status')->default(0)->comment('0: Hariç, 1: Dahil');
            $table->string('currency_type', 50);
            $table->decimal('exchange_rate', 10, 4);
            $table->decimal('total_price', 20, 8);
            $table->decimal('vat_rate', 20, 2);
            $table->text('reason_for_cancellation')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_products');
    }
};
