<?php

namespace App\Http\Controllers\Backend;

use App\Models\Brand;
use App\Models\Product;
use App\Models\Unit;
use Illuminate\Http\Request;
use App\Models\Role;

class BrandController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Marka';
        $this->page = 'brand';
        $this->model = new Brand();

        $this->listQuery = Brand::withCount('products')->select('brands.*');

        $this->view = (object) array(
            'breadcrumb' => array(
                'Marka Listesi' => route('backend.brand_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:50',
                'description' => 'nullable|string|min:3|max:255',
                'email' => ['required', 'email', 'min:10', 'max:191', 'regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix'],
                'phone' => 'nullable|max:15|min:15',
                'address' => 'nullable|string|max:500',
                'contact_name' => 'required|string|max:255',
                'is_active' => 'required|boolean',
            ],
            [
                'name.required' => 'Marka adı zorunludur.',
                'name.max' => 'Marka adı en fazla 255 karakter olabilir.',
                'code.max' => 'Marka kodu en fazla 50 karakter olabilir.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
                'description.min' => 'Açıklama en az 3 karakter olmalıdır.',
                'description.max' => 'Açıklama en fazla 255 karakter olabilir.',
                'email.required' => 'Email adresi girilmesi gerekiyor',
                'email.email' => 'Email formatında girmeniz gerekiyor.',
                'email.min' => 'Email 10 karakterden fazla olmalıdır.',
                'email.regex' => 'Email formatında girmeniz gerekiyor.',
                'phone.max' => 'Telefon en fazla 15 karakter olabilir.',
                'phone.min' => 'Telefon en az 15 karakter olmalıdır.',
                'address.max' => 'Adres en fazla 500 karakter olabilir.',
                'contact_name.required' => 'İletişim kişisi girilmesi gerekiyor',
                'contact_name.max' => 'İletişim kişisi en fazla 255 karakter olabilir.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
            ]
        );
        view()->share('products', Product::active()->get());
        view()->share('units', Unit::active()->get());
        parent::__construct();
    }


    protected function datatableHook($obj)
    {
        return $obj
            ->editColumn('products_count', function ($item) {
                return $item->products_count ?? 0;
            });
    }

    public function detail(Request $request, $unique = NULL)
    {
        $item = $this->model::find($unique);

        if (is_null($item)) {
            return redirect()->back()->with('error', 'Kayıt bulunamadı');
        }

        if ($request->has('datatable')) {
            $select = Product::where('brand_id', $unique)
                ->with(['category', 'unit'])
                ->get();

            $obj = datatables()->of($select)
                ->editColumn('sku', function ($item) {
                    return $item->sku ?? '-';
                })
                ->editColumn('sale_price', function ($item) {
                    return number_format($item->sale_price, 2, ',', '.') . ' ' . ($item->sale_currency_code ?? 'TL');
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active == 1 ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);

            return $obj;
        }

        return view("backend.$this->page.detail", compact('item', 'unique'));
    }

    public function save(Request $request, $unique = null)
    {
        $request->validate($this->validation[0], $this->validation[1]);

        $params = $request->all();

        if (!is_null($unique)) {
            $brand = Brand::find($unique);
            if (!$brand) {
                return redirect()->back();
            }
            $brand->update($params);
        } else {
            Brand::create($params);
        }

        return redirect()->route("backend." . $this->page . "_list")->with('success', 'Kayıt başarılı şekilde eklendi');
    }
}
