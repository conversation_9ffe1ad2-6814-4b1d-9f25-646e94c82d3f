<aside class="sidebar">
    <button type="button" class="sidebar-close-btn">
        <iconify-icon icon="radix-icons:cross-2"></iconify-icon>
    </button>
    <div>
        <a href="<?php echo e(route('backend.index')); ?>" class="sidebar-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_kodlio.png')); ?>" alt="site logo" class="light-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_white.png')); ?>" alt="site logo" class="dark-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_kodlio.png')); ?>" alt="site logo" class="logo-icon">
        </a>
    </div>
    <div class="sidebar-menu-area">
        <ul class="sidebar-menu" id="sidebar-menu">
            <li>
                <a href="<?php echo e(route('backend.index')); ?>">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="menu-icon"></iconify-icon>
                    <span>Anasayfa</span>
                </a>
            </li>
            <!-- Cariler Başlığı -->
            <?php if(hasPermission([
                    'current',
                    'balance',
                    'account_vouchers',
                    'current_financial_transactions',
                    'current_stock_movements',
                ])): ?>
                <li
                    class="dropdown <?php echo e(activeMenu(['current', 'balance', 'account_vouchers', 'current_financial_transactions', 'current_stock_movements'])); ?>">
                    <a class="nav-link d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <iconify-icon icon="mdi:account-group" class="menu-icon"></iconify-icon>
                            <span>Cariler</span>
                        </div>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['current'])): ?>
                            <li class="<?php echo e(activeMenuItem(['current'])); ?>">
                                <a href="<?php echo e(route('backend.current_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['current'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Cari
                                        Hesaplar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['balance'])): ?>
                            <li class="<?php echo e(activeMenuItem(['balance'])); ?>">
                                <a href="<?php echo e(route('backend.balance_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['balance'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Cari
                                        Bakiye Tanımlama</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['current'])): ?>
                            <li class="<?php echo e(activeMenuItem(['account_vouchers'])); ?>">
                                <a href="<?php echo e(route('backend.account_vouchers_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['account_vouchers'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Cari Hesap
                                        Fişleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['current'])): ?>
                            <li class="<?php echo e(activeMenuItem(['current_financial_transactions'])); ?>">
                                <a href="<?php echo e(route('backend.current_financial_transactions_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['current_financial_transactions'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Cari Finansal
                                        Hareketler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['current'])): ?>
                            <li class="<?php echo e(activeMenuItem(['current_stock_movements'])); ?>">
                                <a href="<?php echo e(route('backend.current_stock_movements_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['current_stock_movements'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Cari Stok
                                        Hareketleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Finans Başlığı -->
            <?php if(hasPermission(['expense_voucher', 'payment_transaction', 'receipt_transaction', 'invoice', 'return_purchase_invoice', 'return_sale_invoice'])): ?>
                <li
                    class="dropdown <?php echo e(activeMenu(['invoices', 'purchase_invoices', 'return_purchase_invoice', 'return_sale_invoice', 'receipt_transaction', 'payment_transaction', 'sale_waybill', 'purchase_waybills', 'expense_voucher'])); ?>">
                    <a class="nav-link d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <iconify-icon icon="mdi:finance" class="menu-icon"></iconify-icon>
                            <span>Fatura</span>
                        </div>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['invoices'])); ?>">
                                <a href="<?php echo e(route('backend.invoices_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['invoices'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Satış
                                        Faturaları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['purchase_invoices'])); ?>">
                                <a href="<?php echo e(route('backend.purchase_invoices_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['purchase_invoices'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        Faturaları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['return_purchase_invoice'])); ?>">
                                <a href="<?php echo e(route('backend.return_purchase_invoice_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['return_purchase_invoice'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        İade Faturaları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['return_sale_invoice'])); ?>">
                                <a href="<?php echo e(route('backend.return_sale_invoice_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['return_sale_invoice'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Satış
                                        İade Faturaları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['receipt_transaction'])): ?>
                            <li class="<?php echo e(activeMenuItem(['receipt_transaction'])); ?>">
                                <a href="<?php echo e(route('backend.receipt_transaction_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['receipt_transaction'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Tahsilat
                                        İşlemleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['payment_transaction'])): ?>
                            <li class="<?php echo e(activeMenuItem(['payment_transaction'])); ?>">
                                <a href="<?php echo e(route('backend.payment_transaction_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['payment_transaction'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Ödeme
                                        İşlemleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['sale_waybill'])); ?>">
                                <a href="<?php echo e(route('backend.sale_waybill_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['sale_waybill'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Satış
                                        İrsaliyeleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['invoice'])): ?>
                            <li class="<?php echo e(activeMenuItem(['purchase_waybills'])); ?>">
                                <a href="<?php echo e(route('backend.purchase_waybills_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['purchase_waybills'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        İrsaliyeleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['expense_voucher'])): ?>
                            <li class="<?php echo e(activeMenuItem(['expense_voucher'])); ?>">
                                <a href="<?php echo e(route('backend.expense_voucher_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['expense_voucher'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Masraf
                                        Fişleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Sipariş Yönetimi -->
            <?php if(hasPermission(['order_received', 'order_given'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['order_received', 'order_given'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="material-symbols:order-approve-outline" class="menu-icon"></iconify-icon>
                        <span>Sipariş</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['order_received'])): ?>
                            <li class="<?php echo e(activeMenuItem(['order_received'])); ?>">
                                <a href="<?php echo e(route('backend.order_received_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['order_received'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Alınan
                                        Sipariş</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['order_given'])): ?>
                            <li class="<?php echo e(activeMenuItem(['order_given'])); ?>">
                                <a href="<?php echo e(route('backend.order_given_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['order_given'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Verilen
                                        Sipariş</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Teklif Yönetimi -->
            <?php if(hasPermission(['offer_received', 'offer_given'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['offer_received', 'offer_given'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:file-document-outline" class="menu-icon"></iconify-icon>
                        <span>Teklif</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['offer_received'])): ?>
                            <li class="<?php echo e(activeMenuItem(['offer_received'])); ?>">
                                <a href="<?php echo e(route('backend.offer_received_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['offer_received'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Alınan
                                        Teklif</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['offer_given'])): ?>
                            <li class="<?php echo e(activeMenuItem(['offer_given'])); ?>">
                                <a href="<?php echo e(route('backend.offer_given_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['offer_given'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Verilen
                                        Teklif</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Marka ve Kategori Yönetimi -->
            <?php if(hasPermission(['brand', 'category'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['brand', 'category'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:tag-multiple" class="menu-icon"></iconify-icon>
                        <span>Marka & Kategori</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['brand'])): ?>
                            <li class="<?php echo e(activeMenuItem(['brand'])); ?>">
                                <a href="<?php echo e(route('backend.brand_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['brand'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Markalar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['category'])): ?>
                            <li class="<?php echo e(activeMenuItem(['category'])); ?>">
                                <a href="<?php echo e(route('backend.category_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['category'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Kategoriler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Ürün Yönetimi -->
            <?php if(hasPermission(['product', 'product_variant'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['product', 'product_variant'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:package" class="menu-icon"></iconify-icon>
                        <span>Ürün Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['product'])): ?>
                            <li class="<?php echo e(activeMenuItem(['product'])); ?>">
                                <a href="<?php echo e(route('backend.product_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['product'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Ürünler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['product_variant'])): ?>
                            <li class="<?php echo e(activeMenuItem(['product_variant'])); ?>">
                                <a href="<?php echo e(route('backend.product_variant_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['product_variant'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Ürün
                                        Varyantları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>


            <!-- Depo Yönetimi -->
            <?php if(hasPermission(['warehouse', 'warehouse_location'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['warehouse', 'warehouse_location'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:warehouse" class="menu-icon"></iconify-icon>
                        <span>Depo Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['warehouse'])): ?>
                            <li class="<?php echo e(activeMenuItem(['warehouse'])); ?>">
                                <a href="<?php echo e(route('backend.warehouse_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['warehouse'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Depolar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['warehouse_location'])): ?>
                            <li class="<?php echo e(activeMenuItem(['warehouse_location'])); ?>">
                                <a href="<?php echo e(route('backend.warehouse_location_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['warehouse_location'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Depo
                                        Lokasyonları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Stok Yönetimi -->
            <?php if(hasPermission(['stock', 'stock_movement', 'stock_reservation', 'physical_count', 'stock_period'])): ?>
                <li
                    class="dropdown <?php echo e(activeMenu(['stock', 'stock_movement', 'stock_reservation', 'physical_count', 'stock_period'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:archive" class="menu-icon"></iconify-icon>
                        <span>Stok Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['stock'])): ?>
                            <li class="<?php echo e(activeMenuItem(['stock'])); ?>">
                                <a href="<?php echo e(route('backend.stock_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['stock'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Stoklar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['stock_movement'])): ?>
                            <li class="<?php echo e(activeMenuItem(['stock_movement'])); ?>">
                                <a href="<?php echo e(route('backend.stock_movement_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['stock_movement'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Stok
                                        Hareketleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['stock_reservation'])): ?>
                            <li class="<?php echo e(activeMenuItem(['stock_reservation'])); ?>">
                                <a href="<?php echo e(route('backend.stock_reservation_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['stock_reservation'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Stok
                                        Rezervasyonları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['physical_count'])): ?>
                            <li class="<?php echo e(activeMenuItem(['physical_count'])); ?>">
                                <a href="<?php echo e(route('backend.physical_count_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['physical_count'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Fiziksel
                                        Sayım</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['stock_period'])): ?>
                            <li class="<?php echo e(activeMenuItem(['stock_period'])); ?>">
                                <a href="<?php echo e(route('backend.stock_period_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['stock_period'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Stok
                                        Dönemleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Sistem Yönetimi -->
            <?php if(hasPermission(['user', 'role'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['user', 'role'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:cog" class="menu-icon"></iconify-icon>
                        <span>Sistem Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['user'])): ?>
                            <li class="<?php echo e(activeMenuItem(['user'])); ?>">
                                <a href="<?php echo e(route('backend.user_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['user'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Kullanıcılar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['role'])): ?>
                            <li class="<?php echo e(activeMenuItem(['role'])); ?>">
                                <a href="<?php echo e(route('backend.role_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['role'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i>
                                    <span>Roller</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="<?php echo e(activeMenuItem(['setting'])); ?>">
                            <a href="<?php echo e(route('backend.setting_form')); ?>"
                                class="d-flex align-items-center <?php echo e(activeMenuItem(['setting'])); ?>">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i>
                                <span>Ayarlar</span>
                            </a>
                        </li>
                    </ul>
                </li>
            <?php endif; ?>
            <!-- Tanımlamalar -->
            <?php if(hasPermission(['unit', 'unit_type', 'currency_type', 'vat_rate', 'exchange_rate', 'payment_term'])): ?>
                <li class="dropdown <?php echo e(activeMenu(['unit', 'unit_type', 'currency_type', 'vat_rate', 'exchange_rate', 'payment_term'])); ?>">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:ruler" class="menu-icon"></iconify-icon>
                        <span>Tanımlamalar</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(hasPermission(['unit'])): ?>
                            <li class="<?php echo e(activeMenuItem(['unit'])); ?>">
                                <a href="<?php echo e(route('backend.unit_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['unit'])); ?>">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Birimler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['unit_type'])): ?>
                            <li class="<?php echo e(activeMenuItem(['unit_type'])); ?>">
                                <a href="<?php echo e(route('backend.unit_type_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['unit_type'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Birim
                                        Türleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['currency_type'])): ?>
                            <li class="<?php echo e(activeMenuItem(['currency_type'])); ?>">
                                <a href="<?php echo e(route('backend.currency_type_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['currency_type'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Para
                                        Birimleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['exchange_rate'])): ?>
                            <li class="<?php echo e(activeMenuItem(['exchange_rate'])); ?>">
                                <a href="<?php echo e(route('backend.exchange_rate_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['exchange_rate'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Döviz
                                        Kurları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['vat_rate'])): ?>
                            <li class="<?php echo e(activeMenuItem(['vat_rate'])); ?>">
                                <a href="<?php echo e(route('backend.vat_rate_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['vat_rate'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>KDV
                                        Oranları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(hasPermission(['payment_term'])): ?>
                            <li class="<?php echo e(activeMenuItem(['payment_term'])); ?>">
                                <a href="<?php echo e(route('backend.payment_term_list')); ?>"
                                    class="d-flex align-items-center <?php echo e(activeMenuItem(['payment_term'])); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Vade
                                        Tanımlamaları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</aside>
<?php /**PATH C:\laragon\www\erp_web\resources\views/components/sidebar.blade.php ENDPATH**/ ?>