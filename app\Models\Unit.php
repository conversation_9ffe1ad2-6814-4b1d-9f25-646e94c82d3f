<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Unit extends BaseModel
{
    use SoftDeletes;

    protected $table = 'units';

    protected $guarded = [];


    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'unit_id');
    }

    /**
     * Miktarı bu birime göre formatla
     */
    public function formatQuantity($quantity)
    {
        return formatQuantity($quantity, $this);
    }

    /**
     * Miktarı birim sembolüyle birlikte formatla
     */
    public function formatQuantityWithUnit($quantity)
    {
        return formatQuantityWithUnit($quantity, $this);
    }

}
