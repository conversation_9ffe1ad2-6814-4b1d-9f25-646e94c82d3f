<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->integer('transaction_type_id');
            $table->integer('invoice_id');
            $table->integer('invoice_type')->comment('1: Satış Faturası');
            $table->integer('current_id');
            $table->timestamp('transaction_date');
            $table->integer('payment_method');
            $table->decimal('amount', 20, 2);
            $table->integer('exchange_rate_id');
            $table->text('description')->nullable();
            $table->string('document_no', 50)->nullable()->comment('Belge numarası');
            $table->string('bank_account', 100)->nullable()->comment('<PERSON>a hesabı');
            $table->date('due_date')->nullable()->comment('Çek/Senet vadesi');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
