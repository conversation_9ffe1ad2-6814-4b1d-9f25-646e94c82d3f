<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waybills', function (Blueprint $table) {
            $table->id();
            $table->string('waybill_no', 50);
            $table->date('waybill_date');
            $table->integer('invoice_id')->nullable();
            $table->text('note')->nullable();
            $table->decimal('net_amount', 20, 2)->default(0)->comment('Vergisiz ara toplam');
            $table->decimal('tax_amount', 20, 2)->default(0)->comment('Toplam vergi tutarı');
            $table->decimal('total_amount', 20, 2)->default(0)->comment('Genel toplam (net + vergi)');
            $table->integer('waybill_type'); // 1: Satış İrsaliyesi
            $table->integer('waybill_statu_id')->default(1); // pending, approved, cancelled
            $table->text('reason_for_cancellation')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waybills');
    }
};
