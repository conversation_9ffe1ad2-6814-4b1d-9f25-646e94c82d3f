@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-12">
                                <label class="form-label"><PERSON>gori Adı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:category"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen kategori adını giriniz"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field='name' />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Üst Kategori</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:parent-child"></iconify-icon>
                                    </span>
                                    <select name="parent_id" class="form-control select2">
                                        <option value="">-- Ana Kategori --</option>
                                        @if (isset($categoryOptions))
                                            @foreach ($categoryOptions as $option)
                                                <option value="{{ $option['id'] }}"
                                                    {{ old('parent_id', $item->parent_id ?? '') == $option['id'] ? 'selected' : '' }}>
                                                    {{ $option['name'] }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                                <x-form-error field='parent_id' />
                            </div>
                            <div class="col-12">
                                <label class="form-label">KDV Oranı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select name="vat_rate_id" class="form-control select2">
                                        <option value="">-- Seçiniz --</option>
                                        @foreach ($vatRates as $vatRate)
                                            <option value="{{ $vatRate->id }}"
                                                {{ old('vat_rate_id', $item->vat_rate_id ?? '') == $vatRate->id ? 'selected' : '' }}>
                                                {{ $vatRate->rate }}%
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="vat_rate_id" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <textarea class="form-control" placeholder="Lütfen kategori açıklamasını giriniz" name="description">{{ old('description') ?? ($item->description ?? '') }}</textarea>
                                    <x-form-error field="description" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">Kaydet</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
