<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TelescopeAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Kullanıcı giriş yapmamışsa 401 döndür
        if (!Auth::guard('user')->check()) {
            abort(401, 'Unauthorized');
        }

        // İzinli email'ler listesi
        $allowedEmails = [
            '<EMAIL>',
        ];

        // Kullanıcının email'i kontrol et
        if (!in_array(Auth::guard('user')->user()->email, $allowedEmails)) {
            abort(403, 'Forbidden');
        }

        return $next($request);
    }
}
