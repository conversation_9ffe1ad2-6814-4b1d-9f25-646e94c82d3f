<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_variants';

    protected $guarded = [];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'variant_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'variant_id');
    }

    public function costHistory()
    {
        return $this->hasMany(ProductCostHistory::class, 'variant_id');
    }

    public function getTotalStockQuantityAttribute()
    {
        return $this->stocks()->sum('quantity');
    }

    public function getTotalReservedQuantityAttribute()
    {
        return $this->stocks()
            ->join('stock_reservations', 'stocks.id', '=', 'stock_reservations.stock_id')
            ->where('stock_reservations.is_active', 1)
            ->sum('stock_reservations.quantity') ?? 0;
    }

    public function getAvailableStockQuantityAttribute()
    {
        return $this->total_stock_quantity - $this->total_reserved_quantity;
    }

    public static function generateSku($productId = null, $variantName = null)
    {
        if (!$productId || !$variantName) {
            return null;
        }

        $product = \App\Models\Product::with(['category', 'brand'])->find($productId);

        if (!$product || !$product->category) {
            return null;
        }

        $categorySlug = strSlugTr($product->category->name);
        $productSlug = strSlugTr($product->name);
        $variantSlug = strSlugTr($variantName);

        $categoryCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $categorySlug), 0, 3));

        $brandCode = '';
        if ($product->brand) {
            $brandSlug = strSlugTr($product->brand->name);
            $brandCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $brandSlug), 0, 3));
        } else {
            $brandCode = 'NOB'; // No Brand
        }

        $productCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $productSlug), 0, 3));
        $variantCode = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $variantSlug), 0, 3));

        $prefix = $categoryCode . $brandCode . $productCode . $variantCode;

        $lastVariant = self::withTrashed()->where('sku', 'like', "$prefix%")->latest('sku')->first();

        if ($lastVariant) {
            $lastSequence = (int) substr($lastVariant->sku, -4);
            $nextSequence = str_pad($lastSequence + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '0001';
        }

        return $prefix . $nextSequence;
    }
}
