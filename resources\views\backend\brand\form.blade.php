@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-md-6">
                                <label class="form-label">Marka Adı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:product"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" name="name"
                                        placeholder="Lütfen marka adını giriniz"
                                        value="{{ old('name', $item->name ?? '') }}">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Marka Kodu</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:code"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" name="code"
                                        placeholder="Marka kodunu giriniz" value="{{ old('code', $item->code ?? '') }}">
                                    <x-form-error field="code" />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:text-annotation-toggle"></iconify-icon>
                                    </span>
                                    <textarea class="form-control" name="description" rows="3" placeholder="Marka açıklamasını giriniz">{{ old('description', $item->description ?? '') }}</textarea>
                                    <x-form-error field="description" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">E-posta</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:email"></iconify-icon>
                                    </span>
                                    <input type="email" class="form-control" name="email"
                                        placeholder="E-posta adresini giriniz"
                                        value="{{ old('email', $item->email ?? '') }}">
                                    <x-form-error field="email" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Telefon</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:phone-calling-linear"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                        value="{{ old('phone') ?? ($item->phone ?? '') }}">
                                    <x-form-error field="phone" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">İletişim Kişisi</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:user"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" name="contact_name"
                                        placeholder="İletişim kişisinin adını giriniz"
                                        value="{{ old('contact_name', $item->contact_name ?? '') }}">
                                    <x-form-error field="contact_name" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>
                                            Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>
                                            Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Adres</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:location"></iconify-icon>
                                    </span>
                                    <textarea class="form-control" name="address" rows="3" placeholder="Adres bilgisini giriniz">{{ old('address', $item->address ?? '') }}</textarea>
                                    <x-form-error field="address" />
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">
                                    <i class="fas fa-save me-2"></i>Kaydet
                                </button>
                                <a href="{{ route('backend.' . $container->page . '_list') }}"
                                    class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
