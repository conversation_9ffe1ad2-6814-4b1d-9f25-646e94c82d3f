<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Backend\AuthenticationController;
use App\Http\Controllers\Backend\UserController;
use App\Http\Controllers\Backend\DashboardController;
use App\Http\Controllers\Backend\RoleController;
use App\Http\Controllers\Backend\BrandController;
use App\Http\Controllers\Backend\CategoryController;
use App\Http\Controllers\Backend\CurrencyTypeController;
use App\Http\Controllers\Backend\ExchangeRateController;
use App\Http\Controllers\Backend\PhysicalCountController;
use App\Http\Controllers\Backend\ProductController;
use App\Http\Controllers\Backend\ProductVariantController;
use App\Http\Controllers\Backend\StockController;
use App\Http\Controllers\Backend\StockMovementController;
use App\Http\Controllers\Backend\StockReservationController;
use App\Http\Controllers\Backend\UnitController;
use App\Http\Controllers\Backend\UnitTypeController;
use App\Http\Controllers\Backend\WarehouseController;
use App\Http\Controllers\Backend\WarehouseLocationController;
use App\Http\Controllers\Backend\BalanceController;
use App\Http\Controllers\Backend\CurrentController;
use App\Http\Controllers\Backend\InvoicesController;
use App\Http\Controllers\Backend\PurchaseInvoiceController;
use App\Http\Controllers\Backend\AccountVouchersController;
use App\Http\Controllers\Backend\CurrentFinancialTransactionController;
use App\Http\Controllers\Backend\CurrentStockMovementController;
use App\Http\Controllers\Backend\PaymentTransactionController;
use App\Http\Controllers\Backend\ReceiptTransactionController;
use App\Http\Controllers\Backend\ExpenseVouchersController;
use App\Http\Controllers\Backend\PurchaseWaybillController;
use App\Http\Controllers\Backend\GeneralController;
use App\Http\Controllers\Backend\GivenOfferController;
use App\Http\Controllers\Backend\GivenOrderController;
use App\Http\Controllers\Backend\PaymentTermController;
use App\Http\Controllers\Backend\ReceivedOfferController;
use App\Http\Controllers\Backend\ReceivedOrderController;
use App\Http\Controllers\Backend\ReturnPurchaseInvoiceController;
use App\Http\Controllers\Backend\ReturnSaleInvoiceController;
use App\Http\Controllers\Backend\SaleWaybillController;
use App\Http\Controllers\Backend\VatRateController;
use App\Http\Controllers\Backend\SettingController;

Route::group(['prefix' => '', 'middleware' => ['auth:user']], function () {
    Route::controller(DashboardController::class)->group(function () {
        Route::get('/', 'index')->name('backend.index');
    });
    Route::prefix('profile')->group(function () {
        Route::controller(UserController::class)->group(function () {
            Route::get('/', 'profile')->name('backend.profile')->desc('Profil');
            Route::post('/', 'profile_save')->name('backend.profile_save')->desc('Profil');
            Route::post('password', 'password')->name('backend.profile_password')->desc('Profil');
        });
    });
    Route::group(['middleware' => ['permissions']], function () {
        Route::prefix('user')->group(function () {
            Route::controller(UserController::class)->group(function () {
                Route::any('/', 'list')->name('backend.user_list')->desc('Kullanıcılar');
                Route::get('form/{unique?}', 'form')->name('backend.user_form')->desc('Kullanıcılar');
                Route::post('form/{unique?}', 'save')->name('backend.user_save')->desc('Kullanıcılar');
                Route::delete('delete', 'delete')->name('backend.user_delete')->desc('Kullanıcılar');
            });
        });
        Route::prefix('role')->group(function () {
            Route::controller(RoleController::class)->group(function () {
                Route::any('/', 'list')->name('backend.role_list')->desc('Roller');
                Route::get('form/{unique?}', 'form')->name('backend.role_form')->desc('Roller');
                Route::post('form/{unique?}', 'save')->name('backend.role_save')->desc('Roller');
                Route::delete('delete', 'delete')->name('backend.role_delete')->desc('Roller');
            });
        });

        Route::group(['prefix' => 'exchange'], function () {
            Route::group(['prefix' => 'rate'], function () {
                Route::any('/', [ExchangeRateController::class, 'list'])->name('backend.exchange_rate_list')->desc('Döviz Kurları');
                Route::get('refresh', [ExchangeRateController::class, 'refresh'])->name('backend.exchange_rate_refresh')->desc('Döviz Kurları');
                Route::post('external', [ExchangeRateController::class, 'getExternalRates'])->name('backend.exchange_rate_external')->desc('Döviz Kurları');
            });
        });

        Route::group(['prefix' => 'currency'], function () {
            Route::group(['prefix' => 'type'], function () {
                Route::any('/', [CurrencyTypeController::class, 'list'])->name('backend.currency_type_list')->desc('Para Birimleri');
                Route::get('form/{unique?}', [CurrencyTypeController::class, 'form'])->name('backend.currency_type_form')->desc('Para Birimleri');
                Route::post('form/{unique?}', [CurrencyTypeController::class, 'save'])->name('backend.currency_type_save')->desc('Para Birimleri');
                Route::delete('delete', [CurrencyTypeController::class, 'delete'])->name('backend.currency_type_delete')->desc('Para Birimleri');
            });
        });

        Route::group(['prefix' => 'product'], function () {
            Route::any('/', [ProductController::class, 'list'])->name('backend.product_list')->desc('Ürünler');
            Route::get('form/{unique?}', [ProductController::class, 'form'])->name('backend.product_form')->desc('Ürünler');
            Route::post('form/{unique?}', [ProductController::class, 'save'])->name('backend.product_save')->desc('Ürünler');
            Route::delete('delete', [ProductController::class, 'delete'])->name('backend.product_delete')->desc('Ürünler');
            Route::any('detail/{unique?}', [ProductController::class, 'detail'])->name('backend.product_detail')->desc('Ürünler');
            Route::group(['prefix' => 'variant'], function () {
                Route::any('/', [ProductVariantController::class, 'list'])->name('backend.product_variant_list')->desc('Ürün Varyantları');
                Route::get('form/{unique?}', [ProductVariantController::class, 'form'])->name('backend.product_variant_form')->desc('Ürün Varyantları');
                Route::post('form/{unique?}', [ProductVariantController::class, 'save'])->name('backend.product_variant_save')->desc('Ürün Varyantları');
                Route::delete('delete', [ProductVariantController::class, 'delete'])->name('backend.product_variant_delete')->desc('Ürün Varyantları');
            });
        });

        Route::group(['prefix' => 'category'], function () {
            Route::any('/', [CategoryController::class, 'list'])->name('backend.category_list')->desc('Kategoriler');
            Route::get('form/{unique?}', [CategoryController::class, 'form'])->name('backend.category_form')->desc('Kategoriler');
            Route::post('form/{unique?}', [CategoryController::class, 'save'])->name('backend.category_save')->desc('Kategoriler');
            Route::delete('delete', [CategoryController::class, 'delete'])->name('backend.category_delete')->desc('Kategoriler');
            Route::any('detail/{unique?}', [CategoryController::class, 'detail'])->name('backend.category_detail')->desc('Kategoriler');
        });

        Route::group(['prefix' => 'brand'], function () {
            Route::any('/', [BrandController::class, 'list'])->name('backend.brand_list')->desc('Markalar');
            Route::get('form/{unique?}', [BrandController::class, 'form'])->name('backend.brand_form')->desc('Markalar');
            Route::post('form/{unique?}', [BrandController::class, 'save'])->name('backend.brand_save')->desc('Markalar');
            Route::delete('delete', [BrandController::class, 'delete'])->name('backend.brand_delete')->desc('Markalar');
            Route::any('detail/{unique?}', [BrandController::class, 'detail'])->name('backend.brand_detail')->desc('Markalar');
        });

        Route::group(['prefix' => 'unit'], function () {
            Route::any('/', [UnitController::class, 'list'])->name('backend.unit_list')->desc('Birimler');
            Route::get('form/{unique?}', [UnitController::class, 'form'])->name('backend.unit_form')->desc('Birimler');
            Route::post('form/{unique?}', [UnitController::class, 'save'])->name('backend.unit_save')->desc('Birimler');
            Route::delete('delete', [UnitController::class, 'delete'])->name('backend.unit_delete')->desc('Birimler');
            Route::group(['prefix' => 'type'], function () {
                Route::any('/', [UnitTypeController::class, 'list'])->name('backend.unit_type_list')->desc('Birim Tipleri');
                Route::get('form/{unique?}', [UnitTypeController::class, 'form'])->name('backend.unit_type_form')->desc('Birim Tipleri');
                Route::post('form/{unique?}', [UnitTypeController::class, 'save'])->name('backend.unit_type_save')->desc('Birim Tipleri');
                Route::delete('delete', [UnitTypeController::class, 'delete'])->name('backend.unit_type_delete')->desc('Birim Tipleri');
            });
        });

        Route::group(['prefix' => 'warehouse'], function () {
            Route::any('/', [WarehouseController::class, 'list'])->name('backend.warehouse_list')->desc('Depolar');
            Route::get('form/{unique?}', [WarehouseController::class, 'form'])->name('backend.warehouse_form')->desc('Depolar');
            Route::post('form/{unique?}', [WarehouseController::class, 'save'])->name('backend.warehouse_save')->desc('Depolar');
            Route::delete('delete', [WarehouseController::class, 'delete'])->name('backend.warehouse_delete')->desc('Depolar');
            Route::any('detail/{unique?}', [WarehouseController::class, 'detail'])->name('backend.warehouse_detail')->desc('Depolar');
            Route::group(['prefix' => 'location'], function () {
                Route::any('/', [WarehouseLocationController::class, 'list'])->name('backend.warehouse_location_list')->desc('Depo Lokasyonları');
                Route::get('form/{unique?}', [WarehouseLocationController::class, 'form'])->name('backend.warehouse_location_form')->desc('Depo Lokasyonları');
                Route::post('form/{unique?}', [WarehouseLocationController::class, 'save'])->name('backend.warehouse_location_save')->desc('Depo Lokasyonları');
                Route::delete('delete', [WarehouseLocationController::class, 'delete'])->name('backend.warehouse_location_delete')->desc('Depo Lokasyonları');
                Route::any('detail/{unique?}', [WarehouseLocationController::class, 'detail'])->name('backend.warehouse_location_detail')->desc('Depo Lokasyonları');
            });
        });

        Route::group(['prefix' => 'stock'], function () {
            Route::any('/', [StockController::class, 'list'])->name('backend.stock_list')->desc('Stok Durumu');
            Route::get('form/{unique?}', [StockController::class, 'form'])->name('backend.stock_form')->desc('Stok Durumu');
            Route::post('form/{unique?}', [StockController::class, 'save'])->name('backend.stock_save')->desc('Stok Durumu');
            Route::delete('delete', [StockController::class, 'delete'])->name('backend.stock_delete')->desc('Stok Durumu');
            Route::group(['prefix' => 'movement'], function () {
                Route::any('/', [StockMovementController::class, 'list'])->name('backend.stock_movement_list')->desc('Stok Hareketleri');
                Route::get('form/{unique?}', [StockMovementController::class, 'form'])->name('backend.stock_movement_form')->desc('Stok Hareketleri');
                Route::post('form/{unique?}', [StockMovementController::class, 'save'])->name('backend.stock_movement_save')->desc('Stok Hareketleri');
                Route::delete('delete', [StockMovementController::class, 'delete'])->name('backend.stock_movement_delete')->desc('Stok Hareketleri');
                Route::post('status', [StockMovementController::class, 'status'])->name('backend.stock_movement_status')->desc('Stok Hareketleri');
                Route::any('detail/{unique?}', [StockMovementController::class, 'detail'])->name('backend.stock_movement_detail')->desc('Stok Hareketleri');
            });
            Route::group(['prefix' => 'reservation'], function () {
                Route::any('/', [StockReservationController::class, 'list'])->name('backend.stock_reservation_list')->desc('Stok Rezervasyonları');
                Route::get('form/{unique?}', [StockReservationController::class, 'form'])->name('backend.stock_reservation_form')->desc('Stok Rezervasyonları');
                Route::post('form/{unique?}', [StockReservationController::class, 'save'])->name('backend.stock_reservation_save')->desc('Stok Rezervasyonları');
                Route::delete('delete', [StockReservationController::class, 'delete'])->name('backend.stock_reservation_delete')->desc('Stok Rezervasyonları');
                Route::post('status', [StockReservationController::class, 'status'])->name('backend.stock_reservation_status')->desc('Stok Rezervasyonları');
            });
        });

        Route::group(['prefix' => 'physical'], function () {
            Route::group(['prefix' => 'count'], function () {
                Route::any('/', [PhysicalCountController::class, 'list'])->name('backend.physical_count_list')->desc('Fiziksel Sayımlar');
                Route::get('form/{unique?}', [PhysicalCountController::class, 'form'])->name('backend.physical_count_form')->desc('Fiziksel Sayımlar');
                Route::post('form/{unique?}', [PhysicalCountController::class, 'save'])->name('backend.physical_count_save')->desc('Fiziksel Sayımlar');
                Route::delete('delete', [PhysicalCountController::class, 'delete'])->name('backend.physical_count_delete')->desc('Fiziksel Sayımlar');
                Route::post('status', [PhysicalCountController::class, 'status'])->name('backend.physical_count_status')->desc('Fiziksel Sayımlar');
                Route::any('detail/{physical_count_id?}/{unique?}', [PhysicalCountController::class, 'detail'])->name('backend.physical_count_detail')->desc('Fiziksel Sayımlar');
                Route::post('refresh-stock/{id}', [PhysicalCountController::class, 'refreshStock'])->name('backend.physical_count_refresh_stock')->desc('Fiziksel Sayımlar');
            });
        });

        Route::prefix('balance')->group(function () {
            Route::controller(BalanceController::class)->group(function () {
                Route::any('/', 'list')->name('backend.balance_list')->desc('Bakiyeler');
                Route::get('form/{unique?}', 'form')->name('backend.balance_form')->desc('Bakiyeler');
                Route::post('form/{unique?}', 'save')->name('backend.balance_save')->desc('Bakiyeler');
                Route::delete('delete', 'delete')->name('backend.balance_delete')->desc('Bakiyeler');
                Route::get('detail/{unique?}', 'detail')->name('backend.balance_detail')->desc('Bakiyeler');
            });
        });

        Route::prefix('current')->group(function () {
            Route::controller(CurrentController::class)->group(function () {
                Route::any('/', 'list')->name('backend.current_list')->desc('Cari Hesaplar');
                Route::get('form/{unique?}', 'form')->name('backend.current_form')->desc('Cari Hesaplar');
                Route::post('form/{unique?}', 'save')->name('backend.current_save')->desc('Cari Hesaplar');
                Route::delete('delete', 'delete')->name('backend.current_delete')->desc('Cari Hesaplar');
            });
            Route::prefix('financial')->group(function () {
                Route::prefix('transactions')->group(function () {
                    Route::controller(CurrentFinancialTransactionController::class)->group(function () {
                        Route::any('/', 'list')->name('backend.current_financial_transactions_list')->desc('Cari Finansal Hareketler');
                        Route::get('excel/{unique?}', 'exportExcel')->name('backend.current_financial_transactions_export_excel')->desc('Cari Finansal Hareketler');
                    });
                });
            });
            Route::prefix('stock')->group(function () {
                Route::prefix('movements')->group(function () {
                    Route::controller(CurrentStockMovementController::class)->group(function () {
                        Route::any('/', 'list')->name('backend.current_stock_movements_list')->desc('Cari Stok Hareketleri');
                        Route::get('excel/{unique?}', 'exportExcel')->name('backend.current_stock_movements_export_excel')->desc('Cari Stok Hareketleri');
                    });
                });
            });
        });

        Route::prefix('invoice')->group(function () {
            Route::controller(InvoicesController::class)->group(function () {
                // Temel CRUD işlemleri için BaseController metodları
                Route::any('/', 'list')->name('backend.invoices_list')->desc('Satış Faturaları');
                Route::get('form/{unique?}', 'form')->name('backend.invoices_form')->desc('Satış Faturaları');
                Route::post('form/{unique?}', 'save')->name('backend.invoices_save')->desc('Satış Faturaları');
                Route::delete('delete', 'delete')->name('backend.invoices_delete')->desc('Satış Faturaları');
                Route::get('detail/{unique?}', 'detail')->name('backend.invoices_detail')->desc('Satış Faturaları');
                Route::post('status', 'updateStatus')->name('backend.invoices_update_status')->desc('Satış Faturaları');
                Route::get('excel/{unique?}', 'exportExcel')->name('backend.invoices_export_excel')->desc('Satış Faturaları');
                Route::get('pdf/{unique?}', 'pdf')->name('backend.invoices_pdf')->desc('Satış Faturaları');
                Route::post('calculate-totals', 'calculateTotals')->name('backend.invoices_calculate_totals')->desc('Satış Faturaları');
            });
        });

        Route::prefix('purchase')->group(function () {
            Route::prefix('invoice')->group(function () {
                Route::controller(PurchaseInvoiceController::class)->group(function () {
                    // Temel CRUD işlemleri için BaseController metodları
                    Route::any('/', 'list')->name('backend.purchase_invoices_list')->desc('Alış Faturaları');
                    Route::get('form/{unique?}', 'form')->name('backend.purchase_invoices_form')->desc('Alış Faturaları');
                    Route::post('form/{unique?}', 'save')->name('backend.purchase_invoices_save')->desc('Alış Faturaları');
                    Route::delete('delete', 'delete')->name('backend.purchase_invoices_delete')->desc('Alış Faturaları');
                    Route::get('detail/{unique?}', 'detail')->name('backend.purchase_invoices_detail')->desc('Alış Faturaları');
                    Route::post('status', 'updateStatus')->name('backend.purchase_invoices_update_status')->desc('Alış Faturaları');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.purchase_invoices_export_excel')->desc('Alış Faturaları');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.purchase_invoices_pdf')->desc('Alış Faturaları');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.purchase_invoices_calculate_totals')->desc('Alış Faturaları');
                });
            });
        });

        Route::prefix('account')->group(function () {
            Route::prefix('vouchers')->group(function () {
                Route::controller(AccountVouchersController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.account_vouchers_list')->desc('Cari Hesap Fişleri');
                    Route::get('form/{unique?}', 'form')->name('backend.account_vouchers_form')->desc('Cari Hesap Fişleri');
                    Route::post('form/{unique?}', 'save')->name('backend.account_vouchers_save')->desc('Cari Hesap Fişleri');
                    Route::delete('delete', 'delete')->name('backend.account_vouchers_delete')->desc('Cari Hesap Fişleri');
                    Route::get('detail/{unique?}', 'detail')->name('backend.account_vouchers_detail')->desc('Cari Hesap Fişleri');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.account_vouchers_export_excel')->desc('Cari Hesap Fişleri');
                });
            });
        });

        Route::prefix('sale')->group(function () {
            Route::prefix('waybill')->group(function () {
                Route::controller(SaleWaybillController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.sale_waybill_list')->desc('Satış İrsaliyesi');
                    Route::get('form/{unique?}', 'form')->name('backend.sale_waybill_form')->desc('Satış İrsaliyesi');
                    Route::post('form/{unique?}', 'save')->name('backend.sale_waybill_save')->desc('Satış İrsaliyesi');
                    Route::delete('delete', 'delete')->name('backend.sale_waybill_delete')->desc('Satış İrsaliyesi');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.sale_waybill_export_excel')->desc('Satış İrsaliyesi');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.sale_waybill_pdf')->desc('Satış İrsaliyesi');
                    Route::get('detail/{unique?}', 'detail')->name('backend.sale_waybill_detail')->desc('Satış İrsaliyesi');
                    Route::post('status', 'updateStatus')->name('backend.sale_waybill_update_status')->desc('Satış İrsaliyesi');
                });
            });
        });

        Route::prefix('purchase')->group(function () {
            Route::prefix('waybills')->group(function () {
                Route::controller(PurchaseWaybillController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.purchase_waybills_list')->desc('Alış İrsaliyeleri');
                    Route::get('form/{unique?}', 'form')->name('backend.purchase_waybills_form')->desc('Alış İrsaliyeleri');
                    Route::post('form/{unique?}', 'save')->name('backend.purchase_waybills_save')->desc('Alış İrsaliyeleri');
                    Route::delete('delete', 'delete')->name('backend.purchase_waybills_delete')->desc('Alış İrsaliyeleri');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.purchase_waybill_export_excel')->desc('Alış İrsaliyeleri');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.purchase_waybill_pdf')->desc('Alış İrsaliyeleri');
                    Route::get('detail/{unique?}', 'detail')->name('backend.purchase_waybill_detail')->desc('Alış İrsaliyeleri');
                    Route::post('status', 'updateStatus')->name('backend.purchase_waybill_update_status')->desc('Alış İrsaliyeleri');
                });
            });
        });

        Route::prefix('expense')->group(function () {
            Route::controller(ExpenseVouchersController::class)->group(function () {
                Route::any('/', 'list')->name('backend.expense_voucher_list')->desc('Gider Makbuzları');
                Route::get('form/{unique?}', 'form')->name('backend.expense_voucher_form')->desc('Gider Makbuzları');
                Route::post('form/{unique?}', 'save')->name('backend.expense_voucher_save')->desc('Gider Makbuzları');
                Route::delete('delete', 'delete')->name('backend.expense_voucher_delete')->desc('Gider Makbuzları');
                Route::get('detail/{unique?}', 'detail')->name('backend.expense_voucher_detail')->desc('Gider Makbuzları');
                Route::post('update-status/{unique?}', 'updateStatus')->name('backend.expense_voucher_update_status')->desc('Gider Makbuzları');
            });
        });

        Route::prefix('receipt')->group(function () {
            Route::prefix('transactions')->group(function () {
                Route::controller(ReceiptTransactionController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.receipt_transaction_list')->desc('Tahsilat İşlemleri');
                    Route::get('form/{unique?}', 'form')->name('backend.receipt_transaction_form')->desc('Tahsilat İşlemleri');
                    Route::post('form/{unique?}', 'save')->name('backend.receipt_transaction_save')->desc('Tahsilat İşlemleri');
                    Route::delete('delete', 'delete')->name('backend.receipt_transaction_delete')->desc('Tahsilat İşlemleri');
                });
            });
        });

        Route::prefix('payment')->group(function () {
            Route::prefix('transactions')->group(function () {
                Route::controller(PaymentTransactionController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.payment_transaction_list')->desc('Ödeme İşlemleri');
                    Route::get('form/{unique?}', 'form')->name('backend.payment_transaction_form')->desc('Ödeme İşlemleri');
                    Route::post('form/{unique?}', 'save')->name('backend.payment_transaction_save')->desc('Ödeme İşlemleri');
                    Route::delete('delete', 'delete')->name('backend.payment_transaction_delete')->desc('Ödeme İşlemleri');
                });
            });
        });

        Route::prefix('offer')->group(function () {
            Route::prefix('given')->group(function () {
                Route::controller(GivenOfferController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.offer_given_list')->desc('Verilen Teklif');
                    Route::get('form/{unique?}', 'form')->name('backend.offer_given_form')->desc('Verilen Teklif');
                    Route::post('form/{unique?}', 'save')->name('backend.offer_given_save')->desc('Verilen Teklif');
                    Route::delete('delete', 'delete')->name('backend.offer_given_delete')->desc('Verilen Teklif');
                    Route::get('detail/{unique?}', 'detail')->name('backend.offer_given_detail')->desc('Verilen Teklif');
                    Route::post('status', 'updateStatus')->name('backend.offer_given_update_status')->desc('Verilen Teklif');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.offer_given_export_excel')->desc('Verilen Teklif');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.offer_given_pdf')->desc('Verilen Teklif');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.offer_given_calculate_totals')->desc('Verilen Teklif');
                });
            });
            Route::prefix('received')->group(function () {
                Route::controller(ReceivedOfferController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.offer_received_list')->desc('Alınan Teklif');
                    Route::get('form/{unique?}', 'form')->name('backend.offer_received_form')->desc('Alınan Teklif');
                    Route::post('form/{unique?}', 'save')->name('backend.offer_received_save')->desc('Alınan Teklif');
                    Route::delete('delete', 'delete')->name('backend.offer_received_delete')->desc('Alınan Teklif');
                    Route::get('detail/{unique?}', 'detail')->name('backend.offer_received_detail')->desc('Alınan Teklif');
                    Route::post('status', 'updateStatus')->name('backend.offer_received_update_status')->desc('Alınan Teklif');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.offer_received_export_excel')->desc('Alınan Teklif');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.offer_received_pdf')->desc('Alınan Teklif');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.offer_received_calculate_totals')->desc('Alınan Teklif');
                });
            });
        });

        Route::prefix('order')->group(function () {
            Route::prefix('received')->group(function () {
                Route::controller(ReceivedOrderController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.order_received_list')->desc('Alınan Sipariş');
                    Route::get('form/{unique?}', 'form')->name('backend.order_received_form')->desc('Alınan Sipariş');
                    Route::post('form/{unique?}', 'save')->name('backend.order_received_save')->desc('Alınan Sipariş');
                    Route::delete('delete', 'delete')->name('backend.order_received_delete')->desc('Alınan Sipariş');
                    Route::get('detail/{unique?}', 'detail')->name('backend.order_received_detail')->desc('Alınan Sipariş');
                    Route::post('status', 'updateStatus')->name('backend.order_received_update_status')->desc('Alınan Sipariş');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.order_received_calculate_totals')->desc('Alınan Sipariş');
                });
            });
            Route::prefix('given')->group(function () {
                Route::controller(GivenOrderController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.order_given_list')->desc('Verilen Sipariş');
                    Route::get('form/{unique?}', 'form')->name('backend.order_given_form')->desc('Verilen Sipariş');
                    Route::post('form/{unique?}', 'save')->name('backend.order_given_save')->desc('Verilen Sipariş');
                    Route::delete('delete', 'delete')->name('backend.order_given_delete')->desc('Verilen Sipariş');
                    Route::get('detail/{unique?}', 'detail')->name('backend.order_given_detail')->desc('Verilen Sipariş');
                    Route::post('status', 'updateStatus')->name('backend.order_given_update_status')->desc('Verilen Sipariş');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.order_given_calculate_totals')->desc('Verilen Sipariş');
                });
            });
        });

        Route::prefix('general')->group(function () {
            Route::controller(GeneralController::class)->group(function () {
                Route::post('stock-list', 'stockList')->name('backend.general_stock_list')->desc('Genel');
                Route::post('add-selected-products', 'addSelectedProductsToOrder')->name('backend.add_selected_products')->desc('Genel');
                Route::post('add-selected-invoice-products', 'addSelectedProductsToInvoice')->name('backend.add_selected_invoice_products')->desc('Genel'); //addSelectedProductsToGivenOrder
                Route::post('add-selected-purchase-invoice-products', 'addSelectedProductsToPurchaseInvoice')->name('backend.add_selected_purchase_invoice_products')->desc('Genel');
                Route::post('add-selected-order-given-products', 'addSelectedProductsToOrderGiven')->name('backend.add_selected_order_given_products')->desc('Genel');
                Route::post('add-selected-offer-given-products', 'addSelectedProductsToOffer')->name('backend.add_selected_offer_given_products')->desc('Genel');
                Route::post('add-selected-offer-received-products', 'addSelectedProductsToOfferReceived')->name('backend.add_selected_offer_received_products')->desc('Genel');
                Route::post('add-selected-return-purchase-invoice-products', 'addSelectedProductsToReturnPurchaseInvoice')->name('backend.add_selected_return_purchase_invoice_products')->desc('Genel');
                Route::post('add-selected-return-sale-invoice-products', 'addSelectedProductsToReturnSaleInvoice')->name('backend.add_selected_return_sale_invoice_products')->desc('Genel');
            });
        });

        Route::prefix('vat')->group(function () {
            Route::controller(VatRateController::class)->group(function () {
                Route::any('/', 'list')->name('backend.vat_rate_list')->desc('KDV Oranları');
                Route::get('form/{unique?}', 'form')->name('backend.vat_rate_form')->desc('KDV Oranları');
                Route::post('form/{unique?}', 'save')->name('backend.vat_rate_save')->desc('KDV Oranları');
                Route::delete('delete', 'delete')->name('backend.vat_rate_delete')->desc('KDV Oranları');
            });
        });

        Route::prefix('payment')->group(function () {
            Route::prefix('terms')->group(function () {
                Route::controller(PaymentTermController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.payment_term_list')->desc('Vade Tanımlamaları');
                    Route::get('form/{unique?}', 'form')->name('backend.payment_term_form')->desc('Vade Tanımlamaları');
                    Route::post('form/{unique?}', 'save')->name('backend.payment_term_save')->desc('Vade Tanımlamaları');
                    Route::delete('delete', 'delete')->name('backend.payment_term_delete')->desc('Vade Tanımlamaları');
                });
            });
        });
        Route::prefix('return')->group(function () {
            Route::prefix('purchase')->group(function () {
                Route::controller(ReturnPurchaseInvoiceController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.return_purchase_invoice_list')->desc('Alış İade Faturaları');
                    Route::get('form/{unique?}', 'form')->name('backend.return_purchase_invoice_form')->desc('Alış İade Faturaları');
                    Route::post('form/{unique?}', 'save')->name('backend.return_purchase_invoice_save')->desc('Alış İade Faturaları');
                    Route::delete('delete', 'delete')->name('backend.return_purchase_invoice_delete')->desc('Alış İade Faturaları');
                    Route::get('detail/{unique?}', 'detail')->name('backend.return_purchase_invoice_detail')->desc('Alış İade Faturaları');
                    Route::post('status', 'updateStatus')->name('backend.return_purchase_invoice_update_status')->desc('Alış İade Faturaları');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.return_purchase_invoice_calculate_totals')->desc('Alış İade Faturaları');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.return_purchase_invoice_pdf')->desc('Alış İade Faturaları');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.return_purchase_invoice_export_excel')->desc('Alış İade Faturaları');
                });
            });
            Route::prefix('sale')->group(function () {
                Route::controller(ReturnSaleInvoiceController::class)->group(function () {
                    Route::any('/', 'list')->name('backend.return_sale_invoice_list')->desc('Satış İade Faturaları');
                    Route::get('form/{unique?}', 'form')->name('backend.return_sale_invoice_form')->desc('Satış İade Faturaları');
                    Route::post('form/{unique?}', 'save')->name('backend.return_sale_invoice_save')->desc('Satış İade Faturaları');
                    Route::delete('delete', 'delete')->name('backend.return_sale_invoice_delete')->desc('Satış İade Faturaları');
                    Route::get('detail/{unique?}', 'detail')->name('backend.return_sale_invoice_detail')->desc('Satış İade Faturaları');
                    Route::post('status', 'updateStatus')->name('backend.return_sale_invoice_update_status')->desc('Satış İade Faturaları');
                    Route::post('calculate-totals', 'calculateTotals')->name('backend.return_sale_invoice_calculate_totals')->desc('Satış İade Faturaları');
                    Route::get('pdf/{unique?}', 'pdf')->name('backend.return_sale_invoice_pdf')->desc('Satış İade Faturaları');
                    Route::get('excel/{unique?}', 'exportExcel')->name('backend.return_sale_invoice_export_excel')->desc('Satış İade Faturaları');
                });
            });
        });

        // Sistem Ayarları
        Route::prefix('setting')->group(function () {
            Route::controller(SettingController::class)->group(function () {
                Route::get('/', 'form')->name('backend.setting_form')->desc('Sistem Ayarları');
                Route::post('/', 'save')->name('backend.setting_save')->desc('Sistem Ayarları');
            });
        });
    });
});

Route::prefix('authentication')->group(function () {
    Route::controller(AuthenticationController::class)->group(function () {
        Route::get('/forgotpassword', 'forgotPassword')->name('forgotPassword');
        Route::get('/signin', 'signin')->name('signin');
        Route::get('/login', 'signin')->name('login'); // Laravel'in aradığı login route'u
        Route::get('/signup', 'signup')->name('signup');
        Route::post('login', 'access')->name('signin.post');
        Route::get('logout', 'logout')->name('logout');
    });
});
