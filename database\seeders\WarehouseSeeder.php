<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WarehouseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $warehouses = [
                [
                    'name' => 'Ana Depo',
                    'code' => 'ANA-01',
                    'description' => 'Merkez ana depo - İstanbul',
                    'address' => 'İstanbul, Ümraniye, Sanayi <PERSON>, Depo Caddesi No:1',
                    'phone' => '+90 (216) 555 01 01',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 100000.000,
                    'max_volume_capacity' => 10000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'İzmir Depo',
                    'code' => 'IZM-01',
                    'description' => 'İzmir bölge deposu',
                    'address' => 'İzmir, Bornova, Organize <PERSON><PERSON><PERSON>, 5. Cadde No:10',
                    'phone' => '+90 (232) 555 02 02',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 50000.000,
                    'max_volume_capacity' => 5000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'Ankara Depo',
                    'code' => 'ANK-01',
                    'description' => 'Ankara bölge deposu',
                    'address' => 'Ankara, Sincan, Organize Sanayi Bölgesi, 15. Cadde No:25',
                    'phone' => '+90 (312) 555 03 03',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 30000.000,
                    'max_volume_capacity' => 3000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'Hammadde Deposu',
                    'code' => 'HAM-01',
                    'description' => 'Hammadde ve yarı mamul deposu',
                    'address' => 'İstanbul, Ümraniye, Sanayi Mahallesi, Depo Caddesi No:2',
                    'phone' => '+90 (216) 555 04 04',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 80000.000,
                    'max_volume_capacity' => 8000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'İhracat Deposu',
                    'code' => 'IHR-01',
                    'description' => 'İhracat ürünleri deposu - Serbest Bölge',
                    'address' => 'İstanbul, Tuzla, Serbest Bölge, 3. Cadde No:8',
                    'phone' => '+90 (216) 555 05 05',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 60000.000,
                    'max_volume_capacity' => 6000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'Soğuk Hava Deposu',
                    'code' => 'SHD-01',
                    'description' => 'Soğuk zincir ürünleri deposu',
                    'address' => 'İstanbul, Pendik, Soğuk Zincir Bölgesi, 7. Cadde No:15',
                    'phone' => '+90 (216) 555 06 06',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 20000.000,
                    'max_volume_capacity' => 2000.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'temperature_min' => -18.00,
                    'temperature_max' => 4.00,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'Transit Depo',
                    'code' => 'TRA-01',
                    'description' => 'Geçici depolama ve transit depo',
                    'address' => 'İstanbul, Ümraniye, Sanayi Mahallesi, Transit Caddesi No:3',
                    'phone' => '+90 (216) 555 07 07',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 15000.000,
                    'max_volume_capacity' => 1500.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
                [
                    'name' => 'Quarantine Depo',
                    'code' => 'QUA-01',
                    'description' => 'Karantina ve kalite kontrol deposu',
                    'address' => 'İstanbul, Ümraniye, Sanayi Mahallesi, Kalite Caddesi No:4',
                    'phone' => '+90 (216) 555 08 08',
                    'email' => '<EMAIL>',
                    'max_weight_capacity' => 5000.000,
                    'max_volume_capacity' => 500.0000,
                    'current_weight' => 0.000,
                    'current_volume' => 0.0000,
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ],
            ];

            foreach ($warehouses as $warehouse) {
                Warehouse::updateOrCreate(
                    ['code' => $warehouse['code']],
                    $warehouse
                );
            }
        });

        $this->command->info('Warehouse verileri başarıyla oluşturuldu.');
    }
}
