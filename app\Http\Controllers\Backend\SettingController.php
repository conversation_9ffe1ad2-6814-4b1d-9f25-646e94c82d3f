<?php

namespace App\Http\Controllers\Backend;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Sistem Ayarları';
        $this->page = 'setting';
        $this->model = new Setting();

        $this->view = (object)[
            'breadcrumb' => [
                'Sistem Yönetimi' => '#',
                'Ayarlar' => route('backend.setting_form'),
            ],
        ];

        $this->validation = [
            [
                'decimal_places' => 'required|integer|min:2|max:8'
            ],
            [
                'decimal_places.required' => 'Ondalık basamak sayısı boş bırakılamaz.',
                'decimal_places.integer' => 'Ondalık basamak sayısı sayısal bir değer olmalıdır.',
                'decimal_places.min' => 'Ondalık basamak sayısı en az 2 olmalıdır.',
                'decimal_places.max' => 'Ondalık basamak sayısı en fazla 8 olmalıdır.',
            ]
        ];

        parent::__construct();
    }

    public function form(Request $request, $unique = null)
    {
        // Ayarlar için özel form - tek kayıt üzerinde çalışır
        $item = Setting::where('key', 'decimal_places')->first();

        if (!$item) {
            // Eğer ayar yoksa oluştur
            $item = Setting::create([
                'key' => 'decimal_places',
                'value' => '2',
                'name' => 'Para Gösterimi - Ondalık Basamak Sayısı',
                'description' => 'Paranın ondalık kısmında kaç basamak gösterileceğini belirler (2-8 arası)',
                'type' => 'select',
                'options' => [
                    '2' => '2 Basamak',
                    '3' => '3 Basamak',
                    '4' => '4 Basamak',
                    '5' => '5 Basamak',
                    '6' => '6 Basamak',
                    '7' => '7 Basamak',
                    '8' => '8 Basamak'
                ]
            ]);
        }

        return view("backend.{$this->page}.form", compact('item'));
    }

    public function save(Request $request, $unique = null)
    {
        $request->validate($this->validation[0], $this->validation[1]);

        $params = $request->all();
        if (method_exists($this, 'saveHook')) {
            $params = $this->saveHook($request);
        }

        // decimal_places ayarını güncelle
        $setting = Setting::where('key', 'decimal_places')->first();
        if ($setting) {
            $setting->update(['value' => $params['decimal_places']]);
        }

        // Cache'i temizle
        Cache::forget('setting_decimal_places');

        return redirect()->route("backend.{$this->page}_form")->with('success', 'Ayarlar başarılı şekilde güncellendi');
    }

    public function saveHook(Request $request)
    {
        return $request->all();
    }
}
