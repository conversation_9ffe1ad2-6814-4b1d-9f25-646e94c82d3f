@extends('layout.layout')
@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.offer_given_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-4">
                    <label class="form-label">Cari</label>
                    <select class="form-select select2" id="filter-current" filter-name="filter-current">
                        <option value="">Cari Seçiniz...</option>
                        @foreach($current as $cari)
                            <option value="{{ $cari->id }}">{{ $cari->title }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Teklif <PERSON></label>
                    <input type="date" class="form-control" id="filter-start-date" filter-name="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Teklif Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date" filter-name="filter-end-date">
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Teklif Tarihi</th>
                            <th scope="col" class="text-center">Teklif No</th>
                            <th scope="col" class="text-center">Cari Unvanı</th>
                            <th scope="col" class="text-center">Genel Toplam</th>
                            <th scope="col" class="text-center">Teklif Bitiş Tarihi</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.offer_given_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        return $.extend({}, d, {
                            start_date: $('[filter-name="filter-start-date"]').val(),
                            end_date: $('[filter-name="filter-end-date"]').val(),
                            current_id: $('[filter-name="filter-current"]').val()
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'offer_date',
                        name: 'offer_date',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                var date = new Date(data);
                                return date.toLocaleDateString('tr-TR');
                            }
                            return data;
                        }
                    },
                    {
                        data: 'offer_number',
                        name: 'offer_number',
                        className: 'text-center',
                    },
                    {
                        data: 'current_info',
                        name: 'current.name',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (!data) return '-';

                            let html = '';

                            if (data.deleted) {
                                html += `<span class="badge bg-danger">Silinmiş</span><br>`;
                            }

                            html += `${data.name || '-'} ${data.surname || ''}`;

                            return html;
                        }
                    },
                    {
                        data: 'total_amount',
                        name: 'total_amount',
                        className: 'text-center',
                        render: function(data, type, row) {
                            let currencySymbol = '₺'; // Varsayılan sembol (TL)
                            return parseFloat(data).toLocaleString('tr-TR', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }) + ' ' + currencySymbol;
                        }
                    },
                    {
                        data: 'offer_deadline',
                        name: 'offer_deadline',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                if (data) {
                                    var date = new Date(data);
                                    return date.toLocaleDateString('tr-TR');
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    {
                        render: function(data, type, row) {
                            if (!row.product_statuses || row.product_statuses.length === 0) {
                                return '<span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Beklemede</span>';
                            }
                            var statusCounts = {
                                0: 0, // Beklemede
                                1: 0, // Onaylandı
                                2: 0 // Reddedildi
                            };
                            var totalQuantity = 0;
                            row.product_statuses.forEach(function(item) {
                                if (statusCounts.hasOwnProperty(item.status)) {
                                    statusCounts[item.status] += item.quantity;
                                }
                                totalQuantity += item.quantity;
                            });
                            var html = '';
                            var totalItems = statusCounts[0] + statusCounts[1] + statusCounts[2];
                            if (statusCounts[0] > 0) {
                                html +=
                                    '<div class="mb-1"><span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Beklemede</span></div>';
                                html += '<div class="text-center text-sm mb-2">O: ' + statusCounts[
                                    1] + ' | B: ' + statusCounts[0] + ' | R: ' + statusCounts[
                                    2] + '</div>';
                            } else if (statusCounts[1] === totalItems && totalItems > 0) {
                                html +=
                                    '<div class="mb-1"><span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span></div>';
                                html += '<div class="text-center text-sm mb-2">O: ' + statusCounts[
                                    1] + ' | B: ' + statusCounts[0] + ' | R: ' + statusCounts[
                                    2] + '</div>';
                            } else if (statusCounts[2] === totalItems && totalItems > 0) {
                                html +=
                                    '<div class="mb-1"><span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Reddedildi</span></div>';
                                html += '<div class="text-center text-sm mb-2">O: ' + statusCounts[
                                    1] + ' | B: ' + statusCounts[0] + ' | R: ' + statusCounts[
                                    2] + '</div>';
                            } else if (statusCounts[1] > 0 || statusCounts[2] > 0) {
                                if (statusCounts[1] >= statusCounts[2]) {
                                    html +=
                                        '<div class="mb-1"><span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span></div>';
                                } else {
                                    html +=
                                        '<div class="mb-1"><span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Reddedildi</span></div>';
                                }
                                html += '<div class="text-center text-sm mb-2">O: ' + statusCounts[
                                    1] + ' | B: ' + statusCounts[0] + ' | R: ' + statusCounts[
                                    2] + '</div>';
                            }

                            return html;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                        width: '120px'
                    },
                    {
                        render: function(data, type, row) {
                            var detailUrl = '{{ url('admin/offer/given/detail') }}/' + row.id;
                            var editUrl = '{{ route('backend.offer_given_form') }}/' + row.id;
                            var excelUrl = '{{ route('backend.offer_given_export_excel', [':id']) }}'.replace(':id', row.id);
                            var pdfUrl = '{{ route('backend.offer_given_pdf', [':id']) }}'.replace(':id', row.id);
                            var hasApprovedProducts = false;
                            var allProductsApproved = false;

                            if (row.product_statuses && row.product_statuses.length > 0) {
                                var approvedCount = 0;

                                row.product_statuses.forEach(function(item) {
                                    if (item.status == 1) { // Onaylanmış ürün
                                        hasApprovedProducts = true;
                                        approvedCount++;
                                    }
                                });
                                if (approvedCount === row.product_statuses.length) {
                                    allProductsApproved = true;
                                }
                            }

                            var deleteButton = '';
                            if (!hasApprovedProducts) {
                                deleteButton = `<button type="button" class="remove-item-btn bg-danger-100 text-danger-600 bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}" title="Sil">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>`;
                            } else {
                                deleteButton = `<button type="button" class="bg-danger-100 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" style="opacity: 0.5; cursor: not-allowed;" title="Onaylanmış ürün içeren teklif silinemez">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>`;
                            }

                            var excelButton = `<a href="${excelUrl}" class="bg-success-100 text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" title="Excele Aktar">
                                <iconify-icon icon="mdi:microsoft-excel" class="menu-icon"></iconify-icon>
                            </a>`;
                            var pdfButton = `<a href="${pdfUrl}" target="_blank" class="bg-danger-100 text-danger-600 bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" title="PDF'e Aktar">
                                   <iconify-icon icon="mdi:file-pdf-box" width="24" height="24"></iconify-icon>
                                </a>`;


                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                ${excelButton}
                                ${pdfButton}
                                <a href="${detailUrl}" title="Teklif Detay" class="bg-primary-light text-primary-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="iconamoon:eye-light" class="menu-icon"></iconify-icon>
                                </a>
                                <a href="${editUrl}" class="bg-success-100 text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" title="Düzenle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                ${deleteButton}
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            });

            // Cari seçimi sonrası tabloyu yenile
            $('#btn-select-current').on('click', function() {
            });
            // Eğer cari seçimi başka bir event ile oluyorsa, aşağıdaki kodu kullanın:
            $('#filter-current').on('change', function() {
                $(`[datatable]`).DataTable().ajax.reload();
            });
            // Tarih ve cari inputlarında filter-name olduğu için aşağıdaki kod çalışacak:
            $('[filter-name]').on('change input', function() {
                $(`[datatable]`).DataTable().ajax.reload();
            });
            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");
        });
    </script>
@endsection
