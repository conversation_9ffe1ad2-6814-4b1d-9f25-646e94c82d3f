@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . $container->page . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Ürün</th>
                            <th scope="col">Varyant</th>
                            <th scope="col">Rezerve Miktarı</th>
                            <th scope="col">Rezervasyon Nedeni</th>
                            <th scope="col"><PERSON>i <PERSON></th>
                            <th scope="col">Başlangıç</th>
                            <th scope="col">Bitiş</th>
                            <th scope="col" class="text-center">Öncelik</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'product.name',
                        name: 'product.name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return row.variant ? row.variant.name : '-';
                        },
                        className: 'text-center',
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center',
                        render: function(data) {
                            return parseFloat(data).toFixed(2);
                        }
                    },
                    {
                        render: function(data, type, row) {
                            return row.reservation_reason ? row.reservation_reason.name : '-';
                        },
                        className: 'text-center',
                    },
                    {
                        data: 'current.title',
                        name: 'current.title',
                        className: 'text-center',
                    },
                    {
                        data: 'start_date',
                        name: 'start_date',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                if (data) {
                                    var date = new Date(data);
                                    return date.toLocaleString('tr-TR', {
                                        day: '2-digit',
                                        month: '2-digit',
                                        year: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        hour12: false
                                    });
                                }
                                return '-';
                            }
                            return data;
                        }

                    },
                    {
                        data: 'end_date',
                        name: 'end_date',
                        className: 'text-center',
                    },
                    {
                        data: 'priority_level',
                        name: 'priority_level',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> İptal </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            var buttons = `
                            <td class="text-center">
                                <div class="d-flex align-items-center gap-10 justify-content-center">`;

                            if (row.is_active == 'Aktif') {
                                buttons += `
                                    <button type="button" class="btn-status bg-danger-focus text-danger-600 bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-id="${row.id}" data-status="0" title="İptal Et">
                                        <iconify-icon icon="lucide:x" class="menu-icon"></iconify-icon>
                                    </button>`;
                            } else {
                                buttons += `
                                    <button type="button" class="btn-status bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-id="${row.id}" data-status="1" title="Aktif Et">
                                        <iconify-icon icon="lucide:check" class="menu-icon"></iconify-icon>
                                    </button>`;
                            }

                            buttons += `
                                    <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-primary-focus text-primary-600 bg-hover-primary-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                    </a>
                                    <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                        <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                    </button>
                                </div>
                            </td>`;

                            return buttons;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    }
                ],
                order: [
                    [10, 'desc'],
                ],
                pageLength: 15,
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

            $(document).on('click', '.btn-status', function() {
                var id = $(this).data('id');
                var status = $(this).data('status');
                var btn = $(this);

                btn.prop('disabled', true);

                $.ajax({
                    url: "{{ route('backend.stock_reservation_status') }}",
                    type: 'POST',
                    data: {
                        id: id,
                        status: status,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        table.ajax.reload(null, false);
                    },
                    complete: function() {
                        btn.prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endsection
