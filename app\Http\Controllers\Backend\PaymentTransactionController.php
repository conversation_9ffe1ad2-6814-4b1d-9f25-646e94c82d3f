<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\PaymentType;
use App\Models\Balance;
use App\Models\ExchangeRate;
use App\Models\InvoiceType;
use App\Models\Transaction;
use App\Models\TransactionType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentTransactionController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ödeme İşlemleri';
        $this->page = 'payment_transaction';
        $this->model = new Transaction();
        $this->listQuery = Transaction::with(['invoice']) // << BURAYA DİKKAT
            ->filter(request())
            ->where('transaction_type_id', 1);
        $this->relation = ['current', 'paymentType', 'exchangeRate'];
        $this->view = (object)[
            'breadcrumb' => [
                'Ödeme İşlemleri' => route('backend.payment_transaction_list'),
            ],
        ];
        $this->validation = [
            [
                'transaction_date' => 'required|date',
                'invoice_id' => 'required|integer',
                'invoice_type' => 'required|integer|in:1,4',
                'current_id' => 'required|exists:currents,id',
                'payment_method' => 'required|exists:payment_types,id',
                'amount' => 'required|numeric|min:0.01',
                'exchange_rate_id' => 'required|exists:exchange_rates,id',
                'document_no' => 'nullable|string|max:50',
                'bank_account' => 'nullable|string|max:100',
                'due_date' => 'nullable|date',
                'description' => 'nullable|string|max:500',
            ],
            [
                'transaction_date.required' => 'İşlem tarihi alanı zorunludur.',
                'transaction_date.date' => 'İşlem tarihi geçerli bir tarih olmalıdır.',
                'invoice_id.required' => 'Fatura seçimi zorunludur.',
                'invoice_type.required' => 'Fatura tipi seçimi zorunludur.',
                'current_id.required' => 'Cari hesap seçimi zorunludur.',
                'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
                'payment_method.required' => 'Ödeme yöntemi seçimi zorunludur.',
                'payment_method.exists' => 'Seçilen ödeme yöntemi geçerli değil.',
                'amount.required' => 'Tutar alanı zorunludur.',
                'amount.numeric' => 'Tutar alanı sayısal bir değer olmalıdır.',
                'amount.min' => 'Tutar alanı en az 0.01 olmalıdır.',
                'exchange_rate_id.required' => 'Para birimi seçimi zorunludur.',
                'exchange_rate_id.exists' => 'Seçilen para birimi geçerli değil.',
                'document_no.max' => 'Belge no en fazla 50 karakter olabilir.',
                'bank_account.max' => 'Banka hesabı en fazla 100 karakter olabilir.',
                'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
                'description.max' => 'Açıklama en fazla 500 karakter olabilir.',
            ]
        ];
        view()->share('transactionType', TransactionType::get());
        view()->share('currents', Current::get());
        view()->share('exchangeRate', ExchangeRate::where('is_active', 1)->get());
        view()->share('paymentMethods', PaymentType::active()->pluck('name', 'id')->toArray());
        view()->share('invoiceTypes', InvoiceType::whereIn('id', [1, 4])->pluck('name', 'id')->toArray());
        // Liste sayfası için tüm faturaları paylaş (status 2, 4, 5)
        view()->share('allInvoices', Invoice::with('invoiceStatus')
            ->active()
            ->whereIn('invoice_type_id', [1,4])
            ->whereIn('invoice_status_id', [2, 4, 5])
            ->get());

        // Form sayfası için sadece status 2, 4 olan faturaları paylaş
        view()->share('filteredInvoices', Invoice::with('invoiceStatus')
            ->active()
            ->whereIn('invoice_type_id', [1,4])
            ->whereIn('invoice_status_id', [2, 4])
            ->get());
        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('transaction_date', function ($row) {
                return $row->transaction_date ? $row->transaction_date->format('d.m.Y H:i') : '';
            })
            ->addColumn('invoice_no', function ($row) {
                return Invoice::find($row->invoice_id)?->invoice_no ?? '—';
            })
            ->addColumn('amount_formatted', function ($row) {
            $exchangeRate = ExchangeRate::find($row->exchange_rate_id);
            $currencySymbol = $exchangeRate ? $exchangeRate->symbol : '₺';

            return number_format($row->amount, 2, ',', '.') . ' ' . $currencySymbol;
        });
    }

    public function saveHook(Request $request)
    {
        $invoiceId = $request->input('invoice_id');
        $amount = (float) $request->input('amount');
        $currentId = $request->input('current_id');
        $exchangeRateId = $request->input('exchange_rate_id');
        $transactionId = $request->route('unique'); // Mevcut transaction ID'si

        // Faturayı getir
        $invoice = Invoice::findOrFail($invoiceId);
        $exchangeRate = ExchangeRate::find($exchangeRateId);

        // Fatura para birimine göre amount belirleme
        $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
        $exchangeRateValue = $exchangeRate ? $exchangeRate->selling_rate : 1.0000;

        // Eğer güncelleme işlemi ise ve exchange_rate_id boşsa, fatura bilgilerinden al
        if ($transactionId && !$exchangeRateId) {
            $existingTransaction = Transaction::find($transactionId);
            if ($existingTransaction && $existingTransaction->exchange_rate_id) {
                $exchangeRateId = $existingTransaction->exchange_rate_id;
                $exchangeRate = ExchangeRate::find($exchangeRateId);
                $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
                $exchangeRateValue = $exchangeRate ? $exchangeRate->selling_rate : 1.0000;
            }
        }

        // Eğer güncelleme işlemi ise, önceki tutarları çıkar
        if ($transactionId) {
            $existingTransaction = Transaction::find($transactionId);
            if ($existingTransaction) {
                $oldAmount = (float) $existingTransaction->amount;
                $oldExchangeRate = ExchangeRate::find($existingTransaction->exchange_rate_id);
                $oldCurrencyCode = $oldExchangeRate ? $oldExchangeRate->code : 'TRY';
                $oldExchangeRateValue = $oldExchangeRate ? $oldExchangeRate->selling_rate : 1.0000;

                // Balance'dan eski tutarı çıkar
                $balance = Balance::where('current_id', $currentId)->first();
                if ($balance) {
                    $balance->debit_balance -= $oldAmount;
                    $balance->save();

                    // BalanceCurrency'dan eski tutarı çıkar
                    $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $oldCurrencyCode)->first();
                    if ($balanceCurrency) {
                        $balanceCurrency->debit_balance -= $oldAmount;
                        $balanceCurrency->save();
                    }
                }

                // Faturadaki collected değerini güncelle
                if ($oldCurrencyCode == 'TRY') {
                    $invoice->collected = $invoice->collected - $oldAmount;
                } else {
                    $invoice->collected_fx = $invoice->collected_fx - $oldAmount;
                }
                $invoice->save();
            }
        }

        // Fatura para birimi bilgilerini al
        $invoiceExchangeRate = $invoice->exchangeRate;
        $invoiceCurrencyCode = $invoiceExchangeRate ? $invoiceExchangeRate->code : 'TRY';
        $invoiceExchangeRateValue = $invoiceExchangeRate ? $invoiceExchangeRate->selling_rate : 1.0000;

        // Tahsilat para birimi ile fatura para birimi aynı mı kontrol et
        $isSameCurrency = ($currencyCode === $invoiceCurrencyCode);


        // Daha önce tahsil edilen miktarı bul (güncelleme durumunda mevcut transaction hariç)
        $totalPaid = Transaction::where('invoice_id', $invoiceId);
        if ($transactionId) {
            $totalPaid = $totalPaid->where('id', '!=', $transactionId);
        }
        $totalPaid = $totalPaid->sum('amount');

        // Kalan tutarı hesapla - seçilen para birimine göre
        if ($isSameCurrency) {
            // Aynı para birimi ise direkt hesapla
            if ($currencyCode == 'TRY') {
                $invoiceTotal = (float) ($invoice->total_amount ?? 0);
                $invoiceCollected = (float) ($invoice->collected ?? 0);
            } else {
                $invoiceTotal = (float) ($invoice->total_amount_fx ?? 0);
                $invoiceCollected = (float) ($invoice->collected_fx ?? 0);
            }
            $remainingAmount = $invoiceTotal - $totalPaid;
        } else {
            // Farklı para birimi ise dönüşüm yap
            if ($currencyCode == 'TRY' && $invoiceCurrencyCode != 'TRY') {
                // Tahsilat TRY, fatura döviz
                $invoiceTotalTRY = (float) ($invoice->total_amount ?? 0);
                $invoiceCollectedTRY = (float) ($invoice->collected ?? 0);
                $remainingAmount = $invoiceTotalTRY - $totalPaid;
            } elseif ($currencyCode != 'TRY' && $invoiceCurrencyCode == 'TRY') {
                // Tahsilat döviz, fatura TRY
                $invoiceTotalFX = (float) ($invoice->total_amount_fx ?? 0);
                $invoiceCollectedFX = (float) ($invoice->collected_fx ?? 0);
                $remainingAmount = $invoiceTotalFX - $totalPaid;
            } else {
                // Her ikisi de farklı döviz
                if ($currencyCode == 'TRY') {
                    $invoiceTotal = (float) ($invoice->total_amount ?? 0);
                    $invoiceCollected = (float) ($invoice->collected ?? 0);
                } else {
                    $invoiceTotal = (float) ($invoice->total_amount_fx ?? 0);
                    $invoiceCollected = (float) ($invoice->collected_fx ?? 0);
                }
                $remainingAmount = $invoiceTotal - $totalPaid;
            }
        }

        // // Kalan tutar kontrolü - yeni tutar kalan tutardan fazla olamaz (floating point hassasiyet için tolerans)
        // if ($amount > ($remainingAmount + 0.01)) {
        //     throw new \Exception('Ödeme  tutarı kalan tutardan fazla olamaz. Kalan tutar: ' . number_format($remainingAmount, 2, ',', '.') . ' ' . $currencyCode);
        // }

        // Balance ve BalanceCurrency işlemleri
        $current = Current::find($currentId);
        if ($current) {
            $balance = $current->balance()->where('current_id', $current->id)->first();

            if ($balance) {
                // Eğer balance varsa, credit_balance'ı güncelle
                $balance->increment('debit_balance', $amount);
                $balance->save();
            } else {
                // Eğer balance yoksa, yeni bir kayıt oluştur
                $balance = $current->balance()->create([
                    'debit_balance' => $amount,
                    'is_active' => 1,
                    'created_by' => Auth::id(),
                ]);
            }

            // BalanceCurrency işlemleri (dövizli bakiye)
            $balanceCurrency = $balance->balanceCurrencies()->where('currency_code', $currencyCode)->first();
            if ($balanceCurrency) {
                $balanceCurrency->increment('debit_balance', $amount);
                $balanceCurrency->save();
            } else {
                $balance->balanceCurrencies()->create([
                    'currency_code' => $currencyCode,
                    'debit_balance' => $amount,
                    'credit_balance' => 0,
                    'current_id' => $current->id,
                ]);
            }
        }
        // Faturadaki tahsil edilen miktarı güncelle - seçilen para birimine göre
        if ($isSameCurrency) {
            // Aynı para birimi ise direkt ekle
            if ($currencyCode == 'TRY') {
                $invoice->collected = ($invoice->collected ?? 0) + $amount;
            } else {
                $invoice->collected_fx = ($invoice->collected_fx ?? 0) + $amount;
            }
        } else {
            // Farklı para birimi ise dönüşüm yaparak ekle
            if ($currencyCode == 'TRY' && $invoiceCurrencyCode != 'TRY') {
                // Tahsilat TRY, fatura döviz - TRY'ye ekle
                $invoice->collected = ($invoice->collected ?? 0) + $amount;
            } elseif ($currencyCode != 'TRY' && $invoiceCurrencyCode == 'TRY') {
                // Tahsilat döviz, fatura TRY - dövize ekle
                $invoice->collected_fx = ($invoice->collected_fx ?? 0) + $amount;
            } else {
                // Her ikisi de farklı döviz - seçilen para birimine göre ekle
                if ($currencyCode == 'TRY') {
                    $invoice->collected = ($invoice->collected ?? 0) + $amount;
                } else {
                    $invoice->collected_fx = ($invoice->collected_fx ?? 0) + $amount;
                }
            }
        }

        // Fatura durumunu güncelle
        $this->updateInvoiceStatus($invoice, $currencyCode, $invoiceCurrencyCode);

        $invoice->save();

        return $request->all();
    }
    private function updateInvoiceStatus($invoice, $paymentCurrency, $invoiceCurrency)
    {
        // Fatura toplam tutarlarını al
        $invoiceTotalTRY = (float) ($invoice->total_amount ?? 0);
        $invoiceTotalFX = (float) ($invoice->total_amount_fx ?? 0);
        $invoiceCollectedTRY = (float) ($invoice->collected ?? 0);
        $invoiceCollectedFX = (float) ($invoice->collected_fx ?? 0);

        // Tahsilat para birimine göre kalan tutarı hesapla
        $remainingAmount = 0;

        if ($paymentCurrency == 'TRY') {
            // Tahsilat TRY ise
            if ($invoiceCurrency == 'TRY') {
                // Fatura da TRY ise
                $remainingAmount = $invoiceTotalTRY - $invoiceCollectedTRY;
            } else {
                // Fatura döviz ise - TRY'deki kalan tutar
                $remainingAmount = $invoiceTotalTRY - $invoiceCollectedTRY;
            }
        } else {
            // Tahsilat döviz ise
            if ($invoiceCurrency == 'TRY') {
                // Fatura TRY ise - dövizdeki kalan tutar
                $remainingAmount = $invoiceTotalFX - $invoiceCollectedFX;
            } else {
                // Fatura da döviz ise - dövizdeki kalan tutar
                $remainingAmount = $invoiceTotalFX - $invoiceCollectedFX;
            }
        }

        // Kalan tutar 0.01'den küçükse tam tahsilat
        if (abs($remainingAmount) < 0.01) {
            $invoice->invoice_status_id = 5; // Muhasebelendi
        } else {
            $invoice->invoice_status_id = 4; // Kısmi Muhasebelendi
        }
    }

}
