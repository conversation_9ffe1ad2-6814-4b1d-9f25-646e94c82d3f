@extends('layout.layout')

@php
    $title = $container->title ?? 'Faturalar';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.purchase_invoices_export_excel') }}"
                    class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                    Excel
                </a>
                <a href="{{ route('backend.purchase_invoices_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    Ekle
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">#</th>
                            <th scope="col" class="text-center">Fatura No</th>
                            <th scope="col" class="text-center">Cari Adı</th>
                            <th scope="col" class="text-center">Fatura Tarihi</th>
                            <th scope="col" class="text-center">Aktif/Pasif</th>
                            <th scope="col" class="text-center">Ödeme Durumu</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            $(document).on('shown.bs.modal', '[id^="detailModal"]', function() {
                const modalId = $(this).attr('id');
                const rowId = modalId.replace('detailModal', '');

                // Ödeme bilgilerini doldur
                try {
                    const paymentInfoStr = $(`[data-payment-info="${rowId}"]`).val();
                    if (paymentInfoStr) {
                        const paymentInfo = JSON.parse(paymentInfoStr);
                        $(`#total-amount-${rowId}`).text(paymentInfo.total_amount_formatted);
                        $(`#total-paid-${rowId}`).text(paymentInfo.total_paid_formatted);
                        $(`#remaining-amount-${rowId}`).text(paymentInfo.remaining_amount_formatted);
                    }
                } catch (e) {
                    console.error('Error parsing payment info:', e);
                }
            });

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.purchase_invoices_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    }
                },
                columns: [{
                        data: 'id',
                        name: 'id',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_no',
                        name: 'invoice_no',
                        className: 'text-center'
                    },
                    {
                        data: 'current.title',
                        name: 'current.title',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_date',
                        name: 'invoice_date',
                        className: 'text-center',
                        render: function(data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                if (data) {
                                    var date = new Date(data);
                                    return date.toLocaleDateString('tr-TR');
                                }
                                return '-';
                            }
                            return data;
                        }
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                    {
                        data: 'invoiceStatus.name',
                        name: 'invoiceStatus.name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return `
                <td class="text-center">
                    <div class="d-flex align-items-center gap-10 justify-content-center">
                         <a href="{{ route('backend.purchase_invoices_detail') }}/${row.id}" class="bg-primary-light text-primary-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                            <iconify-icon icon="iconamoon:eye-light" class="menu-icon"></iconify-icon>
                        </a>
                        <a href="{{ route('backend.purchase_invoices_pdf') }}/${row.id}" class="bg-danger-100 text-danger-600 bg-hover-danger-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center  rounded-circle" target="_blank">
                            <iconify-icon icon="mdi:file-pdf-box" width="24" height="24"></iconify-icon>
                        </a>
                        <a href="{{ route('backend.purchase_invoices_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                            <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                        </a>
                        <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                            <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                        </button>
                    </div>
                </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");
        });
    </script>
@endsection
