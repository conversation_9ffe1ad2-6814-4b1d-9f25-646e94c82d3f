<?php

namespace App\Http\Controllers\Backend;

use App\Http\Requests\Backend\OfferRequest;
use App\Models\Offer;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\OfferProduct;
use App\Models\OfferType;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PaymentType;
use App\Models\Stock;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Exports\ExportOffer;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Barryvdh\DomPDF\Facade\PDF;

class GivenOfferController extends BaseController
{
    use BasePattern;

    // Constants to replace magic numbers
    private const GIVEN_OFFER_TYPE_ID = 2;
    private const RECEIVED_ORDER_TYPE_ID = 1;
    private const APPROVED_STATUS = 1;
    private const SAMPLE_ITEM_COUNT = 20;
    private const DEFAULT_CURRENCY = 'TRY';
    private const DEFAULT_CURRENCY_SYMBOL = '₺';

    private function handleResponse(Request $request, bool $success, string $message, array $errors = [], ?string $redirect = null): mixed
    {
        if ($request->ajax()) {
            $response = [
                'success' => $success,
                'message' => $message
            ];

            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            if ($success && $redirect) {
                $response['redirect'] = $redirect;
            }

            return response()->json($response, $success ? 200 : 422);
        }

        if ($success) {
            $redirectTo = $redirect ?? route("backend.{$this->page}_list");
            return redirect($redirectTo)->with('success', $message);
        } else {
            $redirect = redirect()->back()->with('error', $message);
            if (!empty($errors)) {
                $redirect->withErrors($errors);
            }
            return $redirect->withInput();
        }
    }

    public function __construct()
    {
        $this->title = 'Verilen Teklif';
        $this->page = 'offer_given';
        $this->model = new Offer();
        $this->relation = [
            // 'branch',
            'warehouse',
            'exchangeRate',
            'current' => function ($query) {
                $query->withTrashed();
            },
            'paymentType',
            'offerProducts.stock.product.unit',
            'offerProducts.stock.variant'
        ];
        $this->listQuery = Offer::filter(request())->where('offer_type_id', self::GIVEN_OFFER_TYPE_ID);
        $this->view = (object)array(
            'breadcrumb' => array(
                'Ayarlar' => '#',
                'Verilen Teklif' => route('backend.offer_given_list'),
            ),
        );
        view()->share('offerProducts', OfferProduct::get());
        // view()->share('branches', Branch::get());
        view()->share('warehouses', Warehouse::get());
        $exchangeRates = ExchangeRate::where('is_active', 1)
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('exchange_rates')
                    ->where('is_active', 1)
                    ->groupBy('code');
            })
            ->get();
        view()->share('exchangeRate', $exchangeRates);
        view()->share('current', Current::whereIn('current_type_id', [1, 3])->where('is_active', 1)->get());
        view()->share('paymentType', PaymentType::get());
        view()->share('offerType', OfferType::get());
        view()->share('stocks', Stock::with(['product.category', 'variant', 'stockReservations','product.category.vatRate'])->where('is_active', 1)->get());
        parent::__construct();
    }
    public function save(Request $request, $unique = null)
    {
        try {
            $validator = Validator::make($request->all(), (new OfferRequest())->rules(), (new OfferRequest())->messages());
            if ($validator->fails()) {
                return $request->ajax()
                    ? response()->json(['success' => false, 'errors' => $validator->errors()], 422)
                    : redirect()->back()->withErrors($validator)->withInput();
            }
            $params = $request->except(['products', 'stockTable_length', 'stockTable_start', 'stockTable_search', 'stockTable_order', 'stockTable_draw']);
            $products = $request->has('products')
                ? (is_string($request->input('products'))
                    ? json_decode($request->input('products'), true) ?? []
                    : $request->input('products'))
                : [];

            if (!empty($products)) {
                $params['total_price'] = parseTrNumber($request->input('total_price', 0));
                $params['vat_amount'] = parseTrNumber($request->input('vat_amount', 0));
                $params['total_amount'] = parseTrNumber($request->input('total_amount', 0));
            }
            // Dövizli alt toplamlar sadece TRY harici döviz seçildiyse kaydedilsin
            $exchangeRate = ExchangeRate::find($request->input('exchange_rate_id'));
            $currencyCode = $exchangeRate ? $exchangeRate->code : 'TRY';
            if ($currencyCode !== 'TRY') {
                $params['total_price_fx'] = $request->input('total_price_fx');
                $params['vat_amount_fx'] = $request->input('vat_amount_fx');
                $params['total_amount_fx'] = $request->input('total_amount_fx');
            } else {
                $params['total_price_fx'] = null;
                $params['vat_amount_fx'] = null;
                $params['total_amount_fx'] = null;
            }
            if (!is_null($unique)) {
                $offer = Offer::find($unique);
                $offer->update($params);
            } else {
                $offer = Offer::create($params);
            }
            if ($offer && $offer->id) {
                OfferProduct::where('offer_id', $offer->id)->delete();
                if (!empty($products)) {
                    $offer->processOfferProducts($products);
                }
            }
            Cache::flush();
            return $this->handleResponse($request, true, 'Teklif başarılı şekilde kaydedildi', [], route("backend." . $this->page . "_list"));
        } catch (\Exception $e) {
            return $this->handleResponse($request, false, 'Teklif kaydedilirken bir hata oluştu: ' . $e->getMessage(), [], null);
        }
    }
    public function detail(Request $request, $unique = null)
    {
        try {
            $offer = Offer::with([
                // 'branch',
                'warehouse',
                'exchangeRate',
                'current',
                'paymentType',
                'offerType',
                'offerProducts.stock',
                'offerProducts.stock.product.unit',
                'offerProducts.stock.variant'
            ])->findOrFail($unique ?? $request->input('id'));


            $breadcrumb = [
                'Ayarlar' => '#',
                'Verilen Teklif' => route('backend.offer_given_list'),
                'Verilen Teklif Detay' => route('backend.offer_given_detail', ['unique' => $unique])
            ];
            view()->share('breadcrumb', $breadcrumb);

            return view('backend.offer_given.detail', [
                'container' => (object)[
                    'page' => $this->page,
                    'title' => 'Verilen Teklif'
                ],
                'title' => 'Verilen Teklif Detay',
                'subTitle' => 'Verilen Teklif Detay',
                'item' => $offer,
            ]);
        } catch (\Exception $e) {
            return redirect()->route('backend.offer_given_list')->with('error', 'Teklif detayı görüntülenirken bir hata oluştu: ' . $e->getMessage());
        }
    }
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);
            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');

            if ($item && $item->id) {
                $item->offerProducts = OfferProduct::where('offer_id', $item->id)
                    ->where('is_active', 1)
                    ->get();
                foreach ($item->offerProducts as $product) {
                    if (empty($product->item_no)) {
                        $product->item_no = OfferProduct::generateItemNo($item->offer_number);
                        $product->save();
                    }
                }
                if ($item->branch_id) {
                    view()->share('branchWarehouses', Warehouse::where('branch_id', $item->branch_id)->get());
                }
                if ($item->current_id) {
                    $currentList = Current::where('is_active', 1)->get();
                    $deletedCurrent = Current::withTrashed()->where('id', $item->current_id)->first();

                    if ($deletedCurrent && $deletedCurrent->deleted_at) {
                        $currentWithDeleted = $currentList->toBase();
                        $currentWithDeleted->push($deletedCurrent);
                        view()->share('current', $currentWithDeleted);
                    }
                }
            }
        } else {
            $item = new $this->model;
            $item->offer_number = Offer::generateOfferNumber(self::GIVEN_OFFER_TYPE_ID);
            $sampleItemNos = [];
            for ($i = 1; $i <= self::SAMPLE_ITEM_COUNT; $i++) { // 20 örnek item_no oluştur
                $sampleItemNos[] = $item->offer_number . '-' . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
            view()->share('sampleItemNos', $sampleItemNos);
        }
        // Mevcut ürünleri partial format'ına çevir
        $products = [];
        if (isset($item->offerProducts) && $item->offerProducts->count() > 0) {
            $products = $item->offerProducts->map(function ($product) {
                return (object) [
                    'stock_id' => $product->stock_id,
                    'item_no' => $product->item_no,
                    'product_code' => $product->stock->product->sku ?? '',
                    'product_name' => $product->stock->product->name . ($product->stock->variant ? ' / ' . $product->stock->variant->name : ''),
                    'unit' => $product->stock->product->unit->name ?? '',
                    'quantity' => $product->quantity,
                    'unit_price' => $product->unit_price,
                    'vat_status' => $product->vat_status,
                    'vat_rate' => $product->vat_rate,
                    'exchange_rate_id' => $product->exchange_rate_id,
                    'exchange_rate' => $product->exchange_rate,
                    'total_price' => $product->total_price,
                    'vat_amount' => $product->vat_amount,
                    'total_amount' => $product->total_amount,
                    'from_db' => true,
                ];
            })->toArray();
        }
        return view("backend.$this->page.form", compact('item', 'products'));
    }
    protected function datatableHook($datatable)
    {
        return $datatable
            ->addColumn('currency_type', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->currency_code;
                }
                return self::DEFAULT_CURRENCY; // Varsayılan değer
            })
            ->addColumn('currency_symbol', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->symbol;
                }
                return self::DEFAULT_CURRENCY_SYMBOL; // Varsayılan değer (Türk Lirası)
            })
            ->addColumn('product_statuses', function ($item) {
                $statuses = [];
                if ($item->offerProducts && $item->offerProducts->count() > 0) {
                    foreach ($item->offerProducts as $product) {
                        $statuses[] = [
                            'status' => $product->status,
                            'quantity' => $product->quantity
                        ];
                    }
                }
                return $statuses;
            })
            ->addColumn('current_info', function ($item) {
                if ($item->current) {
                    return [
                        'name' => $item->current->title,
                        'deleted' => $item->current->deleted_at ? true : false,
                    ];
                }

                return [
                    'name' => '-',
                    'deleted' => false,
                ];
            });
    }
    public function delete(Request $request)
    {
        $offer = $this->model::find((int)$request->post('id'));
        if (!is_null($offer)) {
            if ($offer->hasApprovedProducts()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Onaylanmış ürün içeren teklif silinemez'
                ]);
            }
            $offer->delete();
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı'
            ]);
        }
        Cache::flush();
        return response()->json(['status' => true]);
    }
    public function updateStatus(Request $request)
    {
        try {
            $productIds = $request->input('product_ids', []);
            $status = $request->input('status');
            $reasonForCancellation = $request->input('reason_for_cancellation');
            // Her zaman yeni sipariş oluşturulsun
            $forceNewOrder = ($status == self::APPROVED_STATUS) ? true : false;

            if (empty($productIds) || !isset($status)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ürün ID\'leri veya durum bilgisi eksik'
                ]);
            }

            // İptal durumu için validation
            if ($status == 2) {
                // Trim'lenmiş haliyle iptal sebebi kontrolü
                $trimmedReason = trim($reasonForCancellation);

                // Boşsa
                if (empty($trimmedReason)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'İptal sebebi zorunludur.'
                    ]);
                }

                // Uzunluk kontrolü
                $length = mb_strlen($trimmedReason);
                if ($length < 5) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Lütfen iptal sebebi en az 5 karakter olmalıdır.'
                    ]);
                }

                if ($length > 500) {
                    return response()->json([
                        'success' => false,
                        'message' => 'İptal sebebi 500 karakterden uzun olamaz.'
                    ]);
                }

            }

            DB::beginTransaction();
            $offerProducts = OfferProduct::with(['stock', 'offer'])->whereIn('id', $productIds)->get();

            // Validate status change
            if (!$this->canChangeStatus($offerProducts)) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Zaten onaylanmış ürünlerin durumunu değiştiremezsiniz.'
                ]);
            }

            // Update product statuses
            $productsByOffer = $this->updateProductStatuses($offerProducts, $status, $reasonForCancellation);

            $message = 'Ürün durumları başarıyla güncellendi';

            // If approved, convert to orders
            if ($status == self::APPROVED_STATUS) {
                $message = $this->processApprovedOffers($productsByOffer, $forceNewOrder); // forceNewOrder her zaman true
            }

            DB::commit();
            Cache::flush();

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Ürün durumları güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    private function canChangeStatus($offerProducts): bool
    {
        return $offerProducts->where('status', self::APPROVED_STATUS)->isEmpty();
    }

    private function updateProductStatuses($offerProducts, int $status, ?string $reasonForCancellation = null): array
    {
        $productsByOffer = [];

        foreach ($offerProducts as $offerProduct) {
            $offerProduct->status = $status;

            // Sadece iptal durumunda reason_for_cancellation kaydet
            if ($status == 2 && $reasonForCancellation) {
                $offerProduct->reason_for_cancellation = $reasonForCancellation;
            }

            $offerProduct->save();

            $offerId = $offerProduct->offer_id;
            $productsByOffer[$offerId] ??= [];
            $productsByOffer[$offerId][] = $offerProduct;
        }

        return $productsByOffer;
    }

    private function processApprovedOffers(array $productsByOffer, $forceNewOrder = false): string
    {
        $lastOrder = null;

        foreach ($productsByOffer as $offerId => $products) {
            $offer = $products[0]->offer;

            if (!$offer) {
                continue;
            }

            // Sipariş seçimi
            if ($forceNewOrder) {
                $order = $this->createNewOrder($offer);
            } else {
                $order = $this->findOrCreateOrder($offer);
            }            $lastOrder = $order;

            $this->createOrderProducts($order, $products);
            $this->updateOrderTotals($order);
        }

        return $this->getSuccessMessage($lastOrder);
    }
    private function createNewOrder($offer)
    {
        return Order::create([
            'current_id' => $offer->current_id,
            'branch_id' => $offer->branch_id,
            'warehouse_id' => $offer->warehouse_id,
            'order_type_id' => self::RECEIVED_ORDER_TYPE_ID,
            'order_number' => Order::generateOrderNumber(),
            'order_date' => now()->format('Y-m-d H:i:s'),
            'exchange_rate_id' => $offer->exchange_rate_id,
            'shipping_address' => $offer->shipping_address,
            'payment_type_id' => $offer->payment_type_id,
            'notes' => $offer->notes ?? '',
            'total_price' => 0,
            'vat_amount' => 0,
            'total_amount' => 0,
            'is_active' => 1,
        ]);
    }

    private function findOrCreateOrder($offer)
    {
        return Order::firstOrCreate(
            [
                // Where conditions
                'current_id' => $offer->current_id,
                'branch_id' => $offer->branch_id,
                'warehouse_id' => $offer->warehouse_id,
                'order_type_id' => self::RECEIVED_ORDER_TYPE_ID,
                'delivery_date' => null,
            ],
            [
                // Default values for new record
                'order_number' => Order::generateOrderNumber(),
                'order_date' => now()->format('Y-m-d H:i:s'),
                'exchange_rate_id' => $offer->exchange_rate_id,
                'shipping_address' => $offer->shipping_address,
                'payment_type_id' => $offer->payment_type_id,
                'notes' => $offer->notes ?? '',
                'total_price' => $offer->total_price,
                'vat_amount' => $offer->vat_amount,
                'total_amount' => $offer->total_amount,
                'total_price_fx' => $offer->total_price_fx ,
                'vat_amount_fx' => $offer->vat_amount_fx,
                'total_amount_fx' => $offer->total_amount_fx,
                'is_active' => 1,
            ]
        );


        // Kayıt zaten varsa (yeni değilse), FX alanlarını elle güncelle
        if (!$order->wasRecentlyCreated) {
            $order->update([
                'total_price_fx' => $offer->total_price_fx,
                'vat_amount_fx' => $offer->vat_amount_fx,
                'total_amount_fx' => $offer->total_amount_fx,
            ]);
        }

        return $order;
    }
    private function updateOrderTotals($order)
    {
        // Sipariş ürünlerinden teklif ID'sini bul
        $orderProduct = OrderProduct::where('order_id', $order->id)->first();
        
        if ($orderProduct) {
            // Teklif ürününden teklifi bul
            $offerProduct = OfferProduct::where('stock_id', $orderProduct->stock_id)->first();
            
            if ($offerProduct) {
                $offer = Offer::find($offerProduct->offer_id);
                
                if ($offer) {
                    $order->total_price = $offer->total_price ;
                    $order->vat_amount = $offer->vat_amount ;
                    $order->total_amount = $offer->total_amount ;
                    $order->total_price_fx = $offer->total_price_fx ?? null;
                    $order->vat_amount_fx = $offer->vat_amount_fx ?? null;
                    $order->total_amount_fx = $offer->total_amount_fx ?? null;
                    $order->save();
                }
            }
        }
    }

    private function createOrderProducts($order, array $products): void
    {
        foreach ($products as $offerProduct) {
            if ($offerProduct->status != self::APPROVED_STATUS) {
                continue;
            }

            $itemNo = OrderProduct::generateItemNo($order->order_number);

            OrderProduct::firstOrCreate(
                [
                    // Where conditions
                    'order_id' => $order->id,
                    'stock_id' => $offerProduct->stock_id,
                ],
                [
                    // Default values for new record
                    'item_no' => $itemNo,
                    'exchange_rate_id' => $offerProduct->exchange_rate_id,
                    'product_id' => $offerProduct->product_id,
                    'product_variant_id' => $offerProduct->product_variant_id,
                    'quantity' => $offerProduct->quantity,
                    'unit_price' => $offerProduct->unit_price,
                    'status' => 0,
                    'vat_status' => $offerProduct->vat_status,
                    'currency_type' => $offerProduct->currency_type,
                    'exchange_rate' => $offerProduct->exchange_rate,
                    'total_price' => $offerProduct->total_price,
                    'vat_rate' => $offerProduct->vat_rate,
                    'is_active' => 1,
                ]
            );
        }
    }

    private function getSuccessMessage($lastOrder): string
    {
        if ($lastOrder) {
            return "Teklifiniz onaylandı ve {$lastOrder->order_number} numaralı alınan siparişe dönüştürüldü.";
        }

        return 'Ürünler onaylandı fakat sipariş oluşturulamadı. Lütfen sistem yöneticisiyle iletişime geçin.';
    }

    public function exportExcel(Request $request, $unique = null)
    {
        $offer = Offer::with(['offerProducts', 'warehouse', 'exchangeRate', 'current.city', 'current.country'])
            ->findOrFail($unique ?? $request->input('id'));

        $export = new ExportOffer($offer->offerProducts, $offer);
        $spreadsheet = $export->export();

        $writer = new Xlsx($spreadsheet);
        $fileName = 'teklif_' . ($offer->offer_number ?? 'bilinmiyor') . '.xlsx';

        return response()->streamDownload(function () use ($writer) {
            $writer->save('php://output');
        }, $fileName, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }
    public function pdf(Request $request, $unique = null)
    {
        // $unique ile teklif bulma (mevcut mantığı koruyoruz)
        $offer = Offer::with(['offerProducts', 'warehouse', 'exchangeRate', 'current.city', 'current.country'])
            ->findOrFail($unique ?? $request->input('id')); // $unique null ise, Request'ten id al

        // Request'ten ek parametreleri al (örneğin, dil veya format seçenekleri)
        $language = $request->input('language', 'tr'); // Varsayılan olarak Türkçe
        $paperSize = $request->input('paper_size', 'a4'); // Varsayılan A4
        $orientation = $request->input('orientation', 'portrait'); // Varsayılan portre

        // ExportOffer sınıfına ek parametreler geçilebilir (örneğin, dil desteği için)
        $export = new ExportOffer($offer->offerProducts, $offer, ['language' => $language]);
        $html = $export->exportToHtml(); // HTML içeriğini al

        // PDF oluştururken Request'ten gelen parametreleri kullan
        $pdf = PDF::loadHTML($html)
            ->setPaper($paperSize, $orientation)
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'DejaVu Sans', // Türkçe karakter desteği
            ]);

        // Dosya adını dinamik olarak oluştur
        $fileName = 'teklif_' . ($offer->offer_number ?? 'bilinmiyor') . '.pdf';

        // Request'ten download isteği gelip gelmediğini kontrol et
        $attachment = $request->input('download', true); // Varsayılan olarak indirme

        return $pdf->stream($fileName, ['Attachment' => $attachment]);
    }

    public function calculateTotals(Request $request)
    {
        try {
            $productsData = $request->input('products', []);
            $mainExchangeRateId = $request->input('exchange_rate_id');

            // Gerekli tüm döviz kurlarını tek seferde çekelim
            $productExchangeRateIds = array_column($productsData, 'exchange_rate_id');
            $allRateIds = array_unique(array_merge([$mainExchangeRateId], $productExchangeRateIds));
            $exchangeRates = ExchangeRate::whereIn('id', $allRateIds)->get()->keyBy('id');

            // Ana faturanın döviz kurunu alalım
            $mainExchangeRate = $exchangeRates->get($mainExchangeRateId);
            $mainCurrencySymbol = $mainExchangeRate ? $mainExchangeRate->symbol : '₺';
            $mainSellingRate = $mainExchangeRate ? $mainExchangeRate->selling_rate : 1;

            $grandTotalNetTry = 0;
            $grandTotalVatTry = 0;
            $grandTotalWithVatTry = 0;
            $productRows = [];

            // Her ürün için hesaplama yap
            foreach ($productsData as $stockId => $product) {
                $quantity = (int)($product['quantity'] ?? 1);
                $unitPrice = (float)parseTrNumber($product['price'] ?? 0);
                $vatRate = (float)($product['vat_rate'] ?? 0);
                $vatStatus = (int)($product['vat_status'] ?? 0); // 0: Hariç, 1: Dahil
                $rowExchangeRateId = $product['exchange_rate_id'] ?? $mainExchangeRateId;

                // Ürün satırının döviz kurunu al
                $rowExchangeRate = $exchangeRates->get($rowExchangeRateId);
                $rowSellingRate = $rowExchangeRate ? $rowExchangeRate->selling_rate : 1;

                // Tutar ve KDV'yi ürünün kendi para biriminde hesapla
                $lineNet = $quantity * $unitPrice;
                $lineVat = 0;
                if ($vatStatus === 1) { // KDV Dahil
                    $lineVat = $lineNet - ($lineNet / (1 + ($vatRate / 100)));
                    $lineNet = $lineNet - $lineVat; // Net tutarı KDV'den arındır
                } else { // KDV Hariç
                    $lineVat = $lineNet * ($vatRate / 100);
                }
                $lineWithVat = $lineNet + $lineVat;

                // Her satırın tutarını TL'ye çevir
                $lineTotalTry = $lineWithVat * $rowSellingRate;

                // Genel toplamlara TL tutarları ekle
                $grandTotalNetTry += $lineNet * $rowSellingRate;
                $grandTotalVatTry += $lineVat * $rowSellingRate;
                $grandTotalWithVatTry += $lineTotalTry;

                // Frontend'deki satırları güncellemek için verileri hazırla
                $productRows[$stockId] = [
                    // Görünen tutar inputu için TL karşılığını gönder
                    'total_amount' => number_format($lineTotalTry, 2, ',', '.'),

                    // Kaydetmek için gizli inputlara orijinal dövizdeki tutarları gönder
                    'amount_raw' => $lineNet,
                    'vat_amount_raw' => $lineVat,
                    'total_amount_raw' => $lineWithVat,
                ];
            }

            // Genel toplamların ana faturanın para birimine göre karşılığını hesapla
            $grandTotalNetFx = $mainSellingRate > 0 ? $grandTotalNetTry / $mainSellingRate : 0;
            $grandTotalVatFx = $mainSellingRate > 0 ? $grandTotalVatTry / $mainSellingRate : 0;
            $grandTotalWithVatFx = $mainSellingRate > 0 ? $grandTotalWithVatTry / $mainSellingRate : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_price' => number_format($grandTotalNetTry, 2, ',', '.'),
                    'vat_amount' => number_format($grandTotalVatTry, 2, ',', '.'),
                    'total_amount' => number_format($grandTotalWithVatTry, 2, ',', '.'),
                    'total_price_fx' => number_format($grandTotalNetFx, 2, ',', '.'),
                    'vat_amount_fx' => number_format($grandTotalVatFx, 2, ',', '.'),
                    'total_amount_fx' => number_format($grandTotalWithVatFx, 2, ',', '.'),
                    'currency_symbol' => $mainCurrencySymbol,
                    'product_rows' => $productRows,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Hesaplama sırasında bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }
}
