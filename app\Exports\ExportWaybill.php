<?php

namespace App\Exports;


use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class ExportWaybill
{
    protected $items;
    protected $title;

    public function __construct($items, $title = 'İrsaliye Listesi')
    {
        $this->items = $items;
        $this->title = $title;
    }

    public function export(): Spreadsheet
    {

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($this->title);

        // Sayfa ayarları
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(0);

        $sheet->getPageMargins()
            ->setTop(0.5)->setRight(0.5)->setBottom(0.5)->setLeft(0.5);
        $sheet->getPageSetup()->setHorizontalCentered(true);

        // Başlıklar
        $headers = [
            'ID',
            'İrsaliye No',
            'Tarih',
            'Tutar',
            'Durum',
        ];

        // Otomatik sütun genişliği
        $lastCol = chr(ord('A') + count($headers) - 1);
        foreach (range('A', $lastCol) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Stil tanımları
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
        ];

        $valueStyle = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
        ];

        // Başlıkları yaz
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue("{$col}1", $header);
            $sheet->getStyle("{$col}1")->applyFromArray($headerStyle);
            $col++;
        }

        // Satırları yaz
        $rowNum = 2;
        foreach ($this->items as $item) {
            $sheet->setCellValueExplicit("A{$rowNum}", $item->id ?? '', DataType::TYPE_STRING);
            $sheet->setCellValueExplicit("B{$rowNum}", $item->waybill_no ?? '', DataType::TYPE_STRING);

            // Tarih formatı
            $formattedDate = '';
            if ($item->waybill_date) {
                try {
                    if (is_string($item->waybill_date)) {
                        $formattedDate = date('d.m.Y', strtotime($item->waybill_date));
                    } else {
                        $formattedDate = $item->waybill_date->format('d.m.Y');
                    }
                } catch (\Exception $e) {
                    $formattedDate = '';
                }
            }
            $sheet->setCellValueExplicit("C{$rowNum}", $formattedDate, DataType::TYPE_STRING);

            // Tutar formatı
            $formattedAmount = number_format($item->total_amount ?? 0, 2, ',', '.');
            $sheet->setCellValueExplicit("D{$rowNum}", $formattedAmount . ' ₺', DataType::TYPE_STRING);

            // Durum
            $status = $item->waybillStatu->name ?? '-';
            $sheet->setCellValueExplicit("E{$rowNum}", $status, DataType::TYPE_STRING);

            // Sadece A–E sütunlarına stil uygula
            foreach (range('A', 'E') as $c) {
                $sheet->getStyle("{$c}{$rowNum}")->applyFromArray($valueStyle);
            }

            $rowNum++;
        }

        return $spreadsheet;
    }
    public function exportWaybillToHtml(object $waybill): string
    {
        // Get current information from the linked invoice
        $current = $waybill->invoice->current ?? null;

        $address = '';
        if ($current && $current->address) $address .= $current->address;
        if ($current && $current->city?->name) $address .= ', ' . $current->city->name;
        if ($current && $current->country?->name) $address .= ', ' . $current->country->name;
        $address = trim($address, ', ') ?: '-';

        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <style>
                body { font-family: "DejaVu Sans", sans-serif; font-size: 12px; margin: 0; padding: 0; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #000; padding: 8px; text-align: left; vertical-align: top; }
                th { background-color: #4E4E4E; color: white; text-align: center; }
                .header { background-color: #F8F8F8; font-weight: bold; text-align: center; color: #333333; }
                .info-table td.label { text-align: right; background-color: #F8F8F8; font-weight: bold; color: #333333; width: 15%; }
                .info-table td { background-color: #FFFFFF; color: #333333; }
                .info-table td.address { word-wrap: break-word; word-break: break-word; max-width: 200px; line-height: 1.4; }
                .total-row { font-weight: bold; background-color: #F8F8F8; text-align: right; color: #333333; }
                .center { text-align: center; }
                .right { text-align: right; }
                .product-table { margin-top: 20px; }
            </style>
        </head>
        <body>
            <table>
                <tr>
                    <td colspan="4" class="header">İRSALİYE BİLGİLERİ</td>
                    <td colspan="5" class="header">CARİ BİLGİLERİ</td>
                </tr>
                <tr class="info-table">
                    <td class="label">İrsaliye No:</td>
                    <td colspan="3">' . htmlspecialchars($waybill->waybill_no ?? '-') . '</td>
                    <td class="label">Cari Adı:</td>
                    <td colspan="4">' . htmlspecialchars(($current->title ?? '-') . ' ' . ($current->short_description ?? '')) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">İrsaliye Tarihi:</td>
                    <td colspan="3">' . ($waybill->waybill_date ? $waybill->waybill_date->format('d.m.Y') : '-') . '</td>
                    <td class="label">Telefon:</td>
                    <td colspan="4">' . htmlspecialchars($current->phone ?? '-') . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Fatura No:</td>
                    <td colspan="3">' . htmlspecialchars($waybill->invoice->invoice_no ?? '-') . '</td>
                    <td class="label">Adres:</td>
                    <td colspan="4" class="address">' . htmlspecialchars($address) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Fatura Tipi:</td>
                    <td colspan="3">' . htmlspecialchars($waybill->invoice->invoiceType->name ?? '-') . '</td>
                    <td class="label">Durum:</td>
                    <td colspan="4">' . htmlspecialchars($waybill->waybillStatu->name ?? '-') . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Açıklama:</td>
                    <td colspan="8">' . htmlspecialchars($waybill->note ?? '-') . '</td>
                </tr>
            </table>
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                <tr>
                    <td colspan="7" style="background-color: #F8F8F8; font-weight: bold; text-align: center; color: #333333; padding: 8px; border: 1px solid #000;">İRSALİYE KALEMLERİ</td>
                </tr>
                <tr>
                    <th style="width: 8%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Sıra No</th>
                    <th style="width: 12%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Ürün Kodu</th>
                    <th style="width: 30%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Ürün Adı</th>
                    <th style="width: 10%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Miktar</th>
                    <th style="width: 15%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Birim Fiyat</th>
                    <th style="width: 10%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">KDV Oranı</th>
                    <th style="width: 15%; background-color: #4E4E4E; color: white; text-align: center; font-weight: bold; border: 1px solid #000; padding: 6px; font-size: 10px;">Toplam</th>
                </tr>';

        $i = 1;
        foreach ($waybill->waybillItems as $item) {
            // Ürün adı ve varyantı birleştir (model ilişkisiyle)
            $productName = $item->product ? $item->product->name : ($item->product_name ?? '-');
            if ($item->productVariant && $item->productVariant->name) {
                $productName .= ' / ' . $item->productVariant->name;
            }
            // Ürün kodu (SKU): önce varyant, yoksa ürün
            $productCode = '-';
            if ($item->productVariant && $item->productVariant->sku) {
                $productCode = $item->productVariant->sku;
            } elseif ($item->product && $item->product->sku) {
                $productCode = $item->product->sku;
            }

            $html .= '
                <tr>
                    <td style="width: 8%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . $i++ . '</td>
                    <td style="width: 12%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($productCode) . '</td>
                    <td style="width: 30%; text-align: left; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . htmlspecialchars($productName) . '</td>
                    <td style="width: 10%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . number_format((float) ($item->quantity ?? 0), 2, ',', '.') . '</td>
                    <td style="width: 15%; text-align: right; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . number_format((float) ($item->price ?? 0), 2, ',', '.') . ' TRY</td>
                    <td style="width: 10%; text-align: center; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">%' . number_format((float) ($item->vat_rate ?? 0), 2, ',', '.') . '</td>
                    <td style="width: 15%; text-align: right; border: 1px solid #000; padding: 6px; font-size: 10px; vertical-align: middle;">' . number_format((float) ($item->total ?? 0), 2, ',', '.') . ' TRY</td>
                </tr>';
        }

        $html .= '
                <tr>
                    <td colspan="5" style="border: 1px solid #000; padding: 6px;"></td>
                    <td style="border: 1px solid #000; padding: 6px; background-color: #4E4E4E; color: white; font-weight: bold; text-align: right; font-size: 10px;">Genel Toplam:</td>
                    <td style="border: 1px solid #000; padding: 6px; text-align: right; font-weight: bold; background-color: #F8F8F8; font-size: 10px;">' . number_format((float) ($waybill->total_amount ?? 0), 2, ',', '.') . ' TRY</td>
                </tr>
            </table>
        </body>
        </html>';

        return $html;
    }
}
