<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OfferTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                [
                    'name' => '<PERSON>ınan <PERSON>f',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Verilen Teklif',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
            ];

            DB::table('offer_types')->upsert(
                $types,
                ['name'],
                ['created_at', 'updated_at']
            );
        });

        $this->command->info('OfferType verileri başarıyla oluşturuldu.');
    }
}
