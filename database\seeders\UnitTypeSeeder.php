<?php

namespace Database\Seeders;

use App\Models\UnitType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UnitTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $unitTypes = [
                [
                    'name' => 'Ağırlık',
                    'code' => 'WEIGHT',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Uzunluk',
                    'code' => 'LENGTH',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Hacim',
                    'code' => 'VOLUME',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Sıvı',
                    'code' => 'LIQUID',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Alan',
                    'code' => 'AREA',
                    'is_active' => 1,
                ],
                [
                    'name' => 'Adet',
                    'code' => 'PIECE',
                    'is_active' => 1,
                ],
            ];

            foreach ($unitTypes as $unitType) {
                UnitType::updateOrCreate(
                    ['name' => $unitType['name']],
                    [
                        'name' => $unitType['name'],
                        'code' => $unitType['code'],
                        'is_active' => $unitType['is_active'],
                        'created_by' => 1,
                        'updated_by' => 1,
                    ]
                );
            }
        });

        $this->command->info('UnitType verileri başarıyla oluşturuldu.');
    }
}
