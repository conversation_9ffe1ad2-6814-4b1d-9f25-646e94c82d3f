@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = $location->name . ' - Detay';
@endphp

@section('content')
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Lokasyon Bilgileri</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.warehouse_location_form', $location->id) }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed">
                    Düzenle
                </a>
                <a href="{{ route('backend.warehouse_location_list') }}"
                    class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed">
                    Geri
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td width="30%"><strong>Lokasyon Kodu:</strong></td>
                            <td>{{ $location->code }}</td>
                        </tr>
                        <tr>
                            <td><strong>Lokasyon Adı:</strong></td>
                            <td>{{ $location->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Depo:</strong></td>
                            <td>{{ $location->warehouse->name }}</td>
                        </tr>
                        <tr>
                            <td width="30%"><strong>Üst Lokasyon:</strong></td>
                            <td>{{ $location->parent->name ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Alt Lokasyon Sayısı:</strong></td>
                            <td>{{ $location->children->count() }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table basic-table mb-0">
                        <tr>
                            <td><strong>Mevcut Ağırlık:</strong></td>
                            <td>{{ number_format($location->current_weight ?? 0, 2, ',', '.') }} kg</td>
                        </tr>
                        <tr>
                            <td><strong>Maksimum Ağırlık:</strong></td>
                            <td>{{ number_format($location->max_weight_capacity ?? 0, 2, ',', '.') }} kg</td>
                        </tr>
                        <tr>
                            <td><strong>Mevcut Hacim:</strong></td>
                            <td>{{ number_format($location->current_volume ?? 0, 2, ',', '.') }} m³</td>
                        </tr>
                        <tr>
                            <td><strong>Maksimum Hacim:</strong></td>
                            <td>{{ number_format($location->max_volume_capacity ?? 0, 2, ',', '.') }} m³</td>
                        </tr>
                        <tr>
                            <td><strong>Durum:</strong></td>
                            <td>
                                @if($location->is_active)
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>
                                @else
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @if($location->stocks->count() > 0)
    <div class="card basic-data-table mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Lokasyon Stok Durumu</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="stocksTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Ürün</th>
                            <th scope="col">Varyant</th>
                            <th scope="col" class="text-center">Miktar</th>
                            <th scope="col" class="text-center">Birim</th>
                            <th scope="col" class="text-center">Son İşlem</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
    @if($location->children->count() > 0)
        <div class="card basic-data-table">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Alt Lokasyonlar</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table datatable class="table bordered-table mb-0" id="childLocationsTable" data-page-length='10'>
                        <thead>
                            <tr>
                                <th scope="col">Lokasyon Kodu</th>
                                <th scope="col">Lokasyon Adı</th>
                                <th scope="col" class="text-center">Stok Sayısı</th>
                                <th scope="col" class="text-center">Durum</th>
                                <th scope="col" class="text-center">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "#stocksTable";
            var stocksTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.warehouse_location_detail', $location->id) }}?datatable=stocks",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'product.name',
                        name: 'product.name',
                        defaultContent: '-'
                    },
                    {
                        data: 'variant.name',
                        name: 'variant.name',
                        defaultContent: '-'
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center'
                    },
                    {
                        data: 'product.unit.name',
                        name: 'product.unit.name',
                        className: 'text-center',
                        defaultContent: '-'
                    },
                    {
                        data: 'updated_at',
                        name: 'updated_at',
                        className: 'text-center'
                    }
                ],
                order: [[4, 'desc']],
                pageLength: 10
            });

            @if($location->children->count() > 0)
            BaseCRUD.selector = "#childLocationsTable";
            var childLocationsTable = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.warehouse_location_detail', $location->id) }}?datatable=children",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [
                    {
                        data: 'code',
                        name: 'code'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'stocks_count',
                        name: 'stocks_count',
                        className: 'text-center',
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return `
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.warehouse_location_detail', '') }}/${row.id}" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:eye" class="menu-icon"></iconify-icon>
                                </a>
                            </div>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [[0, 'desc']],
                pageLength: 10
            });
            @endif
        });
    </script>
@endsection
