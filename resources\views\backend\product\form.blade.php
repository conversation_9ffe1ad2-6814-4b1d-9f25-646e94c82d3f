@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">Ür<PERSON>n <PERSON> <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="lucide:package"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Ürün adını giriniz"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Ürün Tipi <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:category"></iconify-icon>
                                    </span>
                                    <select class="form-select select2" name="product_type_id" id="product_type_id">
                                        <option value="">Ürün tipi seçiniz</option>
                                        @foreach ($productTypes as $productType)
                                            <option
                                                {{ (old('product_type_id') ?? $item->product_type_id) == $productType->id ? 'selected' : '' }}
                                                value="{{ $productType->id }}">{{ $productType->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="product_type_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Kategori <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:category"></iconify-icon>
                                    </span>
                                    <select class="form-select select2" name="category_id" id="category_id">
                                        <option value="">Kategori seçiniz</option>
                                        @foreach ($categories as $category)
                                            <option
                                                {{ (old('category_id') ?? $item->category_id) == $category->id ? 'selected' : '' }}
                                                value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="category_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Marka</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="tabler:brand-apple"></iconify-icon>
                                    </span>
                                    <select class="form-select select2" name="brand_id" id="brand_id">
                                        <option value="">Marka seçiniz</option>
                                        @foreach ($brands as $brand)
                                            <option
                                                {{ (old('brand_id') ?? $item->brand_id) == $brand->id ? 'selected' : '' }}
                                                value="{{ $brand->id }}">{{ $brand->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="brand_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Ürün Kodu (SKU)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:code"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('sku') ?? ($item->sku ?? '') }}" name="sku"
                                        placeholder="Otomatik oluşturulur (opsiyonel)">
                                    <x-form-error field="sku" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Barkod</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mdi:barcode"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('barcode') ?? ($item->barcode ?? '') }}" name="barcode"
                                        placeholder="Barkod numarası">
                                    <x-form-error field="barcode" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="fluent:text-description-24-regular"></iconify-icon>
                                    </span>
                                    <textarea class="form-control" name="description" rows="3" placeholder="Ürün açıklaması">{{ old('description') ?? ($item->description ?? '') }}</textarea>
                                    <x-form-error field="description" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Ağırlık (kg)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="tabler:weight"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.001" min="0"
                                        value="{{ old('weight') ?? ($item->weight ?? '') }}" name="weight"
                                        placeholder="0.000">
                                    <x-form-error field="weight" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Genişlik (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:arrows-horizontal"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('width') ?? ($item->width ?? '') }}" name="width"
                                        placeholder="0.00">
                                    <x-form-error field="width" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Yükseklik (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:arrows-vertical"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('height') ?? ($item->height ?? '') }}" name="height"
                                        placeholder="0.00">
                                    <x-form-error field="height" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Uzunluk (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:ruler"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('length') ?? ($item->length ?? '') }}" name="length"
                                        placeholder="0.00">
                                    <x-form-error field="length" />
                                </div>
                            </div>

                            <div class="col-12 col-md-3">
                                <label class="form-label">Alış Fiyatı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:dollar-minimalistic-linear"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('purchase_price') ?? ($item->purchase_price ?? '') }}"
                                        name="purchase_price" placeholder="0.00">
                                    <x-form-error field="purchase_price" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Alış Para Birimi</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:currency"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="purchase_currency_code">
                                        <option value="">Para birimi seçiniz</option>
                                        @foreach ($currencies as $currency)
                                            <option
                                                {{ (old('purchase_currency_code') ?? ($item->purchase_currency_code ?? 'TRY')) == $currency->code ? 'selected' : '' }}
                                                value="{{ $currency->code }}">{{ $currency->code }} -
                                                {{ $currency->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="purchase_currency_code" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Satış Fiyatı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:dollar-minimalistic-linear"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('sale_price') ?? ($item->sale_price ?? '') }}" name="sale_price"
                                        placeholder="0.00">
                                    <x-form-error field="sale_price" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Satış Para Birimi</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:currency"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="sale_currency_code">
                                        <option value="">Para birimi seçiniz</option>
                                        @foreach ($currencies as $currency)
                                            <option
                                                {{ (old('sale_currency_code') ?? ($item->sale_currency_code ?? 'TRY')) == $currency->code ? 'selected' : '' }}
                                                value="{{ $currency->code }}">{{ $currency->code }} -
                                                {{ $currency->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="sale_currency_code" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Kritik Stok Seviyesi <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="material-symbols:warning-outline"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" min="0"
                                        value="{{ old('critical_stock_level') ?? ($item->critical_stock_level ?? 0) }}"
                                        name="critical_stock_level" placeholder="0">
                                    <x-form-error field="critical_stock_level" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Birim Tipi <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="tabler:ruler-measure"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="unit_type_id" id="unit_type_id">
                                        <option value="">Birim tipi seçiniz</option>
                                        @foreach ($unitTypes as $unitType)
                                            <option
                                                {{ (old('unit_type_id') ?? $item->unit_type_id) == $unitType->id ? 'selected' : '' }}
                                                value="{{ $unitType->id }}" data-units='@json($unitType->units)'>
                                                {{ $unitType->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="unit_type_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Birim <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="tabler:ruler-3"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="unit_id" id="unit_id">
                                        <option value="">Önce birim tipi seçiniz</option>
                                        @if ($item->unit_id)
                                            @foreach ($units->where('unit_type_id', $item->unit_type_id) as $unit)
                                                <option
                                                    {{ (old('unit_id') ?? $item->unit_id) == $unit->id ? 'selected' : '' }}
                                                    value="{{ $unit->id }}">{{ $unit->name }}
                                                    ({{ $unit->symbol }})
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    <x-form-error field="unit_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:status-change"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">Kaydet</button>
                                <a href="{{ route('backend.' . $container->page . '_list') }}"
                                    class="btn btn-secondary">İptal</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            $('#unit_type_id').change(function() {
                var selectedOption = $(this).find('option:selected');
                var units = selectedOption.data('units');
                var unitSelect = $('#unit_id');
                var currentUnitId = unitSelect.data('current-unit-id') || '';

                unitSelect.empty();
                unitSelect.append('<option value="">Birim seçiniz</option>');

                if (units && units.length > 0) {
                    $.each(units, function(index, unit) {
                        var selected = (currentUnitId == unit.id) ? 'selected' : '';
                        unitSelect.append('<option value="' + unit.id + '" ' + selected + '>' + unit
                            .name + ' (' + unit.symbol + ')</option>');
                    });
                } else {
                    unitSelect.append('<option value="">Bu birim tipinde birim bulunamadı</option>');
                }
            });

            var currentUnitId = '{{ old('unit_id') ?? ($item->unit_id ?? '') }}';
            if (currentUnitId) {
                $('#unit_id').data('current-unit-id', currentUnitId);
            }

            if ($('#unit_type_id').val()) {
                $('#unit_type_id').trigger('change');
            }


        });
    </script>
@endsection
