<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportAccountVoucher;
use App\Http\Controllers\Backend\BaseController;
use App\Models\AccountVoucher;
use App\Models\Balance;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\PaymentType;
use App\Models\VoucherType;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class AccountVouchersController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Hesap Fişleri';
        $this->page = 'account_vouchers';
        $this->model = new AccountVoucher();
        $this->relation = ['current', 'voucherType', 'paymentType'];

        $this->view = (object)[
            'breadcrumb' => [
                'Cari Hesap Fişleri' => route('backend.account_vouchers_list'),
            ],
        ];
        $this->validation = array(
            [
                'voucher_date' => 'required|date',
                'current_id' => 'required|exists:currents,id',
                'payment_type_id'  => 'required|integer|exists:payment_types,id',
                'amount' => 'required|numeric|min:0.01',
                'exchange_rate_id' => 'required|integer|exists:exchange_rates,id',
                'description' => 'nullable|string',
                'reference_no' => 'nullable|string',
                'due_date' => 'nullable|date',
                'bank_name' => 'nullable|string',
                'bank_branch' => 'nullable|string',
                'account_no' => 'nullable|string',
                'document_file' => 'nullable|file|max:10240',
            ],
            [
                'exchange_rate_id.required' => 'Para birimi seçilmelidir.',
                'exchange_rate_id.exists' => 'Geçersiz para birimi.',
                'voucher_date.required' => 'Fiş tarihi alanı zorunludur.',
                'voucher_date.date' => 'Fiş tarihi geçerli bir tarih olmalıdır.',
                'current_id.required' => 'Cari hesap seçilmelidir.',
                'current_id.exists' => 'Geçersiz cari hesap.',
                'payment_type_id.required' => 'Lütfen fiş türünü seçiniz',
                'amount.required' => 'Tutar alanı zorunludur.',
                'amount.numeric' => 'Tutar sayısal bir değer olmalıdır.',
                'amount.min' => 'Tutar en az 0.01 olmalıdır.',
                'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
                'document_file.file' => 'Belge dosyası geçerli bir dosya olmalıdır.',
                'document_file.max' => 'Belge dosyası en fazla 10MB olabilir.',
            ]
        );
        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('paymentType', PaymentType::get());
        view()->share('voucherTypes', VoucherType::where('is_active', 1)->get());
        view()->share('exchangeRates', ExchangeRate::all());

        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->addColumn('current_name', function ($row) {
                return $row->current ? $row->current->title : '';
            })
            ->addColumn('voucher_date', function ($row) {
                return $row->voucher_date ? $row->voucher_date->format('d.m.Y') : '';
            })
            ->addColumn('paymentType', function ($row) {
                return $row->paymentType->name;
            })
            ->addColumn('amount_formatted', function ($row) {
                $currency = $row->currency === 'TRY' ? '₺' : $row->currency;
                return number_format($row->amount, 2, ',', '.') . ' ' . $currency;
            })
            ->addColumn('is_active', function ($row) {
                return $row->is_active == 1
                    ? '<span class="badge" style="background-color: #BBF7D0; color: #166534;">Aktif</span>'
                    : '<span class="badge" style="background-color: #FECACA; color: #991B1B;">Pasif</span>';
            })
            ->rawColumns(['is_active']);
    }

    public function detail(Request $request, $unique = null)
    {
        $item = $this->model::with($this->relation)->find((int)$unique);
        
        if (!$item) {
            return redirect()->route('backend.' . $this->page . '_list')->with('error', 'Cari hesap fişi bulunamadı');
        }

        return view("backend.$this->page.detail", compact('item'));
    }

    public function exportExcel(Request $request, $unique = null)
    {
        $items = AccountVoucher::with('current')->get(); // Filtreleme ekleyebilirsin
        $exporter = new ExportAccountVoucher($items);
        $spreadsheet = $exporter->export();

        // Excel çıktısı için response döndür
        $writer = new Xlsx($spreadsheet);
        $filename = 'fis_listesi.xlsx';

        // Response ile dosya indirme
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        $writer->save('php://output');
        exit;
    }
    public function saveHook(Request $request)
    {
        $params = $request->all();

        $newAmount = (float) $request->input('amount');
        $newCurrentId = (int) $request->input('current_id');
        $newExchangeRateId = (int) $request->input('exchange_rate_id');
        $voucherId = $request->route('unique');

        $newExchangeRate = ExchangeRate::find($newExchangeRateId);
        $newCurrencyCode = $newExchangeRate ? $newExchangeRate->code : 'TRY';

        // GÜNCELLEME İSE: ESKİYİ GERİ AL
        if ($voucherId) {
            $existingVoucher = AccountVoucher::find($voucherId);
            if ($existingVoucher) {
                $oldAmount = (float) $existingVoucher->amount;
                $oldCurrentId = (int) $existingVoucher->current_id;
                $oldExchangeRateId = (int) $existingVoucher->exchange_rate_id;

                $oldExchangeRate = ExchangeRate::find($oldExchangeRateId);
                $oldCurrencyCode = $oldExchangeRate ? $oldExchangeRate->code : 'TRY';

                // Sadece eski para birimi TRY ise balances'tan düş
                if ($oldCurrencyCode === 'TRY') {
                    $oldBalance = Balance::where('current_id', $oldCurrentId)->first();
                    if ($oldBalance) {
                        $oldBalance->credit_balance -= $oldAmount;
                        $oldBalance->save();
                    }
                }

                // Her durumda balance_currencies'ten düş
                $oldBalance = Balance::where('current_id', $oldCurrentId)->first();
                if ($oldBalance) {
                    $oldBalanceCurrency = $oldBalance->balanceCurrencies()
                        ->where('currency_code', $oldCurrencyCode)
                        ->first();

                    if ($oldBalanceCurrency) {
                        $oldBalanceCurrency->credit_balance -= $oldAmount;
                        $oldBalanceCurrency->save();
                    }
                }
            }
        }

        // YENİ KAYIT: SADECE TRY ise balances'a ekle
        if ($newCurrencyCode === 'TRY') {
            $newBalance = Balance::where('current_id', $newCurrentId)->first();
            if ($newBalance) {
                $newBalance->credit_balance += $newAmount;
                $newBalance->save();
            } else {
                $newBalance = Balance::create([
                    'current_id' => $newCurrentId,
                    'credit_balance' => $newAmount,
                    'debit_balance' => 0,
                ]);
            }
        } else {
            // Eğer yeni para birimi TRY değilse, balances tablosuna hiç dokunma
            $newBalance = Balance::where('current_id', $newCurrentId)->first();
            if (!$newBalance) {
                $newBalance = Balance::create([
                    'current_id' => $newCurrentId,
                    'credit_balance' => 0,
                    'debit_balance' => 0,
                ]);
            }
        }

        // Her durumda balance_currencies işlemleri
        $balanceCurrency = $newBalance->balanceCurrencies()
            ->where('currency_code', $newCurrencyCode)
            ->first();

        if ($balanceCurrency) {
            $balanceCurrency->credit_balance += $newAmount;
            $balanceCurrency->save();
        } else {
            $newBalance->balanceCurrencies()->create([
                'currency_code' => $newCurrencyCode,
                'debit_balance' => 0,
                'credit_balance' => $newAmount,
                'current_id' => $newCurrentId,
            ]);
        }

        return $params;
    }
}
