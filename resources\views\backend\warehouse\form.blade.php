@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? $item->name . ' Düzenle' : 'Yeni ' . $container->title . ' Ekle';
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', $item->id) }}" method="POST">
                @csrf

                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Depo <PERSON> <span class="text-danger">*</span></label>
                        <input type="text" name="code" class="form-control" value="{{ old('code', $item->code) }}"
                            required>
                        @error('code')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Depo Adı <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" value="{{ old('name', $item->name) }}"
                            required>
                        @error('name')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Durum</label>
                        <div class="icon-field">
                            <span class="icon">
                                <iconify-icon icon="carbon:badge"></iconify-icon>
                            </span>
                            <select class="form-control form-select" name="is_active">
                                <option value="1" {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>
                                    Aktif
                                </option>
                                <option value="0" {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>
                                    Pasif
                                </option>
                            </select>
                            <x-form-error field="is_active" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Telefon</label>
                        <input type="text" name="phone" class="form-control" value="{{ old('phone', $item->phone) }}">
                        @error('phone')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">E-posta</label>
                        <input type="email" name="email" class="form-control" value="{{ old('email', $item->email) }}">
                        @error('email')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label class="form-label">Adres</label>
                        <textarea name="address" class="form-control" rows="3">{{ old('address', $item->address) }}</textarea>
                        @error('address')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label class="form-label">Açıklama</label>
                        <textarea name="description" class="form-control" rows="3">{{ old('description', $item->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Maks. Ağırlık Kapasitesi (kg)</label>
                        <input type="number" step="0.001" name="max_weight_capacity" class="form-control"
                            value="{{ old('max_weight_capacity', $item->max_weight_capacity) }}" min="0">
                        @error('max_weight_capacity')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Maks. Hacim Kapasitesi (m³)</label>
                        <input type="number" step="0.0001" name="max_volume_capacity" class="form-control"
                            value="{{ old('max_volume_capacity', $item->max_volume_capacity) }}" min="0">
                        @error('max_volume_capacity')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Min. Sıcaklık (°C)</label>
                        <input type="number" step="0.01" name="temperature_min" class="form-control"
                            value="{{ old('temperature_min', $item->temperature_min) }}">
                        @error('temperature_min')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Maks. Sıcaklık (°C)</label>
                        <input type="number" step="0.01" name="temperature_max" class="form-control"
                            value="{{ old('temperature_max', $item->temperature_max) }}">
                        @error('temperature_max')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                    <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">İptal</a>
                </div>
            </form>
        </div>
    </div>
@endsection
