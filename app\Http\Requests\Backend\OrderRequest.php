<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class OrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'branch_id'         => 'required|integer|exists:branches,id',
            'warehouse_id'      => 'required|integer|exists:warehouses,id',
            'order_date'        => 'required|date|after_or_equal:today',
            'delivery_date' => 'nullable|date|after_or_equal:order_date',
            'exchange_rate_id'  => 'required|integer|exists:exchange_rates,id',
            'current_id'        => 'required|integer|exists:currents,id',
            'shipping_address' => 'nullable|string|max:255',
            'payment_type_id'  => 'required|integer|exists:payment_types,id',
            'order_type_id'     => 'nullable|integer|exists:order_types,id',
            'notes'             => 'nullable|string',
            'products'          => 'required|array|min:1',
        ];
    }
    public function messages(): array
    {
        return [
            // 'branch_id.required'        => 'Lütfen şube seçiniz',
            'warehouse_id.required'     => 'Lütfen depo seçiniz',
            'order_date.required'       => 'Lütfen sipariş tarihi giriniz',
            'order_date.date'           => 'Lütfen geçerli bir tarih giriniz',
            'order_date.after_or_equal' => 'Sipariş tarihi bugünden sonra olmalıdır',
            'delivery_date.date' => 'Lütfen geçerli bir tarih giriniz',
            'delivery_date.after_or_equal' => 'Teslimat tarihi sipariş tarihinden sonra olmalıdır',
            'exchange_rate_id.required' => 'Lütfen döviz seçiniz',
            'current_id.required'       => 'Lütfen cari seçiniz',
            'shipping_address.max'      => 'Sevkiyat adresi maksimum 255 karakter olmalıdır',
            'payment_type_id.required' => 'Lütfen ödeme planı seçiniz',
            'notes.string'              => 'Notlar metin formatında olmalıdır',
            'products.required'         => 'Lütfen en az bir ürün ekleyiniz.',
            'products.array'            => 'Ürünler hatalı gönderildi.',
            'products.min'              => 'Lütfen en az bir ürün ekleyiniz.',

        ];
    }
}
