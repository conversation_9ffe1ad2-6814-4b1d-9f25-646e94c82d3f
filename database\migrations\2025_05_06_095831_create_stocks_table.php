<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stocks', function (Blueprint $table) {
            $table->id();
            $table->integer('product_id')->nullable()->comment('Ürün ID');
            $table->integer('variant_id')->nullable()->comment('Varyant ID');
            $table->integer('warehouse_id')->comment('Depo ID');
            $table->integer('warehouse_location_id')->nullable()->comment('Depo lokasyon ID');
            $table->integer('stock_batch_id')->nullable()->comment('Stok parti ID');
            $table->decimal('quantity', 15, 2)->default(0)->comment('Mevcut miktar');
            $table->decimal('unit_cost', 15, 2)->default(0)->comment('Birim maliyet');
            $table->decimal('total_cost', 15, 2)->default(0)->comment('Toplam maliyet');
            $table->string('currency_code', 3)->nullable()->comment('Para birimi');
            $table->longText('notes')->nullable()->comment('Notlar');
            $table->tinyInteger('is_active')->default(1)->comment('Aktif mi?');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stocks');
    }
};
