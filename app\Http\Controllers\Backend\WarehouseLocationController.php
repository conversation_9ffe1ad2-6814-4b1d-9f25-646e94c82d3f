<?php

namespace App\Http\Controllers\Backend;

use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;

class WarehouseLocationController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Depo Lokasyonları';
        $this->page = 'warehouse_location';
        $this->model = new WarehouseLocation();
        $this->relation = ['warehouse', 'parent'];
        $this->listQuery = WarehouseLocation::select('warehouse_locations.*');

        $this->view = (object)array(
            'breadcrumb' => array(
                'Depo Lokasyonları' => route('backend.warehouse_location_list'),
            ),
        );

        $this->validation = array(
            [
                'warehouse_id' => 'required|exists:warehouses,id',
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:50',
                'description' => 'nullable|string|max:255',
                'parent_id' => 'nullable|exists:warehouse_locations,id',
                'max_weight_capacity' => 'nullable|numeric|min:0',
                'max_volume_capacity' => 'nullable|numeric|min:0',
                'current_weight' => 'nullable|numeric|min:0',
                'current_volume' => 'nullable|numeric|min:0',
                'is_active' => 'required|boolean',
            ],
            [
                'warehouse_id.required' => 'Depo seçilmedi.',
                'warehouse_id.exists' => 'Depo mevcut değil.',
                'name.required' => 'Lokasyon adı girilmedi.',
                'name.string' => 'Lokasyon adı geçersiz.',
                'name.max' => 'Lokasyon adı en fazla 255 karakter olabilir.',
                'code.required' => 'Kod girilmedi.',
                'code.string' => 'Kod metin formatında olmalıdır.',
                'code.max' => 'Kod en fazla 50 karakter olabilir.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
                'description.max' => 'Açıklama en fazla 255 karakter olabilir.',
                'parent_id.exists' => 'Üst lokasyon mevcut değil.',
                'max_weight_capacity.numeric' => 'Maksimum ağırlık kapasitesi geçersiz.',
                'max_volume_capacity.numeric' => 'Maksimum hacim kapasitesi geçersiz.',
                'current_weight.numeric' => 'Mevcut ağırlık geçersiz.',
                'current_volume.numeric' => 'Mevcut hacim geçersiz.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu geçersiz.',
            ],
        );

        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->with('warehouse')->get());
        parent::__construct();
    }

    public function detail(Request $request, $warehouse_location_id = null, $unique = null)
    {
        $location = WarehouseLocation::with(['warehouse', 'stocks.product.unit', 'stocks.variant', 'parent', 'children'])->find($warehouse_location_id);

        if (!$location) {
            return redirect()->route('backend.warehouse_location_list')->with('error', 'Depo lokasyonu bulunamadı');
        }

        if ($request->has('datatable')) {
            $type = $request->get('datatable');

            if ($type == 'stocks') {
                $query = $location->stocks()
                    ->select('stocks.*')
                    ->with(['product.unit', 'variant']);
                return app('datatables')->of($query)
                    ->editColumn('quantity', function ($item) {
                        $unit = $item->product ? $item->product->unit : null;
                        return formatQuantityWithUnit($item->quantity, $unit);
                    })
                    ->addIndexColumn()
                    ->editColumn('updated_at', function ($row) {
                        return $row->updated_at ? $row->updated_at->format('d.m.Y H:i') : '-';
                    })
                    ->rawColumns([])
                    ->make(true);
            }

            if ($type == 'children') {
                $query = $location->children()->withCount('stocks');
                return app('datatables')->of($query)
                    ->addIndexColumn()
                    ->editColumn('is_active', function ($row) {
                        return $row->is_active ? 'Aktif' : 'Pasif';
                    })
                    ->rawColumns([])
                    ->make(true);
            }
        }

        return view("backend.$this->page.detail", compact('location'));
    }
}
