<?php

if (!function_exists('formatMoney')) {
    /**
     * Para formatla - ayara göre ondalık basamak sayısı
     */
    function formatMoney($amount, $currency = 'TL')
    {
        return \App\Models\Setting::formatMoney($amount, $currency);
    }
}

if (!function_exists('formatMoneyNumber')) {
    /**
     * Para formatla (sadece sayı) - ayara göre ondalık basamak sayısı
     */
    function formatMoneyNumber($amount)
    {
        return \App\Models\Setting::formatMoneyNumber($amount);
    }
}

if (!function_exists('getDecimalPlaces')) {
    /**
     * Sistem ayarından ondalık basamak sayısını getir
     */
    function getDecimalPlaces()
    {
        return \App\Models\Setting::getDecimalPlaces();
    }
}
