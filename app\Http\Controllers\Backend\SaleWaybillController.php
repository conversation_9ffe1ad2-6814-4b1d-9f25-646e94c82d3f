<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportWaybill;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\Waybill;
use App\Models\WaybillItem;
use App\Models\WaybillStatu;
use App\Models\WaybillType;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request; // Corrected use statement
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class SaleWaybillController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Satış İrsaliyesi';
        $this->page = 'sale_waybill';
        $this->model = new Waybill();
        $this->relation = ['current', 'invoice', 'waybillItems', 'waybillType', 'waybillStatu'];
        $this->listQuery = Waybill::filter(request())->where('waybill_type', 2);

        $this->view = (object)[
            'breadcrumb' => [
                'Satış İrsaliyesi' => route('backend.sale_waybill_list'),
            ],
        ];
        $this->validation = [
            [
                'waybill_date' => 'required|date',
                'invoice_id' => 'required|integer',
                'note' => 'nullable|string',
                'total_amount' => 'required|numeric|min:0',
                'status' => 'required|integer',
                'reason_for_cancellation' => 'nullable|string|min:5|max:500',
            ],
            [
                'waybill_date.required' => 'İrsaliye tarihi zorunludur.',
                'waybill_date.date' => 'Geçerli bir tarih giriniz.',
                'invoice_id.required' => 'Fatura seçimi zorunludur.',
                'invoice_id.integer' => 'Geçersiz fatura seçimi.',
                'note.string' => 'Not alanı metin olmalıdır.',
                'total_amount.required' => 'Toplam tutar zorunludur.',
                'total_amount.numeric' => 'Toplam tutar sayısal olmalıdır.',
                'total_amount.min' => 'Toplam tutar sıfır veya daha büyük olmalıdır.',
                'status.required' => 'Durum seçimi zorunludur.',
                'status.integer' => 'Geçersiz durum seçimi.',
                'reason_for_cancellation.string' => 'İptal sebebi metin olmalıdır.',
                'reason_for_cancellation.min' => 'İptal sebebi en az 5 karakter olmalıdır.',
                'reason_for_cancellation.max' => 'İptal sebebi en fazla 500 karakter olabilir.',
            ]
        ];

        view()->share('current', Current::where('is_active', 1)->get());
        $invoices = Invoice::with(['invoiceItems.product', 'invoiceItems.productVariant'])
            ->where('invoice_type_id', 2)
            ->where('is_active', 1)
            ->whereIn('invoice_status_id', [2, 4, 5])
            ->get();

        foreach ($invoices as $inv) {
            $inv->items_for_js = $inv->invoiceItems->map(function($item) {
                return [
                    "stock_id" => $item->stock_id,
                    "product_id" => $item->product_id,
                    "product_variant_id" => $item->product_variant_id,
                    "product_name" => $item->product ? $item->product->name : '',
                    "product_variant_name" => $item->productVariant ? $item->productVariant->name : '',
                    "quantity" => $item->quantity,
                    "unit_price" => $item->unit_price,
                    "vat_rate" => $item->vat_rate,
                    "vat_amount" => $item->vat_amount,
                    "total" => $item->total_price,
                ];
            });
        }
        view()->share('invoice', $invoices);
        view()->share('waybillItems', WaybillItem::where('is_active', 1)->get());
        view()->share('waybillType', WaybillType::where('is_active', 1)->get());
        view()->share('waybillStatu', WaybillStatu::where('is_active', 1)->get());
        parent::__construct();
    }

    public function saveHook(Request $request)
    {
        $params = $request->except('items', 'status');
        if (is_null($request->waybill_no)) {
            $params['waybill_no'] = Waybill::generatePreviewSaleWaybillNumber();
        }
        $params['waybill_type'] = 2;
        $params['waybill_statu_id'] = $request->input('status');
        
        // İptal durumu seçilmişse reason_for_cancellation zorunlu
        if ($request->input('status') == 3) { // İptal Edildi durumu
            if (empty($request->input('reason_for_cancellation'))) {
                throw new ValidationException(
                    validator([], []),
                    response()->json(['message' => 'İptal durumu seçildiğinde iptal sebebi zorunludur.'], 422)
                );
            }
            $params['reason_for_cancellation'] = $request->input('reason_for_cancellation');
        } else {
            // İptal durumu değilse reason_for_cancellation'ı temizle
            $params['reason_for_cancellation'] = null;
        }
        
        return $params;
    }

    public function saveBack($obj)
    {
        if (!$obj || !isset($obj->id)) {
            return redirect()->back()->withInput()->withErrors(['error' => 'İrsaliye kaydedilemedi.']);
        }
        $items = request('items');

        DB::beginTransaction();
        try {
            // Önce eski kalemleri sil (güncelleme durumunda)
            WaybillItem::where('waybill_id', $obj->id)->delete();

            if (is_array($items) && count($items) > 0) {
                foreach ($items as $item) {
                    // Zorunlu alanlar kontrolü
                    if (!isset($item['stock_id'], $item['product_id'], $item['quantity'], $item['price'], $item['vat_rate'], $item['vat_amount'], $item['total'])) {
                        throw new \Exception('Eksik kalem verisi');
                    }
                    $waybillItemData = [
                        'waybill_id' => $obj->id,
                        'stock_id' => $item['stock_id'],
                        'product_id' => $item['product_id'],
                        'product_variant_id' => $item['product_variant_id'] ?? null,
                        'quantity' => $item['quantity'],
                        'price' => $item['price'],
                        'vat_rate' => $item['vat_rate'],
                        'vat_amount' => $item['vat_amount'],
                        'total' => $item['total'],
                    ];
                    WaybillItem::create($waybillItemData);
                }
            } else {
                throw new \Exception('İrsaliye kalemi yok.');
            }
            DB::commit();
            return redirect()->route("backend." . $this->page . "_list")->with('success', 'Kayıt başarılı şekilde işlendi');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withInput()->withErrors(['error' => $e->getMessage()]);
        }
    }
    public function datatableHook($obj)
    {
        return $obj->addColumn('waybillStatu.name', function ($item) {
            return $item->waybillStatu ? $item->waybillStatu->name : '-';
        });
    }
    public function delete(Request $request)
    {
        $waybill = $this->model::find((int)$request->post('id'));

        if (!$waybill) {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı',
            ]);
        }

        try {
            $waybill->delete(); // bootHook() içindeki deleting çalışır

            Cache::flush(); // Daha iyisi: Cache::forget("waybill_{$waybill->id}");

            return response()->json([
                'status' => true,
                'message' => 'İrsaliye başarıyla silindi',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
    public function exportExcel(Request $request, $unique = null)
    {
        $waybills = Waybill::with(['waybillStatu'])
            ->where('waybill_type', 2)
            ->get();

        $exporter = new ExportWaybill($waybills, 'Satış İrsaliyeleri');
        $spreadsheet = $exporter->export();

        $writer = new Xlsx($spreadsheet);
        $filename = 'satis_irsaliyeleri.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        $writer->save('php://output');
        exit;
    }
    public function pdf(Request $request, $unique = null)
    {
        // Faturanın cari bilgilerini alabilmek için 'invoice.current.city', 'invoice.current.country'
        // ve 'invoice.invoiceType' ilişkilerini yükleyin.
        $waybill = Waybill::with(['invoice.current.city', 'invoice.current.country', 'invoice.invoiceType', 'waybillItems', 'waybillStatu'])
            ->where('waybill_type', 2) // Ensure it's a sales waybill
            ->findOrFail($unique ?? $request->input('id')); // Use $unique or 'id' from request

        // Get paper size and orientation from request, with defaults
        $paperSize = $request->input('paper_size', 'a4');
        $orientation = $request->input('orientation', 'portrait');

        // Create an instance of ExportWaybill and pass the waybill object to exportWaybillToHtml
        $export = new ExportWaybill([]); // No items needed for the constructor as we're passing the full waybill object
        $html = $export->exportWaybillToHtml($waybill); // Generate HTML content for the waybill

        // Create PDF
        $pdf = Pdf::loadHTML($html)
            ->setPaper($paperSize, $orientation)
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'DejaVu Sans', // For Turkish character support
            ]);

        // Dynamically create filename, cleaning invalid characters
        $waybillNo = $waybill->waybill_no ?? 'bilinmiyor';
        $cleanWaybillNo = preg_replace('/[\/\\\\:*?"<>|]/', '_', $waybillNo);
        $fileName = 'irsaliye_' . $cleanWaybillNo . '.pdf';

        // Check if download is requested (default to true)
        $attachment = $request->input('download', true);

        return $pdf->stream($fileName, ['Attachment' => $attachment]);
    }

    public function updateStatus(Request $request)
    {
        $waybill = Waybill::find($request->id);
        $waybill->waybill_statu_id = $request->status_id;
        $waybill->reason_for_cancellation = $request->reason_for_cancellation;
        $waybill->save();
        return response()->json(['success' => true, 'message' => 'İrsaliye durumu başarıyla güncellendi.']);
    }

    public function detail(Request $request, $unique = null)
    {
        $waybill = Waybill::with([
            'waybillStatu',
            'invoice',
            'invoice.warehouse',
            'waybillItems.stock.product.unit',
            'waybillItems.stock.variant',
            'current',
        ])->findOrFail($unique ?? $request->input('id'));

        // waybillItems ilişkisini dizi olarak ekle (null foreach hatası için)
        $waybill->waybillItems = $waybill->waybillItems ? $waybill->waybillItems->all() : [];

        return view('backend.sale_waybill.detail', [
            'container' => (object)[
                'page' => $this->page,
                'title' => 'Satış İrsaliyesi'
            ],
            'title' => 'Satış İrsaliyesi Detay',
            'subTitle' => 'Satış İrsaliyesi Detay',
            'item' => $waybill,
            'waybillStatu' => WaybillStatu::where('is_active', 1)->get()
        ]);
    }
}
