@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON>üzenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <input type="hidden" name="volume" id="volume_hidden">
                        <div class="row gy-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">Ana Ürün <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="lucide:package"></iconify-icon>
                                    </span>
                                    <select class="form-select select2" name="product_id" id="product_id">
                                        <option value="">Ana ürün seçiniz</option>
                                        @foreach ($products as $product)
                                            <option
                                                {{ (old('product_id') ?? $item->product_id) == $product->id ? 'selected' : '' }}
                                                value="{{ $product->id }}" data-sku="{{ $product->sku }}"
                                                data-name="{{ $product->name }}"
                                                data-category="{{ $product->category->name ?? '' }}"
                                                data-brand="{{ $product->brand->name ?? '' }}"
                                                data-purchase-price="{{ $product->purchase_price }}"
                                                data-purchase-currency="{{ $product->purchase_currency_code }}"
                                                data-sale-price="{{ $product->sale_price }}"
                                                data-barcode="{{ $product->barcode }}"
                                                data-sale-currency="{{ $product->sale_currency_code }}"
                                                data-critical-stock="{{ $product->critical_stock_level }}"
                                                data-weight="{{ $product->weight }}" data-width="{{ $product->width }}"
                                                data-height="{{ $product->height }}" data-length="{{ $product->length }}">
                                                {{ $product->name }} ({{ $product->sku }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="product_id" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Varyant Adı <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="lucide:package"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Varyant adını giriniz"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Varyant Kodu (SKU) <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:code"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('sku') ?? ($item->sku ?? '') }}" name="sku"
                                        placeholder="Varyant kodunu giriniz">
                                    <x-form-error field="sku" />
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">Barkod</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mdi:barcode"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('barcode') ?? ($item->barcode ?? '') }}" name="barcode"
                                        placeholder="Barkod numarası">
                                    <x-form-error field="barcode" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="fluent:text-description-24-regular"></iconify-icon>
                                    </span>
                                    <textarea class="form-control" name="description" rows="3" placeholder="Varyant açıklaması">{{ old('description') ?? ($item->description ?? '') }}</textarea>
                                    <x-form-error field="description" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Ağırlık (kg)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="tabler:weight"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.001" min="0"
                                        value="{{ old('weight') ?? ($item->weight ?? '') }}" name="weight"
                                        placeholder="0.000">
                                    <x-form-error field="weight" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Genişlik (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:arrows-horizontal"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('width') ?? ($item->width ?? '') }}" name="width"
                                        placeholder="0.00">
                                    <x-form-error field="width" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Yükseklik (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:arrows-vertical"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('height') ?? ($item->height ?? '') }}" name="height"
                                        placeholder="0.00">
                                    <x-form-error field="height" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Uzunluk (cm)</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:ruler"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('length') ?? ($item->length ?? '') }}" name="length"
                                        placeholder="0.00">
                                    <x-form-error field="length" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Alış Fiyatı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:dollar-minimalistic-linear"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('purchase_price') ?? ($item->purchase_price ?? '') }}"
                                        name="purchase_price" placeholder="0.00">
                                    <x-form-error field="purchase_price" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Alış Para Birimi</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:currency"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="purchase_currency_code">
                                        <option value="">Para birimi seçiniz</option>
                                        @foreach ($currencies as $currency)
                                            <option
                                                {{ (old('purchase_currency_code') ?? ($item->purchase_currency_code ?? 'TRY')) == $currency->code ? 'selected' : '' }}
                                                value="{{ $currency->code }}">{{ $currency->code }} -
                                                {{ $currency->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="purchase_currency_code" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Satış Fiyatı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:dollar-minimalistic-linear"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" step="0.01" min="0"
                                        value="{{ old('sale_price') ?? ($item->sale_price ?? '') }}" name="sale_price"
                                        placeholder="0.00">
                                    <x-form-error field="sale_price" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Satış Para Birimi</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:currency"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="sale_currency_code">
                                        <option value="">Para birimi seçiniz</option>
                                        @foreach ($currencies as $currency)
                                            <option
                                                {{ (old('sale_currency_code') ?? ($item->sale_currency_code ?? 'TRY')) == $currency->code ? 'selected' : '' }}
                                                value="{{ $currency->code }}">{{ $currency->code }} -
                                                {{ $currency->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="sale_currency_code" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Kritik Stok Seviyesi <span class="text-danger">*</span></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="material-symbols:warning-outline"></iconify-icon>
                                    </span>
                                    <input type="number" class="form-control" min="0"
                                        value="{{ old('critical_stock_level') ?? ($item->critical_stock_level ?? 0) }}"
                                        name="critical_stock_level" placeholder="0">
                                    <x-form-error field="critical_stock_level" />
                                </div>
                            </div>
                            <div class="col-12 col-md-3">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:status-change"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">Kaydet</button>
                                <a href="{{ route('backend.' . $container->page . '_list') }}"
                                    class="btn btn-secondary">İptal</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var variantCounter = 1;
            var isManualBarcode = false;
            $('#product_id').change(function() {
                var selectedOption = $(this).find('option:selected');
                var productId = $(this).val();

                if (productId && selectedOption.length > 0) {
                    variantCounter = 1;
                    isManualBarcode = false;
                    var productSku = selectedOption.data('sku');
                    var purchasePrice = selectedOption.data('purchase-price');
                    var purchaseCurrency = selectedOption.data('purchase-currency');
                    var salePrice = selectedOption.data('sale-price');
                    var saleCurrency = selectedOption.data('sale-currency');
                    var criticalStock = selectedOption.data('critical-stock');
                    var weight = selectedOption.data('weight');
                    var width = selectedOption.data('width');
                    var height = selectedOption.data('height');
                    var length = selectedOption.data('length');
                    var barcode = selectedOption.data('barcode');

                    if (purchasePrice !== undefined && purchasePrice !== null) {
                        $('input[name="purchase_price"]').val(purchasePrice);
                    }
                    if (purchaseCurrency) {
                        $('select[name="purchase_currency_code"]').val(purchaseCurrency);
                    }
                    if (salePrice !== undefined && salePrice !== null) {
                        $('input[name="sale_price"]').val(salePrice);
                    }
                    if (saleCurrency) {
                        $('select[name="sale_currency_code"]').val(saleCurrency);
                    }

                    if (criticalStock !== undefined && criticalStock !== null) {
                        $('input[name="critical_stock_level"]').val(criticalStock);
                    }

                    if (weight !== undefined && weight !== null) {
                        $('input[name="weight"]').val(weight);
                    }
                    if (width !== undefined && width !== null) {
                        $('input[name="width"]').val(width);
                    }
                    if (height !== undefined && height !== null) {
                        $('input[name="height"]').val(height);
                    }
                    if (length !== undefined && length !== null) {
                        $('input[name="length"]').val(length);
                    }

                    updateBarcodeFromProduct(barcode);

                    calculateVolume();

                    var suggestedSku = generateVariantSku(selectedOption);
                    if (suggestedSku) {
                        $('input[name="sku"]').val(suggestedSku);
                    }
                } else {
                    $('input[name="barcode"]').val('');
                    $('input[name="sku"]').val('');
                }
            });

            $('input[name="width"], input[name="height"], input[name="length"]').on('input', function() {
                calculateVolume();
            });

            $('input[name="name"]').on('input', function() {
                updateSkuSuggestion();
            });

            function calculateVolume() {
                var width = parseFloat($('input[name="width"]').val()) || 0;
                var height = parseFloat($('input[name="height"]').val()) || 0;
                var length = parseFloat($('input[name="length"]').val()) || 0;

                if (width > 0 && height > 0 && length > 0) {
                    var widthM = width / 100;
                    var heightM = height / 100;
                    var lengthM = length / 100;
                    var volume = (widthM * heightM * lengthM).toFixed(6);
                    $('#volume_hidden').val(volume);
                } else {
                    $('#volume_hidden').val('');
                }
            }

            function updateSkuSuggestion() {
                var selectedOption = $('#product_id').find('option:selected');
                if (selectedOption.val()) {
                    var suggestedSku = generateVariantSku(selectedOption);
                    if (suggestedSku) {
                        $('input[name="sku"]').val(suggestedSku);
                    }
                    var baseBarcode = selectedOption.data('barcode');
                    updateBarcodeFromProduct(baseBarcode);
                }
            }

            function updateBarcodeFromProduct(baseBarcode) {
                if (isManualBarcode) {
                    return;
                }

                if (baseBarcode !== undefined && baseBarcode !== null && baseBarcode !== '') {
                    var variantBarcode = generateVariantBarcode(baseBarcode);
                    $('input[name="barcode"]').val(variantBarcode);
                } else {
                    $('input[name="barcode"]').val('');
                }
            }

            $('input[name="barcode"]').on('input', function() {
                isManualBarcode = true;
            });



            function generateVariantSku(selectedOption) {
                var productName = selectedOption.data('name');
                var categoryName = selectedOption.data('category');
                var brandName = selectedOption.data('brand');
                var variantName = $('input[name="name"]').val().trim();

                if (!productName || !categoryName || !variantName) {
                    return null;
                }

                var categoryCode = getCodeFromString(categoryName, 3);

                var brandCode = brandName ? getCodeFromString(brandName, 3) : 'NOB';

                var productCode = getCodeFromString(productName, 3);

                var variantCode = getCodeFromString(variantName, 3);

                var prefix = categoryCode + brandCode + productCode + variantCode;

                var sequence = Date.now().toString().slice(-4);

                return prefix + sequence;
            }

            function generateVariantBarcode(baseBarcode) {
                if (!baseBarcode || baseBarcode === '') {
                    return '';
                }

                var variantName = $('input[name="name"]').val().trim();
                var sequence;

                if (variantName !== '') {
                    var hash = 0;
                    for (var i = 0; i < variantName.length; i++) {
                        var char = variantName.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    sequence = Math.abs(hash % 9999) + 1;
                    sequence = sequence.toString().padStart(4, '0');
                } else {
                    sequence = variantCounter.toString().padStart(4, '0');
                    variantCounter++;
                }

                return baseBarcode + sequence;
            }

            function getCodeFromString(str, length) {
                if (!str || str.length === 0) {
                    return '';
                }

                var cleanStr = slugify(str)
                    .replace(/[^a-zA-Z0-9]/g, '')
                    .toUpperCase();

                if (cleanStr.length === 0) {
                    return '';
                }

                return cleanStr.length >= length ?
                    cleanStr.substring(0, length) :
                    cleanStr.padEnd(length, '0');
            }

            function slugify(str) {
                var turkishChars = {
                    'ç': 'c',
                    'Ç': 'C',
                    'ğ': 'g',
                    'Ğ': 'G',
                    'ı': 'i',
                    'I': 'I',
                    'ö': 'o',
                    'Ö': 'O',
                    'ş': 's',
                    'Ş': 'S',
                    'ü': 'u',
                    'Ü': 'U'
                };

                return str.replace(/[çÇğĞıIöÖşŞüÜ]/g, function(match) {
                    return turkishChars[match] || match;
                });
            }

            calculateVolume();

            var initialProduct = $('#product_id').find('option:selected');
            if (initialProduct.val()) {
                var initialBarcode = initialProduct.data('barcode');
                if ($('input[name="barcode"]').val() === '') {
                    updateBarcodeFromProduct(initialBarcode);
                }
            }

            $('form').on('submit', function(e) {
                calculateVolume();

                var productId = $('#product_id').val();
                var name = $('input[name="name"]').val().trim();
                var sku = $('input[name="sku"]').val().trim();

                if (!productId) {
                    e.preventDefault();
                    alert('Lütfen ana ürün seçiniz.');
                    $('#product_id').focus();
                    return false;
                }

                if (!name) {
                    e.preventDefault();
                    alert('Lütfen varyant adı giriniz.');
                    $('input[name="name"]').focus();
                    return false;
                }

                if (!sku) {
                    e.preventDefault();
                    alert('Lütfen varyant kodu (SKU) giriniz.');
                    $('input[name="sku"]').focus();
                    return false;
                }
            });
        });
    </script>
@endsection
