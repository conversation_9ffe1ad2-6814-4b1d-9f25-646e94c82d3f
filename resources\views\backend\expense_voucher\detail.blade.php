@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = $container->title . ' Detay';
@endphp

@section('content')
<style>
    .info-card {
        height: 100%;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        border: 1px solid;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border-color: #ffeaa7;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
        border-color: #c3e6cb;
    }
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
    }
</style>

<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fs-6">
                    {{ $subTitle }}
                </h5>
                <div class="d-flex gap-2">
                    @if($item->expense_voucher_statu_id == 1)
                        <button type="button" class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center" 
                                onclick="openStatusModal()">
                            <iconify-icon icon="lucide:edit" class="me-1"></iconify-icon> Durum Değiştir
                        </button>
                    @endif
                    <a href="{{ route('backend.' . $container->page . '_list') }}"
                        class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                        <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Özet Kartı -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-gradient-primary text-white">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h4 class="mb-1">{{ $item->voucher_no }}</h4>
                                        <p class="mb-0 opacity-75">{{ \Carbon\Carbon::parse($item->voucher_date)->format('d.m.Y') }} - {{ $item->expenseType->name ?? '-' }}</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <h3 class="mb-0">{{ number_format($item->grand_total, 2, ',', '.') }} ₺</h3>
                                        <small class="opacity-75">Genel Toplam</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fiş Bilgileri -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Fiş Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Fiş No:</strong> {{ $item->voucher_no }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Fiş Tarihi:</strong> {{ \Carbon\Carbon::parse($item->voucher_date)->format('d.m.Y') }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Masraf Türü:</strong> {{ $item->expenseType->name ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Ödeme Türü:</strong> {{ $item->paymentType->name ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Durum:</strong>
                                        @php
                                            $statusClass = '';
                                            switch($item->expense_voucher_statu_id) {
                                                case 1:
                                                    $statusClass = 'status-pending';
                                                    break;
                                                case 2:
                                                    $statusClass = 'status-approved';
                                                    break;
                                                case 3:
                                                    $statusClass = 'status-cancelled';
                                                    break;
                                            }
                                        @endphp
                                        <span class="status-badge {{ $statusClass }}">
                                            {{ $item->expenseVoucherStatu->name ?? '-' }}
                                        </span>
                                    </div>
                                    @if($item->expense_voucher_statu_id == 3 && $item->reason_for_cancellation)
                                        <div class="col-md-12 mb-2">
                                            <strong>İptal Sebebi:</strong> {{ $item->reason_for_cancellation }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Tutar Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Ara Toplam:</strong> 
                                        <span class="text-primary fw-bold">{{ number_format($item->subtotal, 2, ',', '.') }} ₺</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>KDV Oranı:</strong> 
                                        <span class="text-info">{{ number_format($item->vat_rate, 2, ',', '.') }}%</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>KDV Tutarı:</strong> 
                                        <span class="text-warning">{{ number_format($item->vat_total, 2, ',', '.') }} ₺</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Genel Toplam:</strong> 
                                        <span class="text-success fw-bold fs-5">{{ number_format($item->grand_total, 2, ',', '.') }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Açıklama -->
                @if($item->description)
                <div class="card mb-4">
                    <div class="card-body">
                        <h6 class="mb-3">Açıklama</h6>
                        <p class="mb-0">{{ $item->description }}</p>
                    </div>
                </div>
                @endif  
            </div>
        </div>
    </div>
</div>

<!-- Durum Değiştirme Modal -->
<div class="modal fade" id="statusChangeModal" tabindex="-1" aria-labelledby="statusChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusChangeModalLabel">Durum Değiştir</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="statusChangeForm" method="POST">
                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_status" class="form-label">Yeni Durum</label>
                        <select class="form-select" id="new_status" name="expense_voucher_statu_id" required>
                            <option value="">Durum Seçiniz</option>
                            @foreach ($expenseVoucherStatu as $status)
                                @if($status->id != $item->expense_voucher_statu_id)
                                    <option value="{{ $status->id }}">{{ $status->name }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3" id="cancellation_reason_container" style="display: none;">
                        <label for="reason_for_cancellation" class="form-label">İptal Sebebi <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason_for_cancellation" name="reason_for_cancellation" rows="3" placeholder="İptal sebebini giriniz..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Durumu Değiştir</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    let currentStatus = {{ $item->expense_voucher_statu_id }};
    let statusChanged = false;

    // Sayfa yüklendiğinde durum kontrolü
    document.addEventListener('DOMContentLoaded', function() {
        // Eğer durum zaten değiştirilmişse (1 değilse), butonları gizle
        if (currentStatus !== 1) {
            const statusButtons = document.querySelectorAll('[onclick^="openStatusModal"]');
            statusButtons.forEach(button => {
                button.style.display = 'none';
            });
        }
    });

    function openStatusModal() {
        if (statusChanged) {
            alert('Bu fişin durumu zaten değiştirilmiş. Durum sadece bir kez değiştirilebilir.');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
        document.getElementById('new_status').value = '';
        document.getElementById('reason_for_cancellation').value = '';
        document.getElementById('cancellation_reason_container').style.display = 'none';
        modal.show();
    }

    document.getElementById('statusChangeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (statusChanged) {
            alert('Bu fişin durumu zaten değiştirilmiş. Durum sadece bir kez değiştirilebilir.');
            return;
        }

        const formData = new FormData(this);

        fetch('{{ route("backend.expense_voucher_update_status", $item->id) }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                statusChanged = true;
                alert('Durum başarıyla değiştirildi!');
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu!');
        });
    });

    // Durum seçimi değiştiğinde iptal sebebi alanını kontrol et
    document.getElementById('new_status').addEventListener('change', function() {
        const reasonContainer = document.getElementById('cancellation_reason_container');
        const reasonInput = document.getElementById('reason_for_cancellation');
        
        if (this.value == 3) {
            reasonContainer.style.display = 'block';
            reasonInput.required = true;
        } else {
            reasonContainer.style.display = 'none';
            reasonInput.required = false;
        }
    });
</script>
@endsection
