<?php

namespace App\Http\Controllers\Backend;

use App\Models\UnitType;
use Illuminate\Http\Request;

class UnitTypeController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Birim Tipleri';
        $this->page = 'unit_type';
        $this->model = new UnitType();

        $this->view = (object)array(
            'breadcrumb' => array(
                'Birim Tipleri' => route('backend.unit_type_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|min:2|max:255',
                'code' => 'required|string|min:2|max:50',
                'is_active' => 'required|boolean',
            ],
            [
                'name.required' => 'Birim tipi adı zorunludur.',
                'name.max' => 'Birim tipi adı en fazla 255 karakter olabilir.',
                'name.min' => 'Birim tipi adı en az 2 karakter olmalıdır.',
                'code.required' => 'Birim tipi kodu zorunludur.',
                'code.max' => 'Birim tipi kodu en fazla 50 karakter olabilir.',
                'code.min' => 'Birim tipi kodu en az 2 karakter olmalıdır.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
            ]
        );
        parent::__construct();
    }
}
