<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Brand extends BaseModel
{
    use SoftDeletes;

    protected $table = 'brands';

    protected $guarded = [];

    public function products()
    {
        return $this->hasMany(Product::class, 'brand_id');
    }



    public function getProductsCountAttribute()
    {
        if (array_key_exists('products_count', $this->attributes)) {
            return $this->attributes['products_count'];
        }

        return $this->products()->count();
    }
}
