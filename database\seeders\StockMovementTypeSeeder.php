<?php

namespace Database\Seeders;

use App\Models\StockMovementType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StockMovementTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                ['name' => 'Giriş', 'description' => 'Depoya mal girişi'],
                ['name' => 'Çıkı<PERSON>', 'description' => 'Depodan mal çıkışı'],
                ['name' => 'Transfer', 'description' => 'Depodan depoya mal transferi'],
            ];

            foreach ($types as $type) {
                StockMovementType::updateOrCreate(
                    ['name' => $type['name']],
                    $type
                );
            }
        });

        $this->command->info('StockMovementType verileri başarıyla oluşturuldu.');
    }
}
