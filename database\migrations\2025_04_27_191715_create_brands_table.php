<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->nullable()->comment('Marka adı');
            $table->string('code', 50)->nullable()->comment('Marka kodu');
            $table->longText('description')->nullable()->comment('Açıklama');
            $table->string('email', 100)->unique()->nullable()->comment('E-posta');
            $table->string('phone', 20)->nullable()->comment('Telefon');
            $table->longText('address')->nullable()->comment('Adres');
            $table->string('contact_name', 255)->nullable()->comment('<PERSON><PERSON><PERSON><PERSON><PERSON> kişisi');
            $table->tinyInteger('is_active')->default(1)->comment('Aktif mi?');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
