<?php

namespace App\Http\Controllers\Backend;

use App\Models\Category;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\VatRate;
use Illuminate\Http\Request;
use App\Models\Role;


class CategoryController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Kategori';
        $this->page = 'category';
        $this->model = new Category();

        $this->relation = ['parent', 'children'];

        $this->listQuery = Category::withCount([
                'children',
                'products'
            ])
            ->with('parent', 'children')
            ->select('categories.*');

        $this->view = (object)array(
            'breadcrumb' => array(
                'Kategori Listesi' => route('backend.category_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|max:255',
                'is_active' => 'required|boolean',
                'parent_id' => 'nullable|integer|exists:categories,id',
                'description' => 'nullable|string|max:500',
                'vat_rate_id' => 'nullable|integer|exists:vat_rates,id',
            ],
            [
                'name.required' => 'Kategori adı zorunludur.',
                'name.max' => 'Kategori adı en fazla 255 karakter olabilir.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır.',
                'parent_id.integer' => 'Üst kategori seçilmedi.',
                'parent_id.exists' => 'Üst kategori seçilmedi.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
                'description.max' => 'Açıklama en fazla 500 karakter olabilir.',
                'vat_rate_id.integer' => 'KDV oranı seçilmedi.',
                'vat_rate_id.exists' => 'KDV oranı seçilmedi.',
            ]
        );

        view()->share('categories', Category::active()->get());
        view()->share('products', Product::active()->get());
        view()->share('brands', Brand::active()->get());
        view()->share('units', Unit::active()->get());
        view()->share('vatRates', VatRate::active()->get());
        parent::__construct();
    }
    protected function datatableHook($obj)
    {
        return $obj
            ->editColumn('products_count', function ($item) {
                $directProducts = $item->products_count ?? 0;
                $allProducts = $item->getAllSubcategoryProductsCount();
                if ($directProducts == $allProducts) {
                    return $directProducts;
                } else {
                    return $directProducts . ' (' . $allProducts . ' toplam)';
                }
            });
    }
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);
            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
        } else {
            $item = new $this->model;
        }

        $categories = Category::active()->with('childrenRecursive')->whereNull('parent_id')->get();

        $excludeIds = [];
        if (!is_null($item->id) && $item->exists) {
            $excludeIds = $this->getAllDescendantIds($item);
            $excludeIds[] = $item->id;
        }

        $categoryOptions = $this->buildCategoryOptions($categories, $excludeIds);

        return view("backend.$this->page.form", compact('item', 'categoryOptions'));
    }

    private function getAllDescendantIds($category)
    {
        $ids = [];
        $category->load('childrenRecursive');

        foreach ($category->childrenRecursive as $child) {
            $ids[] = $child->id;
            $ids = array_merge($ids, $this->getAllDescendantIds($child));
        }

        return $ids;
    }

    private function buildCategoryOptions($categories, $excludeIds = [], $prefix = '', $depth = 0)
    {
        $options = [];

        foreach ($categories as $category) {
            if (!in_array($category->id, $excludeIds)) {
                $options[] = [
                    'id' => $category->id,
                    'name' => $prefix . $category->name,
                    'depth' => $depth
                ];

                if ($category->childrenRecursive->count() > 0) {
                    $childOptions = $this->buildCategoryOptions(
                        $category->childrenRecursive,
                        $excludeIds,
                        $prefix . '— ',
                        $depth + 1
                    );
                    $options = array_merge($options, $childOptions);
                }
            }
        }

        return $options;
    }

    public function detail(Request $request, $unique = NULL)
    {
        $item = $this->model::find($unique);

        if (is_null($item)) {
            return redirect()->back()->with('error', 'Kayıt bulunamadı');
        }

        if ($request->has('datatable') && $request->datatable == 'subcategories') {
            $select = Category::where('parent_id', $unique)
                ->withCount(['products', 'children'])
                ->get();

            $obj = datatables()->of($select)
                ->editColumn('products_count', function ($item) {
                    return $item->products_count ?? 0;
                })
                ->editColumn('children_count', function ($item) {
                    return $item->children_count ?? 0;
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);

            return $obj;
        }

        if ($request->has('datatable') && $request->datatable == 'products') {
            $categoryIds = $item->getAllSubcategoryIds();

            $select = Product::whereIn('category_id', $categoryIds)
                ->with(['brand', 'unit', 'category'])
                ->get();

            $obj = datatables()->of($select)
                ->editColumn('sku', function ($item) {
                    return $item->sku ?? '-';
                })
                ->editColumn('category', function ($item) {
                    return $item->category->name ?? '-';
                })
                ->editColumn('brand', function ($item) {
                    return $item->brand->name ?? '-';
                })
                ->editColumn('unit', function ($item) {
                    return $item->unit->name ?? '-';
                })
                ->editColumn('sale_price', function ($item) {
                    return number_format($item->sale_price, 2, ',', '.') . ' ' . ($item->sale_currency_code ?? 'TL');
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);

            return $obj;
        }

        return view("backend.$this->page.detail", compact('item', 'unique'));
    }
}
