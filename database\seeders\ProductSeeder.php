<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createProducts();
        });

        $this->command->info('Product verileri başarıyla oluşturuldu.');
    }

    /**
     * Ürünleri oluştur
     */
    private function createProducts(): void
    {
        // Kategorileri, markaları ve birimleri al
        $categories = Category::all()->keyBy('name');
        $brands = Brand::all()->keyBy('name');
        $units = Unit::all()->keyBy('name');

        if ($categories->isEmpty() || $brands->isEmpty() || $units->isEmpty()) {
            $this->command->warn('Lütfen önce CategorySeeder, BrandSeeder ve UnitSeeder\'ı çalıştırın!');
            return;
        }

        $products = $this->getProductData();

        foreach ($products as $productData) {
            $this->createProduct($productData, $categories, $brands, $units);
        }
    }

    /**
     * Tek bir ürün oluştur
     */
    private function createProduct(array $productData, $categories, $brands, $units): void
    {
        $category = $categories->get($productData['category']);
        $brand = $brands->get($productData['brand']);
        $unit = $units->get($productData['unit']);

        if (!$category || !$brand || !$unit) {
            $this->command->warn("Ürün oluşturulamadı: {$productData['name']} - Eksik kategori, marka veya birim");
            return;
        }

        $sku = $this->generateSku($category->name, $productData['name']);

        Product::updateOrCreate(
            ['sku' => $sku],
            [
                'name' => $productData['name'],
                'sku' => $sku,
                'description' => $productData['description'] ?? $productData['name'] . ' ürünü',
                'category_id' => $category->id,
                'brand_id' => $brand->id,
                'unit_id' => $unit->id,
                'unit_type_id' => $unit->unit_type_id,
                'purchase_price' => $productData['purchase_price'],
                'sale_price' => $productData['sale_price'],
                'purchase_currency_code' => $productData['purchase_currency_code'] ?? 'TRY',
                'sale_currency_code' => $productData['sale_currency_code'] ?? 'TRY',
                'critical_stock_level' => $productData['critical_stock_level'],
                'weight' => $productData['weight'] ?? null,
                'width' => $productData['width'] ?? null,
                'height' => $productData['height'] ?? null,
                'length' => $productData['length'] ?? null,
                'volume' => $this->calculateVolume(
                    $productData['width'] ?? null,
                    $productData['height'] ?? null,
                    $productData['length'] ?? null
                ),
                'barcode' => $productData['barcode'] ?? null,
                'is_active' => $productData['is_active'] ?? true,
                'created_by' => 1,
                'updated_by' => 1,
            ]
        );
    }

    /**
     * Hacim hesapla (m³)
     */
    private function calculateVolume(?float $width, ?float $height, ?float $length): ?float
    {
        if ($width === null || $height === null || $length === null) {
            return null;
        }

        // Hacim = genişlik × yükseklik × uzunluk / 1.000.000 (cm³'ten m³'e çevrim)
        return ($width * $height * $length) / 1000000;
    }

    /**
     * SKU oluştur
     */
    private function generateSku(string $categoryName, string $productName): string
    {
        // Kategori kodu (ilk 3 karakter)
        $categoryCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $categoryName), 0, 3));

        // Ürün kodu (orta kısım)
        $productCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $productName), 0, 6));

        // Rastgele sayı (son 3 karakter)
        $randomNumber = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);

        $baseSku = $categoryCode . '-' . $productCode . '-' . $randomNumber;

        // Eğer SKU zaten varsa, sayıyı artır
        $sku = $baseSku;
        $counter = 1;

        while (Product::where('sku', $sku)->exists()) {
            $sku = $categoryCode . '-' . $productCode . '-' . str_pad($randomNumber + $counter, 3, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $sku;
    }

    /**
     * Ürün verilerini döndür
     */
    private function getProductData(): array
    {
        return [
            // Elektronik Ürünler
            [
                'name' => 'iPhone 15 Pro Max',
                'category' => 'Akıllı Telefon',
                'brand' => 'Apple',
                'purchase_price' => 45000.00,
                'sale_price' => 54999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 5,
                'weight' => 0.221,
                'width' => 7.67,
                'height' => 15.93,
                'length' => 0.83,
            ],
            [
                'name' => 'Samsung Galaxy S24 Ultra',
                'category' => 'Akıllı Telefon',
                'brand' => 'Samsung',
                'purchase_price' => 40000.00,
                'sale_price' => 48999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 5,
                'weight' => 0.232,
                'width' => 7.92,
                'height' => 16.27,
                'length' => 0.86,
            ],
            [
                'name' => 'MacBook Pro 16" M3',
                'category' => 'Dizüstü Bilgisayar',
                'brand' => 'Apple',
                'purchase_price' => 85000.00,
                'sale_price' => 99999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'weight' => 2.140,
                'width' => 24.81,
                'height' => 35.57,
                'length' => 1.68,
            ],
            [
                'name' => 'ASUS ROG Strix G16',
                'category' => 'Gaming Bilgisayar',
                'brand' => 'Asus',
                'purchase_price' => 35000.00,
                'sale_price' => 42999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'weight' => 2.500,
            ],
            [
                'name' => 'LG OLED65C3PSA 65" 4K TV',
                'category' => 'Televizyon',
                'brand' => 'LG',
                'purchase_price' => 28000.00,
                'sale_price' => 34999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'weight' => 20.9,
                'width' => 144.0,
                'height' => 82.8,
                'length' => 25.1,
            ],

            // Giyim Ürünleri
            [
                'name' => 'Nike Air Max 270',
                'category' => 'Koşu',
                'brand' => 'Nike',
                'purchase_price' => 1200.00,
                'sale_price' => 1899.00,
                'unit' => 'Çift',
                'critical_stock_level' => 10,
                'weight' => 0.380,
            ],
            [
                'name' => 'Adidas Ultraboost 22',
                'category' => 'Koşu',
                'brand' => 'Adidas',
                'purchase_price' => 1500.00,
                'sale_price' => 2299.00,
                'unit' => 'Çift',
                'critical_stock_level' => 10,
                'weight' => 0.320,
            ],
            [
                'name' => 'Zara Kadın Blazer Ceket',
                'category' => 'Ceket ve Mont',
                'brand' => 'Zara',
                'purchase_price' => 400.00,
                'sale_price' => 799.00,
                'unit' => 'Adet',
                'critical_stock_level' => 15,
                'weight' => 0.650,
            ],
            [
                'name' => 'H&M Erkek Slim Fit Pantolon',
                'category' => 'Pantolon',
                'brand' => 'H&M',
                'purchase_price' => 200.00,
                'sale_price' => 399.00,
                'unit' => 'Adet',
                'critical_stock_level' => 20,
                'weight' => 0.450,
            ],

            // Bisiklet Ürünleri
            [
                'name' => 'Bianchi Via Nirone 7',
                'category' => 'Yol Bisikleti',
                'brand' => 'Bianchi',
                'purchase_price' => 15000.00,
                'sale_price' => 22999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'weight' => 9.2,
                'width' => 180.0,
                'height' => 110.0,
                'length' => 70.0,
            ],
            [
                'name' => 'Giant Talon 29 2',
                'category' => 'Dağ Bisikleti',
                'brand' => 'Giant',
                'purchase_price' => 12000.00,
                'sale_price' => 17999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'weight' => 13.5,
            ],
            [
                'name' => 'Trek FX 3 Disc',
                'category' => 'Şehir Bisikleti',
                'brand' => 'Trek',
                'purchase_price' => 8000.00,
                'sale_price' => 12999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 4,
                'weight' => 11.8,
            ],

            // Ev ve Yaşam
            [
                'name' => 'IKEA EKTORP 3 Kişilik Kanepe',
                'category' => 'Oturma Odası Mobilyaları',
                'brand' => 'IKEA',
                'purchase_price' => 3500.00,
                'sale_price' => 5999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'weight' => 85.0,
                'width' => 218.0,
                'height' => 88.0,
                'length' => 88.0,
            ],
            [
                'name' => 'Bosch Serie 6 Bulaşık Makinesi',
                'category' => 'Bulaşık Makinesi',
                'brand' => 'Bosch',
                'purchase_price' => 8000.00,
                'sale_price' => 11999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'weight' => 48.0,
                'width' => 60.0,
                'height' => 84.5,
                'length' => 55.0,
            ],
            [
                'name' => 'Arçelik No-Frost Buzdolabı',
                'category' => 'Buzdolabı',
                'brand' => 'Arçelik',
                'purchase_price' => 12000.00,
                'sale_price' => 17999.00,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'weight' => 75.0,
                'width' => 60.0,
                'height' => 185.0,
                'length' => 67.5,
            ],

            // Gıda Ürünleri
            [
                'name' => 'Nestle Fitness Tahıllı Gevrek 400g',
                'category' => 'Temel Gıda',
                'brand' => 'Nestle',
                'purchase_price' => 25.00,
                'sale_price' => 45.90,
                'unit' => 'Adet',
                'critical_stock_level' => 50,
                'weight' => 0.400,
                'width' => 7.5,
                'height' => 26.0,
                'length' => 19.0,
            ],
            [
                'name' => 'Ülker Çikolatalı Gofret',
                'category' => 'Çikolata',
                'brand' => 'Ülker',
                'purchase_price' => 8.00,
                'sale_price' => 14.90,
                'unit' => 'Adet',
                'critical_stock_level' => 100,
                'weight' => 0.035,
            ],

            // Kozmetik
            [
                'name' => 'L\'Oreal Paris Revitalift Gündüz Kremi',
                'category' => 'Nemlendirici',
                'brand' => 'L\'Oréal',
                'purchase_price' => 120.00,
                'sale_price' => 249.90,
                'unit' => 'Adet',
                'critical_stock_level' => 20,
                'weight' => 0.050,
                'width' => 5.0,
                'height' => 5.0,
                'length' => 8.0,
            ],
            [
                'name' => 'Maybelline Fit Me Fondöten',
                'category' => 'Fondöten',
                'brand' => 'Maybelline',
                'purchase_price' => 80.00,
                'sale_price' => 159.90,
                'unit' => 'Adet',
                'critical_stock_level' => 25,
                'weight' => 0.030,
                'width' => 3.5,
                'height' => 10.0,
                'length' => 3.5,
            ],
        ];
    }
}
