@extends('layout.layout')
@php
$title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
$subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
$hasLockedProducts = isset($item->offerProducts) && $item->offerProducts->whereIn('status', [1])->count() > 0;
$tryRate = $exchangeRate->where('code', 'TRY')->first();
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">
                    {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                </h5>
            </div>
            <div class="card-body">
                <form id="offerGivenForm" action="{{ route('backend.offer_given_save', ['unique' => $item->id]) }}" method="POST"
                    enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="offer_type_id" value="2">
                    <input type="hidden" id="js_offer_number" value="{{ $item->offer_number }}">
                    <input type="hidden" name="total_price_fx" id="total_price_fx_hidden">
                    <input type="hidden" name="vat_amount_fx" id="vat_amount_fx_hidden">
                    <input type="hidden" name="total_amount_fx" id="total_amount_fx_hidden">
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <h6 class="mb-0">Teklif Ekle</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse"
                                    data-bs-target="#teklifBilgileri" aria-expanded="true"
                                    aria-controls="teklifBilgileri">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="teklifBilgileri">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Teklif Bilgileri</h6>
                                            <div class="row g-3">
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Teklif Numarası</label>
                                                    <input type="text" class="form-control" name="offer_number"
                                                        id="offer_number"
                                                        oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                                        value="{{ old('offer_number', $item->id ? $item->offer_number : \App\Models\Offer::generateOfferNumber(1)) }}"
                                                        {{ $item->id ? 'readonly' : '' }} placeholder="Otomatik
                                                    oluşturulacak"
                                                    readonly>
                                                </div>
                                                <div class="col-12 col-lg-6">
                                                    <label class="form-label">Teklif Tarihi</label>
                                                    <input class="form-control radius-8 bg-base" id="offer_date"
                                                        name="offer_date" type="date"
                                                        value="{{ old('offer_date', isset($item->offer_date) && !is_null($item->offer_date) ? \Carbon\Carbon::parse($item->offer_date)->format('Y-m-d') : '') }}">
                                                    <x-form-error field="offer_date" />
                                                </div>
                                                <div class="col-12 col-lg-6">
                                                    <label class="form-label">Teklif Son Tarihi</label>
                                                    <input class="form-control radius-8 bg-base" id="offer_deadline"
                                                        name="offer_deadline" type="date"
                                                        value="{{ old('offer_deadline', isset($item->offer_deadline) && !is_null($item->offer_deadline) ? \Carbon\Carbon::parse($item->offer_deadline)->format('Y-m-d') : '') }}">
                                                    <x-form-error field="offer_deadline" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label required">Depo</label>
                                                    <select class="form-select select2" name="warehouse_id" id="warehouse_id"
                                                        style="background-color: inherit; color: inherit;">
                                                        <option value="">Depo Seçiniz...</option>
                                                        @foreach ($warehouses as $warehouse)
                                                        <option value="{{ $warehouse->id }}"
                                                            title="{{ $warehouse->name }}" {{ old('warehouse_id',
                                                            $item->warehouse_id) == $warehouse->id ?
                                                            'selected' : '' }}>
                                                            {{ Str::limit($warehouse->name, 30) }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="warehouse_id" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Döviz</label>
                                                    <div class="input-group">
                                                        <select class="form-select select2" name="exchange_rate_id"
                                                            id="exchange_rate_id">
                                                            <option value="">Döviz Seçiniz...</option>
                                                            @foreach ($exchangeRate as $rate)
                                                            <option value="{{ $rate->id }}"
                                                                data-currency-code="{{ $rate->code }}"
                                                                data-selling-rate="{{ $rate->selling_rate }}"
                                                                data-symbol="{{ $rate->symbol }}" {{
                                                                old('exchange_rate_id', $item->exchange_rate_id) ==
                                                                $rate->id ? 'selected' :
                                                                ($rate->code == 'TRY' && !old('exchange_rate_id') &&
                                                                !$item->exchange_rate_id ? 'selected' : '') }}>
                                                                {{ $rate->code }} ({{ $rate->symbol }}) = {{ $rate->selling_rate }}
                                                            </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <x-form-error field="exchange_rate_id" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Cari Hesap Bilgileri</h6>
                                            <div class="row g-3">
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Cari Kodu-Unvanı</label>
                                                    <select class="form-select select2" name="current_id"
                                                        id="current_id">
                                                        <option value="">Cari Seçiniz...</option>
                                                        @foreach ($current as $c)
                                                        <option value="{{ $c->id }}" data-current-code="{{ $c->title }}"
                                                            data-address="{{ $c->address ?? '' }}, {{ $c->district->name ?? '' }}, {{ $c->city->name ?? '' }}, {{ $c->country->name ?? '' }}"
                                                            title="{{ $c->title }} - {{ $c->current_type_id }}" {{ old('current_id', $item->current_id) == $c->id ? 'selected': ''}}
                                                            {{ $c->deleted_at ? 'style=color:red;font-style:italic;' :''}}>
                                                            {{ $c->title }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="current_id" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Cari Adresi</label>
                                                    <input type="text" class="form-control" id="address" readonly>
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Sevkiyat Adres</label>
                                                    <input type="text" class="form-control" name="shipping_address"
                                                        id="shipping_address"
                                                        value="{{ old('shipping_address', $item->shipping_address ?? '') }}">
                                                    <x-form-error field="shipping_address" />
                                                </div>
                                                <div class="col-12 col-lg-12">
                                                    <label class="form-label">Ödeme Planı</label>
                                                    <select class="form-select select2" name="payment_type_id"
                                                        id="payment_type_id">
                                                        <option value="">Ödeme Planı Seçiniz...</option>
                                                        @foreach ($paymentType as $plan)
                                                        <option value="{{ $plan->id }}"
                                                            data-branch-id="{{ $plan->branch_id ?? '' }}" {{--
                                                            data-branch-id="{{ $plan->branch_id ?? '' }}" --}} {{
                                                            old('payment_type_id', $item->payment_type_id) == $plan->id
                                                            ?
                                                            'selected' : '' }}>

                                                            {{ $plan->name }}
                                                        </option>
                                                        @endforeach
                                                    </select>
                                                    <x-form-error field="payment_type_id" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <h6 class="mb-0">Ürünler</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse"
                                    data-bs-target="#ürünler" aria-expanded="true" aria-controls="ürünler">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="ürünler">
                                @include('backend.offer_given.partials.product_rows', ['products' => $products,
                                'exchangeRates' =>
                                $exchangeRate])
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center p-3">
                                <button type="button" class="btn btn-sm btn-outline-primary ms-auto"
                                    data-bs-toggle="collapse" data-bs-target="#açıklama" aria-expanded="true"
                                    aria-controls="açıklama">
                                    <iconify-icon icon="mdi:plus"></iconify-icon>
                                </button>
                            </div>
                            <div class="row collapse show" id="açıklama">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Teklif Notu</h6>
                                            <div class="row g-2">
                                                <div class="col-12">
                                                    <textarea class="form-control" name="notes" rows="5"
                                                        placeholder="Notunuzu yazın...">{{ old('notes', $item->notes ?? '') }}</textarea>
                                                </div>
                                                <x-form-error field="notes" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Alt Toplamlar</h6>
                                            <div class="row g-2">
                                                <div class="col-4 text-strat">Toplam</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end"
                                                            id="total_price" name="total_price"
                                                            value="{{ old('total_price', displayMoneyNumber($item->total_price)) }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_total"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span
                                                            style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end"
                                                            id="total_price_fx" readonly>
                                                        <span class="input-group-text" id="currency_symbol_total_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-strat">KDV</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end" id="vat_amount"
                                                            name="vat_amount"
                                                            value="{{ old('vat_amount', displayMoneyNumber($item->vat_amount)) }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_vat"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span
                                                            style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end"
                                                            id="vat_amount_fx" readonly>
                                                        <span class="input-group-text" id="currency_symbol_vat_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 text-strat">Genel Toplam</div>
                                                <div class="col-8">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control text-end"
                                                            id="total_amount" name="total_amount"
                                                            value="{{ old('total_amount', displayMoneyNumber($item->total_amount)) }}"
                                                            readonly>
                                                        <span class="input-group-text" id="currency_symbol_total_amount"
                                                            style="background-color: inherit; color: inherit;">₺</span>
                                                        <span
                                                            style="width: 8px; border: none; background: transparent;"></span>
                                                        <input type="text" class="form-control text-end"
                                                            id="total_amount_fx" readonly>
                                                        <span class="input-group-text"
                                                            id="currency_symbol_total_amount_fx"
                                                            style="background-color: inherit; color: inherit;"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 text-end">
                        <a href="{{ route('backend.' . $container->page . '_list') }}"
                            class="btn btn-secondary">İptal</a>
                        <button type="submit" class="btn btn-primary" id="offerGivenSaveBtn">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="stockProductModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen-lg-down modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Stok Ürünleri</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table id="stockProductModalTable" datatable class="table bordered-table mb-0 dataTable"
                        width="100%">
                        <thead>
                            <tr>
                                <th scope="col">
                                    <input type="checkbox" id="selectAllStockProducts" class="form-check-input">
                                </th>
                                <th scope="col">Kategori</th>
                                <th scope="col">Stok Kodu</th>
                                <th scope="col">Ürün Adı</th>
                                <th scope="col">Varyant</th>
                                <th scope="col">Stok</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-primary" id="addSelectedProducts">Seçilen Ürünleri Ekle</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    // Sistem ayarından ondalık basamak sayısını al
    const DECIMAL_PLACES = {{ getDecimalPlaces() }};

    // Para formatla helper fonksiyonu
    function formatMoney(amount) {
        if (amount === null || amount === undefined || amount === '') {
            return '0' + ','.repeat(DECIMAL_PLACES > 0 ? 1 : 0) + '0'.repeat(DECIMAL_PLACES);
        }
        return parseFloat(amount).toLocaleString('tr-TR', {
            minimumFractionDigits: DECIMAL_PLACES,
            maximumFractionDigits: DECIMAL_PLACES
        });
    }

    $(document).ready(function() {
        const $currentId = $('#current_id');
        const $exchangeRateId = $('#exchange_rate_id');
        const $warehouseId = $('#warehouse_id');
        const $addProductBtn = $('#addProductBtn');
        const hasApprovedProducts = @json($hasLockedProducts);
        if (hasApprovedProducts) {
            disableFormForApprovedProducts();
        }
        updateExchangeRateDisplay();
        updateCurrentAddress();
        setTimeout(() => calculateAllProducts(), 100);
        $currentId.on('change', updateCurrentAddress);
        $exchangeRateId.on('change', function() {
            updateExchangeRateDisplay();
            calculateAllProducts();
        });

        $addProductBtn.on('click', handleAddProduct);
        $('#addSelectedProducts').on('click', handleAddSelectedProducts);
        $('#stockProductModal').on('hidden.bs.modal', function() {
            $('.select-product-checkbox').prop('checked', false);
            $('#selectAllStockProducts').prop('checked', false);
        });

        $(document).on('change', '#productTable .product-quantity, #productTable .product-price, #productTable input[name$="[vat_rate]"], #productTable select[name$="[vat_status]"], #productTable .product-currency', function() {
            const $row = $(this).closest('tr');
            const exchangeRate = parseFloat($row.find('.product-currency option:selected').data('selling-rate')) || 1;
            $row.find('.product-exchange-rate').val(exchangeRate);
            calculateAllProducts();
        });

        $(document).on('click', '#productTable .remove-product', function() {
            $(this).closest('tr').remove();
            calculateAllProducts();
        });

        $('#offerGivenForm').on('submit', handleFormSubmit);

        function disableFormForApprovedProducts() {
            const fieldsToDisable = ['#warehouse_id', '#offer_date', '#exchange_rate_id', '#current_id', '#shipping_address', '#payment_type_id', '#offer_deadline', 'textarea[name="notes"]','#offerGivenSaveBtn'];
            fieldsToDisable.forEach(field => $(field).prop('disabled', true));
            $addProductBtn.prop('disabled', true).css({'opacity': '0.5', 'cursor': 'not-allowed'});

            $('#productTable input, #productTable select, #productTable textarea').prop('disabled', true);
            $('#productTable .remove-product').prop('disabled', true).css({'opacity': '0.5', 'cursor': 'not-allowed'});

            Swal.fire({
                text: 'Bu verilen teklifte onaylanmış ürünler bulunduğu için  verilen teklif bilgileri değiştirilemez.',
                icon: 'warning',
                confirmButtonText: 'Tamam'
            });
        }

        function updateExchangeRateDisplay() {
            const $selected = $exchangeRateId.find('option:selected');
            const rate = $selected.data('selling-rate') || '';
            const symbol = $selected.data('symbol') || '';

            $('#selling_rate').val(rate ? parseFloat(rate).toFixed(4).replace('.', ',') : '');
            $('#currency_symbol').text(symbol);

            $('#currency_symbol_total_fx, #currency_symbol_vat_fx, #currency_symbol_total_amount_fx').text(symbol);
        }

        function updateCurrentAddress() {
            const $selected = $currentId.find('option:selected');
            const address = $selected.data('address') || '';
            $('#address').val(address);
        }



        function calculateAllProducts() {
            const formData = collectFormData();

            $.ajax({
                url: "{{ route('backend.offer_given_calculate_totals') }}",
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        updateTotalsDisplay(response.data);

                        // Satır güncelleme
                        if (response.data.product_rows) {
                            Object.keys(response.data.product_rows).forEach(function(stockId) {
                                var row = $('#productTable tr[data-id="' + stockId + '"]');
                                var rowData = response.data.product_rows[stockId];
                                // Gizli inputlar
                                row.find('input[name="products['+stockId+'][amount]"]').val(rowData.total_amount.replace(/\./g, '').replace(',', '.'));
                                row.find('input[name="products['+stockId+'][vat_amount]"]').val(rowData.vat_amount_raw);
                                row.find('input[name="products['+stockId+'][total_amount]"]').val(rowData.total_amount_raw);
                                // Görünen tutar inputu
                                row.find('.product-amount').val(rowData.total_amount);
                            });
                        }
                    } else {
                        console.error('Hesaplama hatası:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX hatası:', error);
                }
            });
        }

        function collectFormData() {
            const products = {};
            const exchangeRateId = $exchangeRateId.val();

            $('#productTable tbody tr').each(function() {
                const $row = $(this);
                const stockId = $row.data('id');

                if (stockId) {
                    products[stockId] = {
                        quantity: $row.find('.product-quantity').val() || 1,
                        price: ($row.find('.product-price').val() || '0.00').replace('.', ','),
                        vat_rate: $row.find('input[name$="[vat_rate]"]').val() || 0,
                        vat_status: $row.find('select[name$="[vat_status]"]').val() || 0,
                        exchange_rate_id: $row.find('.product-currency').val() || exchangeRateId
                    };
                }
            });

            return {
                products: products,
                exchange_rate_id: exchangeRateId,
                _token: $('meta[name="csrf-token"]').attr('content')
            };
        }

        function updateTotalsDisplay(data) {
            $('#total_price').val(data.total_price);
            $('#vat_amount').val(data.vat_amount);
            $('#total_amount').val(data.total_amount);

            $('#total_price_fx').val(data.total_price_fx);
            $('#vat_amount_fx').val(data.vat_amount_fx);
            $('#total_amount_fx').val(data.total_amount_fx);

            $('#currency_symbol_total_fx, #currency_symbol_vat_fx, #currency_symbol_total_amount_fx').text(data.currency_symbol);

            const convertForHidden = (value) => value.replace(/\./g, '').replace(',', '.');
            $('#total_price_fx_hidden').val(convertForHidden(data.total_price_fx));
            $('#vat_amount_fx_hidden').val(convertForHidden(data.vat_amount_fx));
            $('#total_amount_fx_hidden').val(convertForHidden(data.total_amount_fx));
        }

        function handleAddProduct() {
            const selectedWarehouseId = $warehouseId.val();
            if (!selectedWarehouseId) {
                Swal.fire({
                    text: 'Lütfen depo seçiniz',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                });
                return;
            }

            loadStockProducts();
            $('#stockProductModal').modal('show');
        }

        function handleAddSelectedProducts() {
            const $selectedCheckboxes = $('.select-product-checkbox:checked');
            if ($selectedCheckboxes.length === 0) {
                Swal.fire({
                    text: 'Lütfen en az bir ürün seçiniz.',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                });
                return;
            }

            const selectedIds = [];
            $selectedCheckboxes.each(function() {
                selectedIds.push($(this).val());
            });

            $.ajax({
                url: "{{ route('backend.add_selected_offer_given_products') }}",
                type: 'POST',
                data: {
                    selected_ids: selectedIds,
                    offer_number: $('#js_offer_number').val(),
                    current_product_count: $('#productTable tbody tr').length,
                    exchange_rate_id: $exchangeRateId.val(),
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#productTable tbody').append(response.html);

                        calculateAllProducts();

                        $('#stockProductModal').modal('hide');
                        $('.select-product-checkbox').prop('checked', false);
                        $('#selectAllStockProducts').prop('checked', false);
                    } else {
                        Swal.fire({
                            text: response.message || 'Ürünler eklenirken bir hata oluştu.',
                            icon: 'error',
                            confirmButtonText: 'Tamam'
                        });
                    }
                },
                error: function(xhr) {
                    Swal.fire({
                        text: 'Ürünler eklenirken bir hata oluştu.',
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                }
            });
        }



        function loadStockProducts() {
            const selectedWarehouseId = $warehouseId.val();

            if ($.fn.DataTable.isDataTable('#stockProductModalTable')) {
                $('#stockProductModalTable').DataTable().destroy();
            }

            BaseCRUD.selector = "#stockProductModalTable";
            BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.general_stock_list') }}?datatable=true",
                    type: 'POST',
                    data: {
                        warehouse_id: selectedWarehouseId
                    },
                    headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }
                },
                columns: [
                    {
                        data: null,
                        orderable: false,
                        className: 'text-center',
                        render: function(data, type, row) {
                            return `<input type="checkbox" class="form-check-input select-product-checkbox" value="${row.id}">`;
                        }
                    },
                    { data: 'category', name: 'category', className: 'text-center' },
                    { data: 'stock_code', name: 'stock_code', className: 'text-center' },
                    { data: 'product_name', name: 'product_name', className: 'text-center' },
                    { data: 'variant_name', name: 'variant_name', className: 'text-center' },
                    { data: 'available_stock', name: 'available_stock', className: 'text-center' }
                ],
                order: [[1, 'desc']],
                pageLength: 5,
                initComplete: function() {
                    // Hepsini seç checkbox'ı için event handler
                    $('#selectAllStockProducts').on('change', function() {
                        const isChecked = $(this).prop('checked');
                        $('.select-product-checkbox').prop('checked', isChecked);
                    });

                    // Tek tek checkbox'lar için event handler
                    $(document).on('change', '.select-product-checkbox', function() {
                        const totalCheckboxes = $('.select-product-checkbox').length;
                        const checkedCheckboxes = $('.select-product-checkbox:checked').length;
                        $('#selectAllStockProducts').prop('checked', totalCheckboxes === checkedCheckboxes);
                    });
                }
            });
        }

        function handleFormSubmit(e) {
            const selectedCurrency = $exchangeRateId.find('option:selected').data('currency-code');
            if (selectedCurrency !== 'TRY') {
                $('#total_price_fx_hidden').val($('#total_price_fx').val().replace(/\./g, '').replace(',', '.'));
                $('#vat_amount_fx_hidden').val($('#vat_amount_fx').val().replace(/\./g, '').replace(',', '.'));
                $('#total_amount_fx_hidden').val($('#total_amount_fx').val().replace(/\./g, '').replace(',', '.'));
            }
        }

        @if (old('products'))
            setTimeout(() => calculateAllProducts(), 200);
        @endif
    });
</script>
@endsection
