<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Warehouse extends BaseModel
{
    use SoftDeletes;

    protected $table = 'warehouses';

    protected $guarded = [];

    public function warehouseType()
    {
        return $this->belongsTo(WarehouseType::class, 'warehouse_type_id');
    }

    public function locations()
    {
        return $this->hasMany(WarehouseLocation::class, 'warehouse_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'warehouse_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'warehouse_id');
    }

    public function targetStockMovements()
    {
        return $this->hasMany(StockMovement::class, 'target_warehouse_id');
    }

    public function physicalCounts()
    {
        return $this->hasMany(PhysicalCount::class, 'warehouse_id');
    }
}
