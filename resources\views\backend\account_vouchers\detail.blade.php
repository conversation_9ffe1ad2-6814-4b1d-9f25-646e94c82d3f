@extends('layout.layout')

@php
    $title = $container->title . ' Detay';
    $subTitle = $container->title . ' Detay';
@endphp

@section('content')
<style>
    .info-card {
        height: 100%;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        border: 1px solid;
    }
    .status-active {
        background-color: #d4edda;
        color: #155724;
        border-color: #c3e6cb;
    }
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
    }
    
    @media print {
        .btn, .card-header {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-body {
            padding: 0 !important;
        }
        body {
            background: white !important;
        }
    }
</style>

<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fs-6">
                    {{ $subTitle }}
                </h5>
                <div class="d-flex gap-2">
                    <a href="{{ route('backend.' . $container->page . '_list') }}"
                        class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                        <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Özet Kartı -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-gradient-primary text-white">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h4 class="mb-1">{{ $item->voucher_no }}</h4>
                                        <p class="mb-0 opacity-75">{{ \Carbon\Carbon::parse($item->voucher_date)->format('d.m.Y') }} - {{ $item->current->title ?? '-' }}</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <h3 class="mb-0">{{ number_format($item->amount, 2, ',', '.') }} {{ $item->exchangeRate->symbol ?? '₺' }}</h3>
                                        <small class="opacity-75">Fiş Tutarı</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fiş Bilgileri -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Fiş Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Fiş No:</strong> {{ $item->voucher_no }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Fiş Tarihi:</strong> {{ \Carbon\Carbon::parse($item->voucher_date)->format('d.m.Y') }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Vade Tarihi:</strong> {{ $item->due_date ? \Carbon\Carbon::parse($item->due_date)->format('d.m.Y') : '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Fiş Türü:</strong> {{ $item->paymentType->name ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Referans No:</strong> {{ $item->reference_no ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Durum:</strong>
                                        @php
                                            $statusClass = $item->is_active == 1 ? 'status-active' : 'status-inactive';
                                            $statusText = $item->is_active == 1 ? 'Aktif' : 'Pasif';
                                        @endphp
                                        <span class="status-badge {{ $statusClass }}">
                                            {{ $statusText }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Cari Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Cari Adı:</strong> {{ $item->current->title ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Cari Kodu:</strong> {{ $item->current->current_code ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Telefon:</strong> {{ $item->current->phone ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>E-posta:</strong> {{ $item->current->email ?? '-' }}
                                    </div>
                                    <div class="col-md-12 mb-2">
                                        <strong>Adres:</strong> {{ $item->current->address ?? '-' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tutar Bilgileri -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Tutar Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Tutar:</strong> 
                                        <span class="text-primary fw-bold">{{ number_format($item->amount, 2, ',', '.') }} {{ $item->exchangeRate->symbol ?? '₺' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Para Birimi:</strong> 
                                        <span class="text-info">{{ $item->exchangeRate->code ?? 'TRY' }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Döviz Kuru:</strong> 
                                        <span class="text-warning">{{ $item->exchangeRate->selling_rate }}</span>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>TL Karşılığı:</strong> 
                                        <span class="text-success fw-bold">{{ number_format($item->amount * ($item->exchangeRate->selling_rate ?? 1), 2, ',', '.') }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card info-card">
                            <div class="card-body">
                                <h6 class="mb-3">Banka Bilgileri</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <strong>Banka Adı:</strong> {{ $item->bank_name ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Banka Şubesi:</strong> {{ $item->bank_branch ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Hesap No:</strong> {{ $item->account_no ?? '-' }}
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <strong>Belge:</strong> 
                                        @if($item->document_file)
                                            <a href="{{ asset('upload/account_vouchers/' . $item->document_file) }}" target="_blank" class="text-primary">
                                                <iconify-icon icon="lucide:file-text"></iconify-icon> Görüntüle
                                            </a>
                                        @else
                                            -
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Açıklama -->
                @if($item->description)
                <div class="card mb-4">
                    <div class="card-body">
                        <h6 class="mb-3">Açıklama</h6>
                        <p class="mb-0">{{ $item->description }}</p>
                    </div>
                </div>
                @endif

                <!-- Sistem Bilgileri -->
                <div class="card">
                    <div class="card-body">
                        <h6 class="mb-3">Sistem Bilgileri</h6>
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <strong>Oluşturulma Tarihi:</strong><br>
                                {{ $item->created_at ? \Carbon\Carbon::parse($item->created_at)->format('d.m.Y H:i') : '-' }}
                            </div>
                            <div class="col-md-3 mb-2">
                                <strong>Güncellenme Tarihi:</strong><br>
                                {{ $item->updated_at ? \Carbon\Carbon::parse($item->updated_at)->format('d.m.Y H:i') : '-' }}
                            </div>
                            <div class="col-md-3 mb-2">
                                <strong>Oluşturan:</strong><br>
                                {{ $item->createdBy->name ?? '-' }}
                            </div>
                            <div class="col-md-3 mb-2">
                                <strong>Güncelleyen:</strong><br>
                                {{ $item->updatedBy->name ?? '-' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
