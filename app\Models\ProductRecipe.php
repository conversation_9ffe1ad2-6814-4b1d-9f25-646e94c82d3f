<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ProductRecipe extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_recipes';

    protected $guarded = [];

    public function products()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function productRecipeItems()
    {
        return $this->hasMany(ProductRecipeItem::class, 'product_recipe_id');
    }
}
