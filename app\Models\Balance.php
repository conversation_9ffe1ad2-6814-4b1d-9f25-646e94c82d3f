<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Balance extends BaseModel
{
    use SoftDeletes;

    protected $table = 'balances';

    protected $guarded = [];

    protected $casts = [
        'debit_balance' => 'float',
        'credit_balance' => 'float'
    ];

    public function current()
    {
        return $this->belongsTo(Current::class);
    }
    public function balanceCurrencies()
    {
        return $this->hasMany(BalanceCurrency::class);
    }
}

