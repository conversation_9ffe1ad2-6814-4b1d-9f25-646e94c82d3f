<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

class ExportCurrentStockMovement
{
    protected $items;

    public function __construct($items)
    {
        $this->items = $items;
    }

    public function export(): Spreadsheet
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Cari Stok Hareketleri');

        // Sayfa ayarları (isteğe bağlı)
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(0);

        $sheet->getPageMargins()
            ->setTop(0.5)
            ->setRight(0.5)
            ->setBottom(0.5)
            ->setLeft(0.5)
            ->setHeader(0.2)
            ->setFooter(0.3);
        $sheet->getPageSetup()->setHorizontalCentered(true); // Yatayda ortala
        // Sütun genişliği ayarları
        foreach (range('A', 'I') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Başlık stili
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['argb' => 'FFFFFFFF'], 'size' => 12],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Veri stili
        $valueStyle = [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];

        // Başlıklar
        $headers = [
            'İşlem No',
            'Cari',
            'Ürün',
            'Tarih',
            'İşlem Tipi',
            'Miktar',
            'Birim Fiyat',
            // 'Toplam',
            //'Detay',
        ];

        // Başlıkları yaz
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue("{$col}1", $header);
            $sheet->getStyle("{$col}1")->applyFromArray($headerStyle);
            $col++;
        }

        // Satırları yaz
        $rowNum = 2;
        foreach ($this->items as $item) {
            $col = 'A';

            // İşlem No
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['invoice_no'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Cari
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['current_name'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Ürün
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['product_name'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Tarih
            $dateFormatted = '';
            if (isset($item['invoice_date']) && $item['invoice_date']) {
                try {
                    $dateFormatted = $item['invoice_date']; // Zaten formatlanmış geliyor
                } catch (\Exception $e) {
                    $dateFormatted = '';
                }
            }
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $dateFormatted, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // İşlem Tipi
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $item['transaction_type_name'] ?? '', DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Miktar
            $quantity = $item['quantity'] ?? 0;
            $formattedQuantity = number_format((float)$quantity, 2, ',', '.');

            // İşlem tipine göre + veya - işareti ekle
            if (isset($item['transaction_type'])) {
                if ($item['transaction_type'] === 'sales') {
                    $formattedQuantity = '-' . $formattedQuantity;
                } else if ($item['transaction_type'] === 'purchase') {
                    $formattedQuantity = '+' . $formattedQuantity;
                }
            }

            $sheet->setCellValueExplicit("{$col}{$rowNum}", $formattedQuantity, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Birim Fiyat
            $unitPrice = $item['unit_price'] ?? 0;
            $formattedUnitPrice = number_format((float)$unitPrice, 2, ',', '.') . ' ₺';
            $sheet->setCellValueExplicit("{$col}{$rowNum}", $formattedUnitPrice, DataType::TYPE_STRING);
            $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            $col++;

            // Toplam
            // $total = $item['total'] ?? 0;
            // $formattedTotal = number_format((float)$total, 2, ',', '.') . ' ₺';
            // $sheet->setCellValueExplicit("{$col}{$rowNum}", $formattedTotal, DataType::TYPE_STRING);
            // $sheet->getStyle("{$col}{$rowNum}")->applyFromArray($valueStyle);
            // $col++;
            $col++;

            $rowNum++;
        }

        return $spreadsheet;
    }
}
