@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? $item->name . ' Düzenle' : 'Yeni ' . $container->title . ' Ekle';
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                @csrf

                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Depo <span class="text-danger">*</span></label>
                        <select name="warehouse_id" class="form-select select2" required>
                            <option value="">Seçiniz</option>
                            @foreach($warehouses as $warehouse)
                                <option value="{{ $warehouse->id }}" {{ old('warehouse_id', $item->warehouse_id) == $warehouse->id ? 'selected' : '' }}>
                                    {{ $warehouse->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('warehouse_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">Üst Lokasyon</label>
                        <select name="parent_id" class="form-select select2">
                            <option value="">Ana Lokasyon</option>
                            @foreach($locations as $location)
                                @if($location->id != $item->id)
                                    <option value="{{ $location->id }}" {{ old('parent_id', $item->parent_id) == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endif
                            @endforeach
                        </select>
                        @error('parent_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">Lokasyon Kodu</label>
                        <input type="text" name="code" class="form-control" value="{{ old('code', $item->code) }}">
                        @error('code')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-8">
                        <label class="form-label">Lokasyon Adı <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" value="{{ old('name', $item->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">Durum</label>
                        <select name="is_active" class="form-select">
                            <option value="1" {{ old('is_active', $item->is_active) == 1 ? 'selected' : '' }}>Aktif</option>
                            <option value="0" {{ old('is_active', $item->is_active) == 0 ? 'selected' : '' }}>Pasif</option>
                        </select>
                        @error('is_active')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label class="form-label">Açıklama</label>
                        <textarea name="description" class="form-control" rows="3">{{ old('description', $item->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Maks. Ağırlık Kapasitesi (kg)</label>
                        <input type="number" step="0.001" name="max_weight_capacity" class="form-control" value="{{ old('max_weight_capacity', $item->max_weight_capacity) }}" min="0">
                        @error('max_weight_capacity')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Maks. Hacim Kapasitesi (m³)</label>
                        <input type="number" step="0.0001" name="max_volume_capacity" class="form-control" value="{{ old('max_volume_capacity', $item->max_volume_capacity) }}" min="0">
                        @error('max_volume_capacity')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                    <a href="{{ route('backend.'.$container->page.'_list') }}" class="btn btn-secondary">İptal</a>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var allLocations = [
                @foreach($locations as $location)
                    {
                        id: {{ $location->id }},
                        name: "{{ $location->name }}",
                        warehouse_id: {{ $location->warehouse_id }}
                    },
                @endforeach
            ];

            $('select[name="warehouse_id"]').on('change', function() {
                var warehouseId = $(this).val();
                var parentSelect = $('select[name="parent_id"]');

                parentSelect.html('<option value="">Ana Lokasyon</option>');

                if (warehouseId) {
                    var filteredLocations = allLocations.filter(function(location) {
                        return location.warehouse_id == warehouseId;
                    });

                    filteredLocations.forEach(function(location) {
                        @if($item->id)
                            if (location.id != {{ $item->id }}) {
                                parentSelect.append('<option value="' + location.id + '">' + location.name + '</option>');
                            }
                        @else
                            parentSelect.append('<option value="' + location.id + '">' + location.name + '</option>');
                        @endif
                    });
                }
            });

            var initialWarehouseId = $('select[name="warehouse_id"]').val();

            if (initialWarehouseId) {
                var currentParentId = $('select[name="parent_id"]').val();

                $('select[name="warehouse_id"]').trigger('change');

                if (currentParentId) {
                    $('select[name="parent_id"] option[value="' + currentParentId + '"]').prop('selected', true);
                }
            }
        });
    </script>
@endsection
