<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TransactionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $types = [
                [
                    'name' => 'Ödeme İşlemleri',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
                [
                    'name' => 'Tahsilat İşlemleri',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ],
            ];

            DB::table('transaction_types')->upsert(
                $types,
                ['name'],
                ['created_at', 'updated_at']
            );
        });

        $this->command->info('TransactionType verileri başarıyla oluşturuldu.');
    }
}
