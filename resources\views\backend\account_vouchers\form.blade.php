@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON>üzenle');
@endphp
@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        {{-- <input type="hidden" name="id" value="{{ $item->id ?? '' }}"> --}}

                        <div class="row gy-3">
                            <!-- Fiş No -->
                            <div class="col-md-4">
                                <label class="form-label">Fiş No</label>
                                <input type="text" class="form-control" id="voucher_no" name="voucher_no"
                                    value="{{ old('voucher_no', $item->id ? $item->voucher_no : \App\Models\AccountVoucher::generatePreviewAccountVoucherNumber()) }}"
                                    {{ $item->id ? 'readonly' : '' }} placeholder="Otomatik oluşturulacak" readonly>
                            </div>

                            <!-- Fiş Tarihi -->
                            <div class="col-md-4">
                                <label class="form-label">Fiş Tarihi</label>
                                <input type="date" class="form-control" name="voucher_date"
                                    value="{{ old('voucher_date', $item->voucher_date ? date('Y-m-d', strtotime($item->voucher_date)) : date('Y-m-d')) }}"
                                    required>
                                <x-form-error field="voucher_date" />
                            </div>

                            <!-- Vade Tarihi -->
                            <div class="col-md-4">
                                <label class="form-label">Vade Tarihi</label>
                                <input type="date" class="form-control" name="due_date"
                                    value="{{ old('due_date', $item->due_date ? date('Y-m-d', strtotime($item->due_date)) : '') }}">
                                <x-form-error field="due_date" />
                            </div>

                            <!-- Cari -->
                            <div class="col-md-6">
                                <label class="form-label">Cari</label>
                                <select class="form-control select2" name="current_id">
                                    <option value="">Cari Seç</option>
                                    @foreach ($currents as $current)
                                        <option value="{{ $current->id }}"
                                            {{ old('current_id', $item->current_id) == $current->id ? 'selected' : '' }}>
                                            {{ $current->title }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-form-error field="current_id" />
                            </div>

                            <!-- Fiş Türü -->
                            <div class="col-md-6">
                                <label class="form-label">Fiş Türü</label>
                                <select class="form-control select2" name="payment_type_id">
                                    <option value="">Fiş Türü Seç</option>
                                    @foreach ($paymentType as $plan)
                                        <option value="{{ $plan->id }}" data-branch-id="{{ $plan->branch_id ?? '' }}"
                                            {{-- data-branch-id="{{ $plan->branch_id ?? '' }}" --}}
                                            {{ old('payment_type_id', $item->payment_type_id) == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-form-error field="payment_type_id" />
                            </div>

                            <!-- Tutar -->
                            <div class="col-md-4">
                                <label class="form-label">Tutar</label>
                                <input type="number" step="0.01" class="form-control" name="amount"
                                    value="{{ old('amount', $item->amount ?? 0) }}">
                                <x-form-error field="amount" />
                            </div>

                            <!-- Para Birimi -->
                            <div class="col-md-4">
                                <label class="form-label">Para Birimi</label>
                                <select class="form-control" id="currency_selector">
                                    <option value="">Para Birimi Seç</option>
                                    @foreach ($exchangeRates as $rate)
                                        <option value="{{ $rate->id }}" data-rate="{{ $rate->buying_rate }}" data-code="{{ $rate->code }}"
                                            {{ old('exchange_rate_id', $item->exchange_rate_id ?? 1) == $rate->id ? 'selected' : '' }}>
                                            {{ $rate->code }}({{$rate->symbol}})
                                        </option>
                                    @endforeach
                                </select>
                                <input type="hidden" class="form-control" name="exchange_rate_id"
                                    value="{{ old('exchange_rate_id', $item->exchange_rate_id ?? 1) }}">
                                <x-form-error field="exchange_rate_id" />
                            </div>

                            <!-- Referans No -->
                            <div class="col-md-6">
                                <label class="form-label">Referans No (Çek/Senet No vb.)</label>
                                <input type="number" class="form-control" name="reference_no"
                                    value="{{ old('reference_no', $item->reference_no ?? '') }}">
                            </div>

                            <!-- Açıklama -->
                            <div class="col-md-6">
                                <label class="form-label">Açıklama</label>
                                <input type="text" class="form-control" name="description"
                                    value="{{ old('description', $item->description ?? '') }}">
                            </div>

                            <!-- Banka Adı -->
                            <div class="col-md-4">
                                <label class="form-label">Banka Adı</label>
                                <input type="text" class="form-control" name="bank_name"
                                    value="{{ old('bank_name', $item->bank_name ?? '') }}">
                            </div>

                            <!-- Banka Şubesi -->
                            <div class="col-md-4">
                                <label class="form-label">Banka Şubesi</label>
                                <input type="text" class="form-control" name="bank_branch"
                                    value="{{ old('bank_branch', $item->bank_branch ?? '') }}">
                            </div>

                            <!-- Hesap No -->
                            <div class="col-md-4">
                                <label class="form-label">Hesap No</label>
                                <input type="number" class="form-control" name="account_no"
                                    value="{{ old('account_no', $item->account_no ?? '') }}">
                            </div>
                        </div>

                        <div class="mt-5 text-end">
                            <a href="{{ route('backend.account_vouchers_list') }}" class="btn btn-secondary">İptal</a>
                            <button type="submit" class="btn btn-primary">Kaydet</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const currencySelector = document.getElementById('currency_selector');
            const currencyInput = document.querySelector('input[name="exchange_rate_id"]');

            function updateCurrency() {
                const selectedOption = currencySelector.options[currencySelector.selectedIndex];
                const exchangeRateId = selectedOption.value;
                currencyInput.value = exchangeRateId;
            }

            currencySelector.addEventListener('change', updateCurrency);
            updateCurrency();
        });
    </script>
@endsection
