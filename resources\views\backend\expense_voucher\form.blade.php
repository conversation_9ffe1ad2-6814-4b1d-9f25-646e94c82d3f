@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' Düzenle');
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', $item->id ?? null) }}" method="POST"
                enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Fiş No</label>
                            <input type="text" class="form-control" id="voucher_no" name="voucher_no"
                                value="{{ old('voucher_no', $item->id ? $item->voucher_no : \App\Models\ExpenseVoucher::generatePreviewExpenseVoucherNumber()) }}"
                                {{ $item->id ? 'readonly' : '' }} placeholder="Otomatik oluşturulacak" readonly>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="voucher_date" class="form-label">Fiş Tarihi <span
                                    class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="voucher_date" name="voucher_date"
                                value="{{ old('voucher_date', $item->voucher_date?->format('Y-m-d')) }}">
                            <x-form-error field="voucher_date" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="payment_type_id" class="form-label">Ödeme Türü <span
                                    class="text-danger">*</span></label>
                            <select class="form-select select2" id="payment_type_id" name="payment_type_id">
                                <option value="">Ödeme Türü Seçiniz</option>
                                @foreach ($paymentTypes as $paymentType)
                                    <option value="{{ $paymentType->id }}"
                                        {{ old('payment_type_id', $item->payment_type_id) == $paymentType->id ? 'selected' : '' }}>
                                        {{ $paymentType->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-form-error field="payment_type_id" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="expense_type_id" class="form-label">Masraf Türü <span
                                    class="text-danger">*</span></label>
                            <select class="form-select select2" id="expense_type_id" name="expense_type_id">
                                <option value="">Masraf Türü Seçiniz</option>
                                @foreach ($expenseTypes as $expenseType)
                                    <option value="{{ $expenseType->id }}"
                                        {{ old('expense_type_id', $item->expense_type_id) == $expenseType->id ? 'selected' : '' }}>
                                        {{ $expenseType->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-form-error field="expense_type_id" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">Durum</label>
                            <select class="form-select select2" id="expense_voucher_statu_id" name="expense_voucher_statu_id">
                                @foreach ($expenseVoucherStatu as $status)
                                    <option value="{{ $status->id }}"
                                        {{ old('expense_voucher_statu_id', $item->expense_voucher_statu_id) == $status->id ? 'selected' : '' }}>
                                        {{ $status->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-form-error field="expense_voucher_statu_id" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="description" class="form-label">Açıklama</label>
                            <textarea class="form-control" id="description" name="description" rows="1">{{ old('description', $item->description) }}</textarea>
                            <x-form-error field="description" />
                        </div>
                    </div>
                </div>

                <div class="row" id="cancellation_reason_container"
                    style="{{ old('expense_voucher_statu_id', $item->expense_voucher_statu_id) == 3 ? '' : 'display: none;' }}">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="reason_for_cancellation" class="form-label">İptal Sebebi <span
                                    class="text-danger">*</span></label>
                            <textarea class="form-control" id="reason_for_cancellation" name="reason_for_cancellation" rows="3">{{ old('reason_for_cancellation', $item->reason_for_cancellation ?? null) }}</textarea>
                            <x-form-error field="reason_for_cancellation" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="subtotal" class="form-label">Ara Toplam <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="subtotal" name="subtotal"
                                value="{{ old('subtotal', $item->subtotal) }}">
                            <x-form-error field="subtotal" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="vat_rate" class="form-label">KDV Oranı <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" min="0" max="100" class="form-control" id="vat_rate" name="vat_rate"
                                value="{{ old('vat_rate', $item->vat_rate) }}">
                            <x-form-error field="vat_rate" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="vat_total" class="form-label">KDV Tutarı</label>
                            <input type="number" step="0.01" class="form-control" id="vat_total" name="vat_total"
                                value="{{ old('vat_total', $item->vat_total) }}">
                            <x-form-error field="vat_total" />
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="grand_total" class="form-label">Genel Toplam <span
                                    class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="grand_total"
                                name="grand_total" value="{{ old('grand_total', $item->grand_total) }}">
                            <x-form-error field="grand_total" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 text-end"> <a href="{{ route('backend.' . $container->page . '_list') }}"
                            class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>İptal
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Kaydet
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@section('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const subtotalInput = document.getElementById('subtotal');
            const vatRateInput = document.getElementById('vat_rate');
            const vatTotalInput = document.getElementById('vat_total');
            const grandTotalInput = document.getElementById('grand_total');
            const statusSelect = document.getElementById('expense_voucher_statu_id');
            const cancellationReasonContainer = document.getElementById('cancellation_reason_container');
            const reasonTextarea = document.getElementById('reason_for_cancellation');

            function calculateVAT() {
                const subtotal = parseFloat(subtotalInput.value) || 0;
                const vatRate = parseFloat(vatRateInput.value) || 0;

                const vatTotal = subtotal * (vatRate / 100);
                const grandTotal = subtotal + vatTotal;

                vatTotalInput.value = vatTotal.toFixed(2);
                grandTotalInput.value = grandTotal.toFixed(2);
            }

            function toggleCancellationReason() {
                if (statusSelect.value == '3') {
                    cancellationReasonContainer.style.display = 'block';
                } else {
                    cancellationReasonContainer.style.display = 'none';

                    if (reasonTextarea.classList.contains('is-invalid')) {
                        reasonTextarea.classList.remove('is-invalid');
                        const errorDiv = reasonTextarea.parentElement.querySelector('.invalid-feedback');
                        if (errorDiv) {
                            errorDiv.remove();
                        }
                    }
                }
            }

            subtotalInput.addEventListener('input', calculateVAT);
            vatRateInput.addEventListener('input', calculateVAT);
            statusSelect.addEventListener('change', toggleCancellationReason);

            // İlk yüklemede çalıştır (özellikle validasyon sonrası)
            calculateVAT();
            toggleCancellationReason();
        });
    </script>
@endsection