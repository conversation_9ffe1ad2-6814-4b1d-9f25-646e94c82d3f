<?php

namespace Database\Seeders;

use App\Models\StockMovementReason;
use App\Models\StockMovementType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StockMovementReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createReasons();
        });

        $this->command->info('StockMovementReason verileri başarıyla oluşturuldu.');
    }

    /**
     * Hareket nedenlerini oluştur
     */
    private function createReasons(): void
    {
        // Hareket tiplerini al
        $girisType = StockMovementType::where('name', 'Giriş')->first();
        $cikisType = StockMovementType::where('name', 'Çıkış')->first();
        $transferType = StockMovementType::where('name', 'Transfer')->first();

        if (!$girisType || !$cikisType || !$transferType) {
            $this->command->warn('StockMovementType kayıtları bulunamadı! Önce StockMovementTypeSeeder\'ı çalıştırın.');
            return;
        }

        $reasons = [
            // Giriş nedenleri
            [
                'name' => 'Alım',
                'description' => 'Mal alımı',
                'movement_type_id' => $girisType->id
            ],
            [
                'name' => 'İade Giriş',
                'description' => 'Mal iadesi - Giriş',
                'movement_type_id' => $girisType->id
            ],
            [
                'name' => 'Transfer Giriş',
                'description' => 'Depolar arası transfer - Giriş',
                'movement_type_id' => $transferType->id
            ],
            [
                'name' => 'Sayım Fazlası',
                'description' => 'Sayım sonucu fazla',
                'movement_type_id' => $girisType->id
            ],
            [
                'name' => 'Numune Giriş',
                'description' => 'Numune - Giriş',
                'movement_type_id' => $girisType->id
            ],

            // Çıkış nedenleri
            [
                'name' => 'Satış',
                'description' => 'Mal satışı',
                'movement_type_id' => $cikisType->id
            ],
            [
                'name' => 'İade Çıkış',
                'description' => 'Mal iadesi - Çıkış',
                'movement_type_id' => $cikisType->id
            ],
            [
                'name' => 'Transfer Çıkış',
                'description' => 'Depolar arası transfer - Çıkış',
                'movement_type_id' => $transferType->id
            ],
            [
                'name' => 'Sayım Eksiği',
                'description' => 'Sayım sonucu eksik',
                'movement_type_id' => $cikisType->id
            ],
            [
                'name' => 'Numune Çıkış',
                'description' => 'Numune - Çıkış',
                'movement_type_id' => $cikisType->id
            ],
            [
                'name' => 'Fire',
                'description' => 'Fire kayıp',
                'movement_type_id' => $cikisType->id
            ],
            [
                'name' => 'Stok Tanımlama',
                'description' => 'Stok tanımlama',
                'movement_type_id' => $girisType->id
            ],
        ];

        foreach ($reasons as $reason) {
            StockMovementReason::updateOrCreate(
                [
                    'name' => $reason['name'],
                    'movement_type_id' => $reason['movement_type_id']
                ],
                $reason
            );
        }
    }
}
