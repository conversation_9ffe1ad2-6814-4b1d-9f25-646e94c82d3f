<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class ExpenseVoucher extends BaseModel
{
    use SoftDeletes;

    protected $table = 'expense_vouchers';

    protected $guarded = [];

    protected $casts = ['voucher_date' => 'date'];

    public function expenseType()
    {
        return $this->belongsTo(ExpenseType::class, 'expense_type_id');
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class, 'payment_type_id');
    }
    public function expenseVoucherStatu()
    {
        return $this->belongsTo(ExpenseVoucherStatu::class);
    }
    public static function generatePreviewExpenseVoucherNumber()
    {
        $currentDate = Carbon::now();
        $year = $currentDate->format('Y');
        $month = $currentDate->format('m');

        // <PERSON>linen kayıtlar da dahil, en yüksek fiş numarasını al
        $lastVoucher = ExpenseVoucher::withTrashed()
            ->whereYear('voucher_date', $year)
            ->whereMonth('voucher_date', $month)
            ->where('voucher_no', 'like', "MSF/{$month}/{$year}/%")
            ->orderBy('voucher_no', 'desc')
            ->first();

        if ($lastVoucher) {
            $lastNumberPart = substr($lastVoucher->voucher_no, strrpos($lastVoucher->voucher_no, '/') + 1);
            $nextNumber = intval($lastNumberPart) + 1;
        } else {
            $nextNumber = 1;
        }

        return "MSF/{$month}/{$year}/" . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
    public function scopeFilter($query, $filter)
    {
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('voucher_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        }
        return $query;
    }

    public static function bootHook()
    {
        static::deleting(function ($voucher) {
            if ($voucher->expense_voucher_statu_id == 2) {
                session()->flash('error', 'Onaylanmış masraf fişleri silinemez.');
                return false;
            }
        });
    }
}
