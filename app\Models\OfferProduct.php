<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OfferProduct extends BaseModel
{
    use SoftDeletes;

    protected $table = 'offer_products';

    protected $guarded = [];

    // Para formatlaması için accessor'lar
    public function getFormattedUnitPriceAttribute()
    {
        return formatMoneyNumber($this->unit_price);
    }

    public function getFormattedTotalPriceAttribute()
    {
        return formatMoneyNumber($this->total_price);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }

    public function offer()
    {
        return $this->belongsTo(Offer::class);
    }

    public function stock()
    {
        return $this->belongsTo(Stock::class);
    }

    public static function generateItemNo($offerNumber)
    {
        $prefix = $offerNumber . '-';
        $lastItemNo = self::withTrashed()->where('item_no', 'like', $prefix.'%')->latest('item_no')->first();
        if ($lastItemNo) {
            $lastSequence = (int)substr($lastItemNo->item_no, strrpos($lastItemNo->item_no, '-') + 1);
            $nextSequence = str_pad($lastSequence + 1, 2, '0', STR_PAD_LEFT);
        }
        else {
            $nextSequence = '01';
        }

        return $prefix . $nextSequence;
    }
}
