<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class WarehouseLocation extends BaseModel
{
    use SoftDeletes;

    protected $table = 'warehouse_locations';

    protected $guarded = [];

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function parent()
    {
        return $this->belongsTo(WarehouseLocation::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(WarehouseLocation::class, 'parent_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'warehouse_location_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'location_id');
    }

    public function targetStockMovements()
    {
        return $this->hasMany(StockMovement::class, 'target_location_id');
    }

    public function physicalCounts()
    {
        return $this->hasMany(PhysicalCount::class, 'location_id');
    }
}
