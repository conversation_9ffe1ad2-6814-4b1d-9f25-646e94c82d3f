<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ProductVariantController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ürün Varyantları';
        $this->page = 'product_variant';
        $this->model = new ProductVariant();
        $this->relation = ['product', 'stocks'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ürün Yönetimi' => '#',
                '<PERSON>rün Varyantları' => route('backend.product_variant_list'),
            ),
        );

        $this->validation = array(
            [
                'product_id' => 'required|exists:products,id',
                'name' => 'required|string|max:255',
                'sku' => 'nullable|string|max:100',
                'weight' => 'nullable|numeric|min:0|max:999999.999',
                'width' => 'nullable|numeric|min:0|max:999999.999',
                'height' => 'nullable|numeric|min:0|max:999999.999',
                'length' => 'nullable|numeric|min:0|max:999999.999',
                'volume' => 'nullable|numeric|min:0|max:999999.999',
                'barcode' => 'nullable|string|max:100',
                'purchase_price' => 'nullable|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0',
                'min_sale_price' => 'nullable|numeric|min:0',
                'purchase_currency_code' => 'nullable|string|max:3',
                'sale_currency_code' => 'nullable|string|max:3',
                'critical_stock_level' => 'nullable|numeric|min:0|max:999999.999',
                'max_stock_level' => 'nullable|numeric|min:0|max:999999.999',
                'description' => 'nullable|string|max:255',
                'is_active' => 'required|boolean',
            ],
            [
                'product_id.required' => 'Ürün seçilmedi.',
                'product_id.exists' => 'Seçilen ürün geçersiz.',
                'name.required' => 'Varyant adı zorunludur.',
                'name.string' => 'Varyant adı metin formatında olmalıdır.',
                'name.max' => 'Varyant adı en fazla 255 karakter olabilir.',
                'sku.string' => 'Varyant kodu metin formatında olmalıdır.',
                'sku.max' => 'Varyant kodu en fazla 100 karakter olabilir.',
                'weight.numeric' => 'Ağırlık sayısal olmalıdır.',
                'weight.min' => 'Ağırlık en az 0 olmalıdır.',
                'weight.max' => 'Ağırlık en fazla 999999.999 olmalıdır.',
                'width.numeric' => 'Genişlik sayısal olmalıdır.',
                'width.min' => 'Genişlik en az 0 olmalıdır.',
                'width.max' => 'Genişlik en fazla 999999.999 olmalıdır.',
                'height.numeric' => 'Yükseklik sayısal olmalıdır.',
                'height.min' => 'Yükseklik en az 0 olmalıdır.',
                'height.max' => 'Yükseklik en fazla 999999.999 olmalıdır.',
                'length.numeric' => 'Uzunluk sayısal olmalıdır.',
                'length.min' => 'Uzunluk en az 0 olmalıdır.',
                'length.max' => 'Uzunluk en fazla 999999.999 olmalıdır.',
                'volume.numeric' => 'Hacim sayısal olmalıdır.',
                'volume.min' => 'Hacim en az 0 olmalıdır.',
                'volume.max' => 'Hacim en fazla 999999.999 olmalıdır.',
                'barcode.string' => 'Barkod metin formatında olmalıdır.',
                'barcode.max' => 'Barkod en fazla 100 karakter olabilir.',
                'purchase_price.numeric' => 'Alış fiyatı sayısal olmalıdır.',
                'purchase_price.min' => 'Alış fiyatı en az 0 olmalıdır.',
                'sale_price.numeric' => 'Satış fiyatı sayısal olmalıdır.',
                'sale_price.min' => 'Satış fiyatı en az 0 olmalıdır.',
                'min_sale_price.numeric' => 'Minimum satış fiyatı sayısal olmalıdır.',
                'min_sale_price.min' => 'Minimum satış fiyatı en az 0 olmalıdır.',
                'purchase_currency_code.string' => 'Alış para birimi metin formatında olmalıdır.',
                'purchase_currency_code.max' => 'Alış para birimi en fazla 3 karakter olmalıdır.',
                'sale_currency_code.string' => 'Satış para birimi metin formatında olmalıdır.',
                'sale_currency_code.max' => 'Satış para birimi en fazla 3 karakter olmalıdır.',
                'critical_stock_level.numeric' => 'Kritik stok seviyesi sayısal olmalıdır.',
                'critical_stock_level.min' => 'Kritik stok seviyesi en az 0 olmalıdır.',
                'critical_stock_level.max' => 'Kritik stok seviyesi en fazla 999999.999 olmalıdır.',
                'max_stock_level.numeric' => 'Maksimum stok seviyesi sayısal olmalıdır.',
                'max_stock_level.min' => 'Maksimum stok seviyesi en az 0 olmalıdır.',
                'max_stock_level.max' => 'Maksimum stok seviyesi en fazla 999999.999 olmalıdır.',
                'description.string' => 'Açıklama metin formatında olmalıdır.',
                'description.max' => 'Açıklama en fazla 255 karakter olabilir.',
                'is_active.required' => 'Aktiflik durumu seçilmedi.',
                'is_active.boolean' => 'Aktiflik durumu geçersiz.',
            ],
        );

        view()->share('products', Product::active()->get());
        view()->share('currencies', ExchangeRate::active()->select('code', 'name')->distinct('code')->get());
        parent::__construct();
    }

    public function save(Request $request, $unique = null)
    {
        $validator = \Validator::make($request->all(), $this->validation[0], $this->validation[1]);

        if ($validator->fails()) {
            return $request->ajax()
                ? response()->json(['success' => false, 'errors' => $validator->errors()])
                : redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $params = $request->all();

            if (empty($params['sku'])) {
                $params['sku'] = ProductVariant::generateSku($params['product_id'], $params['name']);
            }

            if (!empty($params['width']) && !empty($params['height']) && !empty($params['length'])) {
                $params['volume'] = ($params['width'] * $params['height'] * $params['length']) / 1000000; // m³ cinsinden
            }

            if (!is_null($unique)) {
                $variant = ProductVariant::find($unique);
                if (!$variant) {
                    return $request->ajax()
                        ? response()->json(['success' => false, 'message' => 'Güncellenecek varyant bulunamadı.'])
                        : redirect()->back()->with('error', 'Güncellenecek varyant bulunamadı.');
                }
                $variant->update($params);
                $message = 'Ürün varyantı başarılı şekilde güncellendi';
            } else {
                $variant = ProductVariant::create($params);
                $message = 'Ürün varyantı başarılı şekilde oluşturuldu';
            }

            Cache::flush();

            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => $message,
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', $message);

        } catch (\Exception $e) {
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Varyant kaydedilirken bir hata oluştu'
                ])
                : redirect()->back()->with('error', 'Varyant kaydedilirken bir hata oluştu')->withInput();
        }
    }
}
