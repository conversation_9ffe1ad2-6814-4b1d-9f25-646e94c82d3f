<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Status;
use App\Models\Stock;
use App\Models\StockBatch;
use App\Models\StockMovement;
use App\Models\StockMovementItem;
use App\Models\StockMovementReason;
use App\Models\StockMovementType;
use App\Models\StockReservation;
use App\Models\Unit;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Services\ExchangeRateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class StockMovementController extends BaseController
{
    use BasePattern;

    protected array $tempProductData = [];

    public function __construct()
    {
        $this->title = 'Stok Hareketleri';
        $this->page = 'stock_movement';
        $this->model = new StockMovement();
        $this->relation = [
            'warehouse',
            'location',
            'targetWarehouse',
            'targetLocation',
            'current',
            'status',
            'stockMovementReason',
            'stockMovementType',
            'items.product.unit',
            'items.variant',
            'starter',
            'approver',
            'current'
        ];

        $this->view = (object) array(
            'breadcrumb' => array(
                'Stok Hareketleri' => route('backend.stock_movement_list'),
            ),
        );

        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('currents', Current::active()->get());
        view()->share('statuses', Status::active()->get());
        view()->share('movementReasons', StockMovementReason::active()->get());
        view()->share('movementTypes', StockMovementType::active()->get());
        view()->share('stocks', Stock::with(['product', 'variant', 'warehouse', 'warehouseLocation'])->active()->get());
        parent::__construct();
    }

    public function detail(Request $request, $unique = null)
    {
        $item = $this->model::with([
            'current',
            'starter',
            'approver',
            'status',
            'stockMovementReason',
            'stockMovementType',
            'warehouse',
            'location',
            'targetWarehouse',
            'targetLocation',
            'items' => function ($query) {
                $query->with(['product.unit', 'variant', 'unit', 'status']);
            },

        ])->find($unique);

        if (!$item) {
            return redirect()->back()->with('error', 'Stok hareketi bulunamadı');
        }

        if ($request->has('datatable') && $request->has('type')) {
            return $this->getMovementItemsDatatable($unique);
        }

        $isApproved = $item->status_id == 2;

        return view("backend.$this->page.detail", compact('item', 'unique', 'isApproved'));
    }

    private function getMovementItemsDatatable(int $movementId)
    {
        $select = StockMovementItem::where('stock_movement_id', $movementId)
            ->with(['product.unit', 'variant', 'status'])
            ->whereNull('deleted_at');

        return datatables()->of($select)
            ->addColumn('product_name', fn($item) => $item->product->name ?? '-')
            ->addColumn('variant_name', fn($item) => $item->variant->name ?? '-')
            ->editColumn('quantity', function ($item) {
                $unit = $item->product ? $item->product->unit : null;
                return formatQuantityWithUnit($item->quantity, $unit);
            })
            ->addColumn('unit_symbol', fn($item) => $item->product?->unit?->symbol ?? '-')
            ->editColumn('unit_price', function ($item) {
                if (!$item->unit_price || $item->unit_price == 0)
                    return '-';
                return number_format($item->unit_price, 2, ',', '.') . ' ' . ($item->currency_code ?? 'TRY');
            })
            ->editColumn('total_price', function ($item) {
                if (!$item->total_price || $item->total_price == 0)
                    return '-';
                return number_format($item->total_price, 2, ',', '.') . ' ' . ($item->currency_code ?? 'TRY');
            })
            ->addIndexColumn()
            ->make(true);
    }

    public function status(Request $request)
    {
        $movement = StockMovement::with(['items', 'stockMovementReason'])->find($request->id);

        if (!$movement) {
            return response()->json(['status' => false, 'message' => 'Hareket bulunamadı']);
        }

        if ($movement->items->isEmpty()) {
            return response()->json(['status' => false, 'message' => 'Hareket kalemleri bulunamadı']);
        }

        DB::beginTransaction();

        try {
            $previousStatus = $movement->status_id;
            $newStatus = $request->status;

            if ($previousStatus == $newStatus) {
                return response()->json(['status' => false, 'message' => 'Status zaten aynı']);
            }

            if ($previousStatus == 1 && $newStatus == 2) {
                $this->approveMovement($movement, $request);
            }

            $movement->status_id = $newStatus;
            $movement->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Durum güncellendi']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => false,
                'message' => 'Bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }
    private function approveMovement(StockMovement $movement, Request $request): void
    {
        $movement->approver_id = Auth::id();
        $movement->approval_date = now();

        $this->updateItemPricesAndExchangeRates($movement);

        $this->updateMovementTotalAmount($movement);

        $this->processStockMovement($movement);
    }

    private function updateItemPricesAndExchangeRates(StockMovement $movement): void
    {
        $exchangeRateService = new ExchangeRateService();

        foreach ($movement->items as $item) {
            if ((!$item->unit_price || $item->unit_price == 0) && $item->stock_id) {
                $stock = Stock::find($item->stock_id);
                if ($stock && $stock->unit_cost > 0) {
                    $item->unit_price = $stock->unit_cost;
                    $item->currency_code = $stock->currency_code ?? 'TRY';
                }
            }

            if (empty($item->currency_code)) {
                $item->currency_code = 'TRY';
            }

            if ($item->currency_code !== 'TRY') {
                $exchangeRate = ExchangeRate::where('code', $item->currency_code)
                    ->whereDate('rate_date', '<=', $movement->movement_date)
                    ->orderBy('rate_date', 'desc')
                    ->first();

                if ($exchangeRate) {
                    $item->exchange_rate = $exchangeRate->selling_rate;
                } else {
                    $item->exchange_rate = 1;
                }
            } else {
                $item->exchange_rate = 1;
            }

            $item->total_price = $item->unit_price * $item->quantity;

            $item->save();
        }
    }

    private function updateMovementTotalAmount(StockMovement $movement): void
    {
        $totalAmount = 0;
        $mainCurrency = 'TRY';

        foreach ($movement->items as $item) {
            $itemTotal = $item->total_price;

            if ($item->currency_code !== $mainCurrency && $item->exchange_rate > 0) {
                $itemTotal = $itemTotal * $item->exchange_rate;
            }

            $totalAmount += $itemTotal;
        }

        $movement->total_amount = $totalAmount;
        $movement->currency_code = $mainCurrency;
        $movement->save();
    }

    private function getPriceInfo($productId, $variantId = null)
    {
        if ($variantId) {
            $variant = ProductVariant::find($variantId);
            if ($variant && $variant->purchase_price > 0) {
                return [
                    'price' => $variant->purchase_price,
                    'currency_code' => $variant->purchase_currency_code ?? 'TRY'
                ];
            }
        }

        $product = Product::find($productId);
        if ($product) {
            return [
                'price' => $product->purchase_price ?? 0,
                'currency_code' => $product->purchase_currency_code ?? 'TRY'
            ];
        }

        return [
            'price' => 0,
            'currency_code' => 'TRY'
        ];
    }

    private function processStockMovement(StockMovement $movement): void
    {
        $movementType = $movement->stockMovementReason->movement_type_id;

        match ($movementType) {
            1 => $this->processStockIn($movement),
            2 => $this->processStockOut($movement),
            3 => $this->processStockTransfer($movement),
            default => throw new \Exception('Geçersiz hareket türü')
        };
    }

    private function processStockIn(StockMovement $movement): void
    {
        foreach ($movement->items as $item) {
            $stock = $this->findOrCreateStock($item, $movement, true);

            $item->stock_id = $stock->id;
            $item->save();

            $quantityToAdd = $item->base_quantity ?: $item->quantity;

            $warehouse = $movement->warehouse()->first();
            if ($warehouse) {
                $this->checkWarehouseCapacityBeforeStockIn($warehouse, $item->product, $item->variant, $quantityToAdd);
            }

            $location = $movement->location()->first();
            $this->checkWarehouseLocationCapacityBeforeStockIn($location, $item->product, $item->variant, $quantityToAdd);

            $stock->quantity += $quantityToAdd;
            $stock->save();

            if ($warehouse) {
                $this->updateWarehouseCapacityForStockIn($warehouse, $item->product, $item->variant, $quantityToAdd);
            }

            $this->updateWarehouseLocationCapacityForStockIn($location, $item->product, $item->variant, $quantityToAdd);
        }
    }

    private function processStockOut(StockMovement $movement): void
    {
        foreach ($movement->items as $item) {
            $quantityToDeduct = $item->base_quantity ?: $item->quantity;

            if ($item->stock_id) {
                $this->processDirectStockOut($item, $quantityToDeduct);
            } else {
                $this->processStockOutWithFIFO($item, $movement, $quantityToDeduct);
            }

            $warehouse = $movement->warehouse()->first();
            if ($warehouse) {
                $this->updateWarehouseCapacityForStockOut($warehouse, $item->product, $item->variant, $quantityToDeduct);
            }

            $location = $movement->location()->first();
            $this->updateWarehouseLocationCapacityForStockOut($location, $item->product, $item->variant, $quantityToDeduct);
        }
    }

    private function processDirectStockOut(StockMovementItem $item, float $quantityToDeduct): void
    {
        $stock = Stock::find($item->stock_id);

        if (!$stock) {
            throw new \Exception('Seçilen stok kaydı bulunamadı veya silinmiş olabilir.');
        }

        $availableQuantity = $this->calculateAvailableQuantity($stock);

        if ($availableQuantity < $quantityToDeduct) {
            throw new \Exception('Transfer için yetersiz stok miktarı. Gereken: ' . $quantityToDeduct . ', Kullanılabilir: ' . $availableQuantity . ', Eksik: ' . ($quantityToDeduct - $availableQuantity));
        }

        $stock->quantity -= $quantityToDeduct;
        $stock->save();
    }

    private function processStockOutWithFIFO(StockMovementItem $item, StockMovement $movement, float $quantityToDeduct): void
    {
        $stocks = $this->getFIFOStocks($item, $movement);

        if ($stocks->isEmpty()) {
            throw new \Exception('Transfer için kaynak depoda uygun stok bulunamadı. Depo, lokasyon ve ürün bilgilerini kontrol ediniz.');
        }

        $remainingQuantity = $quantityToDeduct;
        $processedStocks = [];

        foreach ($stocks as $stock) {
            $availableQuantity = $this->calculateAvailableQuantity($stock);

            if ($availableQuantity <= 0) {
                continue;
            }

            $quantityToTake = min($availableQuantity, $remainingQuantity);

            $stock->quantity -= $quantityToTake;
            $stock->save();

            $processedStocks[] = [
                'stock_id' => $stock->id,
                'quantity' => $quantityToTake
            ];

            $remainingQuantity -= $quantityToTake;

            if ($remainingQuantity <= 0) {
                break;
            }
        }

        if ($remainingQuantity > 0) {
            foreach ($processedStocks as $processed) {
                $stock = Stock::find($processed['stock_id']);
                $stock->quantity += $processed['quantity'];
                $stock->save();
            }

            throw new \Exception('Transfer için yetersiz kullanılabilir stok. Gereken miktar: ' . ($quantityToDeduct) . ', Kullanılabilir: ' . ($quantityToDeduct - $remainingQuantity) . ', Eksik: ' . $remainingQuantity);
        }

        $item->stock_id = $processedStocks[0]['stock_id'];
        $item->notes = ($item->notes ?? '') . "\nFIFO çıkışı yapıldı. İşlenen stoklar: " . json_encode($processedStocks);
        $item->save();
    }

    private function processStockTransfer(StockMovement $movement): void
    {
        foreach ($movement->items as $item) {
            $quantityToTransfer = $item->base_quantity ?: $item->quantity;

            if ($item->stock_id) {
                $this->processDirectStockOut($item, $quantityToTransfer);
            } else {
                $this->processStockOutWithFIFO($item, $movement, $quantityToTransfer);
            }

            $targetWarehouse = Warehouse::find($movement->target_warehouse_id);
            if ($targetWarehouse) {
                $this->checkWarehouseCapacityBeforeStockIn($targetWarehouse, $item->product, $item->variant, $quantityToTransfer);
            }

            $targetLocation = $movement->targetLocation()->first();
            $this->checkWarehouseLocationCapacityBeforeStockIn($targetLocation, $item->product, $item->variant, $quantityToTransfer);

            $targetStock = $this->findOrCreateTargetStock($item, $movement);
            $targetStock->quantity += $quantityToTransfer;
            $targetStock->save();

            $sourceWarehouse = $movement->warehouse()->first();
            if ($sourceWarehouse) {
                $this->updateWarehouseCapacityForStockOut($sourceWarehouse, $item->product, $item->variant, $quantityToTransfer);
            }

            if ($targetWarehouse) {
                $this->updateWarehouseCapacityForStockIn($targetWarehouse, $item->product, $item->variant, $quantityToTransfer);
            }

            $sourceLocation = $movement->location()->first();
            $this->updateWarehouseLocationCapacityForStockOut($sourceLocation, $item->product, $item->variant, $quantityToTransfer);

            $targetLocation = $movement->targetLocation()->first();
            $this->updateWarehouseLocationCapacityForStockIn($targetLocation, $item->product, $item->variant, $quantityToTransfer);
        }
    }

    private function calculateAvailableQuantity($stock, $excludeReservationId = null)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        if ($excludeReservationId) {
            $baseQuery->where('id', '!=', $excludeReservationId);
        }

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');

        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $stock->quantity - $salesReservations + $purchaseReservations;
    }

    private function calculateStockReservedQuantity(Stock $stock): float
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity') ?? 0;
    }

    private function getFIFOStocks(StockMovementItem $item, StockMovement $movement)
    {
        $query = Stock::where('product_id', $item->product_id)
            ->where('warehouse_id', $movement->warehouse_id)
            ->where('quantity', '>', 0)
            ->where('is_active', 1);

        if ($movement->location_id) {
            $query->where('warehouse_location_id', $movement->location_id);
        }

        if ($item->variant_id) {
            $query->where('variant_id', $item->variant_id);
        } else {
            $query->whereNull('variant_id');
        }

        return $query->orderBy('created_at', 'asc')->get();
    }

    private function findOrCreateStock(StockMovementItem $item, StockMovement $movement, bool $isStockIn = false): Stock
    {
        $query = Stock::where('product_id', $item->product_id)
            ->where('warehouse_id', $movement->warehouse_id)
            ->where('warehouse_location_id', $movement->location_id)
            ->where('variant_id', $item->variant_id)
            ->whereNull('stock_batch_id');

        $stock = $query->first();

        if (!$stock && $isStockIn) {
            $stockData = $this->getStockDataFromOrdersOrInvoices($item, $movement);

            $stock = Stock::create([
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'warehouse_id' => $movement->warehouse_id,
                'warehouse_location_id' => $movement->location_id,
                'stock_batch_id' => null,
                'quantity' => 0,
                'unit_cost' => $stockData['unit_cost'],
                'total_cost' => 0,
                'currency_code' => $stockData['currency_code'],
                'is_active' => 1,
                'created_by' => Auth::id()
            ]);
        }

        if (!$stock) {
            throw new \Exception('Kaynak depoda uygun stok bulunamadı veya stok oluşturulamadı.');
        }

        return $stock;
    }

    private function getStockDataFromOrdersOrInvoices(StockMovementItem $item, StockMovement $movement): array
    {
        $defaultData = [
            'unit_cost' => 0,
            'currency_code' => 'TRY'
        ];

        if (!$movement->current_id) {
            return $defaultData;
        }

        $invoiceData = $this->getDataFromInvoices($item, $movement);
        if ($invoiceData['unit_cost'] > 0) {
            return $invoiceData;
        }

        $orderData = $this->getDataFromOrders($item, $movement);
        if ($orderData['unit_cost'] > 0) {
            return $orderData;
        }

        return $defaultData;
    }

    private function getDataFromInvoices(StockMovementItem $item, StockMovement $movement): array
    {
        $defaultData = ['unit_cost' => 0, 'currency_code' => 'TRY'];

        $invoiceItem = \App\Models\InvoiceItem::whereHas('invoice', function($query) use ($movement) {
                $query->where('current_id', $movement->current_id)
                      ->where('invoice_status_id', 2)
                      ->whereHas('invoiceType', function($q) {
                          $q->where('id', 1);
                      });
            })
            ->where('product_id', $item->product_id)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($invoiceItem && $invoiceItem->unit_price > 0) {
            return [
                'unit_cost' => $invoiceItem->unit_price,
                'currency_code' => $invoiceItem->currency_type ?? 'TRY'
            ];
        }

        return $defaultData;
    }

    private function getDataFromOrders(StockMovementItem $item, StockMovement $movement): array
    {
        $defaultData = ['unit_cost' => 0, 'currency_code' => 'TRY'];

        $orderProduct = \App\Models\OrderProduct::whereHas('order', function($query) use ($movement) {
                $query->where('current_id', $movement->current_id)
                      ->whereHas('orderType', function($q) {
                          $q->where('id', 1);
                      });
            })
            ->whereHas('stock', function($query) use ($item) {
                $query->where('product_id', $item->product_id)
                      ->when($item->variant_id, function($q) use ($item) {
                          $q->where('variant_id', $item->variant_id);
                      });
            })
            ->where('unit_price', '>', 0)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($orderProduct && $orderProduct->unit_price > 0) {
            return [
                'unit_cost' => $orderProduct->unit_price,
                'currency_code' => $orderProduct->currency_type ?? 'TRY'
            ];
        }

        return $defaultData;
    }

    private function checkWarehouseCapacityBeforeStockIn(Warehouse $warehouse, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        if (!$warehouse->max_weight_capacity && !$warehouse->max_volume_capacity) {
            return;
        }

        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToAdd = $productWeight * $quantity;
        $totalVolumeToAdd = $productVolume * $quantity;

        if ($warehouse->max_weight_capacity > 0) {
            $newTotalWeight = $warehouse->current_weight + $totalWeightToAdd;
            if ($newTotalWeight > $warehouse->max_weight_capacity) {
                $remainingCapacity = $warehouse->max_weight_capacity - $warehouse->current_weight;
                throw new \Exception(
                    "Depo ağırlık kapasitesi aşılacak! " .
                    "Mevcut: " . number_format($warehouse->current_weight, 3) . " kg, " .
                    "Maksimum: " . number_format($warehouse->max_weight_capacity, 3) . " kg, " .
                    "Eklenecek: " . number_format($totalWeightToAdd, 3) . " kg, " .
                    "Kalan Kapasite: " . number_format($remainingCapacity, 3) . " kg"
                );
            }
        }

        if ($warehouse->max_volume_capacity > 0) {
            $newTotalVolume = $warehouse->current_volume + $totalVolumeToAdd;
            if ($newTotalVolume > $warehouse->max_volume_capacity) {
                $remainingCapacity = $warehouse->max_volume_capacity - $warehouse->current_volume;
                throw new \Exception(
                    "Depo hacim kapasitesi aşılacak! " .
                    "Mevcut: " . number_format($warehouse->current_volume, 4) . " m³, " .
                    "Maksimum: " . number_format($warehouse->max_volume_capacity, 4) . " m³, " .
                    "Eklenecek: " . number_format($totalVolumeToAdd, 4) . " m³, " .
                    "Kalan Kapasite: " . number_format($remainingCapacity, 4) . " m³"
                );
            }
        }
    }

    private function updateWarehouseCapacityForStockIn(Warehouse $warehouse, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToAdd = $productWeight * $quantity;
        $totalVolumeToAdd = $productVolume * $quantity;

        $warehouse->current_weight += $totalWeightToAdd;
        $warehouse->current_volume += $totalVolumeToAdd;
        $warehouse->save();
    }

    private function updateWarehouseCapacityForStockOut(Warehouse $warehouse, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToRemove = $productWeight * $quantity;
        $totalVolumeToRemove = $productVolume * $quantity;

        $warehouse->current_weight = max(0, $warehouse->current_weight - $totalWeightToRemove);
        $warehouse->current_volume = max(0, $warehouse->current_volume - $totalVolumeToRemove);
        $warehouse->save();
    }

    private function updateWarehouseLocationCapacityForStockIn(?WarehouseLocation $location, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        if (!$location) {
            return;
        }

        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToAdd = $productWeight * $quantity;
        $totalVolumeToAdd = $productVolume * $quantity;

        $location->current_weight += $totalWeightToAdd;
        $location->current_volume += $totalVolumeToAdd;
        $location->save();
    }

    private function updateWarehouseLocationCapacityForStockOut(?WarehouseLocation $location, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        if (!$location) {
            return;
        }

        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToRemove = $productWeight * $quantity;
        $totalVolumeToRemove = $productVolume * $quantity;

        $location->current_weight = max(0, $location->current_weight - $totalWeightToRemove);
        $location->current_volume = max(0, $location->current_volume - $totalVolumeToRemove);
        $location->save();
    }

    private function checkWarehouseLocationCapacityBeforeStockIn(?WarehouseLocation $location, Product $product, ?ProductVariant $variant, float $quantity): void
    {
        if (!$location || !$location->max_weight_capacity || !$location->max_volume_capacity) {
            return;
        }

        $productWeight = $variant ? ($variant->weight ?: $product->weight ?: 0) : ($product->weight ?: 0);
        $productVolume = $variant ? ($variant->volume ?: $product->volume ?: 0) : ($product->volume ?: 0);

        $totalWeightToAdd = $productWeight * $quantity;
        $totalVolumeToAdd = $productVolume * $quantity;

        // Ağırlık kapasitesi kontrolü
        if ($location->max_weight_capacity > 0) {
            $remainingCapacity = $location->max_weight_capacity - $location->current_weight;
            if ($totalWeightToAdd > $remainingCapacity) {
                throw new \Exception(
                    "Depo lokasyonu ağırlık kapasitesi yetersiz! " .
                    "Mevcut: " . number_format($location->current_weight, 4) . " kg, " .
                    "Maksimum: " . number_format($location->max_weight_capacity, 4) . " kg, " .
                    "Eklenecek: " . number_format($totalWeightToAdd, 4) . " kg, " .
                    "Kalan Kapasite: " . number_format($remainingCapacity, 4) . " kg"
                );
            }
        }

        // Hacim kapasitesi kontrolü
        if ($location->max_volume_capacity > 0) {
            $remainingCapacity = $location->max_volume_capacity - $location->current_volume;
            if ($totalVolumeToAdd > $remainingCapacity) {
                throw new \Exception(
                    "Depo lokasyonu hacim kapasitesi yetersiz! " .
                    "Mevcut: " . number_format($location->current_volume, 4) . " m³, " .
                    "Maksimum: " . number_format($location->max_volume_capacity, 4) . " m³, " .
                    "Eklenecek: " . number_format($totalVolumeToAdd, 4) . " m³, " .
                    "Kalan Kapasite: " . number_format($remainingCapacity, 4) . " m³"
                );
            }
        }
    }

    private function findOrCreateTargetStock(StockMovementItem $item, StockMovement $movement): Stock
    {
        $stock = Stock::where('product_id', $item->product_id)
            ->where('variant_id', $item->variant_id)
            ->where('warehouse_id', $movement->target_warehouse_id)
            ->where('warehouse_location_id', $movement->target_location_id)
            ->whereNull('stock_batch_id')
            ->first();

        if (!$stock) {
            $stockData = $this->getStockDataFromOrdersOrInvoices($item, $movement);

            $stock = Stock::create([
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'warehouse_id' => $movement->target_warehouse_id,
                'warehouse_location_id' => $movement->target_location_id,
                'stock_batch_id' => null,
                'quantity' => 0,
                'unit_cost' => $stockData['unit_cost'],
                'total_cost' => 0,
                'currency_code' => $stockData['currency_code'],
                'is_active' => 1,
                'created_by' => Auth::id()
            ]);
        }

        return $stock;
    }

    public function saveHook($request): array
    {
        $params = $request->all();

        $movementData = [
            'movement_no' => $params['movement_no'] ?? StockMovement::generateMovementNo(),
            'movement_date' => $params['movement_date'] ?? now(),
            'current_id' => $params['current_id'] ?? null,
            'starter_id' => Auth::id(),
            'status_id' => $params['status_id'] ?? 1,
            'stock_movement_reason_id' => $params['stock_movement_reason_id'] ?? null,
            'stock_movement_type_id' => $params['movement_type_id'] ?? null,
            'warehouse_id' => $params['warehouse_id'] ?? null,
            'location_id' => $params['location_id'] ?? null,
            'target_warehouse_id' => $params['target_warehouse_id'] ?? null,
            'target_location_id' => $params['target_location_id'] ?? null,
            'notes' => $params['notes'] ?? null,
            'is_active' => $params['is_active'] ?? 1,
        ];

        if (isset($params['stock_movement_reason_id']) && !empty($params['stock_movement_reason_id'])) {
            $reason = StockMovementReason::find($params['stock_movement_reason_id']);
            if ($reason) {
                $movementData['stock_movement_type_id'] = $reason->movement_type_id;
            }
        }

        if ($movementData['stock_movement_type_id'] == 3 && empty($movementData['stock_movement_reason_id'])) {
            $transferReason = StockMovementReason::where('movement_type_id', 3)->first();
            if ($transferReason) {
                $movementData['stock_movement_reason_id'] = $transferReason->id;
            }
        }

        $this->tempProductData = [
            'products_data' => $params['products_data'] ?? null
        ];
        return $movementData;
    }

    public function saveBack($movement)
    {
        if (empty($this->tempProductData) || !$this->tempProductData['products_data']) {
            return redirect()
                ->route("backend.{$this->page}_list")
                ->with('success', 'Kayıt başarıyla oluşturuldu');
        }

        DB::beginTransaction();

        try {
            $this->createMovementItems($movement);

            DB::commit();

            return redirect()
                ->route("backend.{$this->page}_list")
                ->with('success', 'Stok hareketi başarıyla oluşturuldu');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Stok hareketi kaydedilirken hata oluştu: ' . $e->getMessage());
        }
    }

    private function createMovementItems(StockMovement $movement): void
    {
        $productsData = json_decode($this->tempProductData['products_data'], true);

        if (!is_array($productsData) || empty($productsData)) {
            throw new \Exception('Ürün verileri bulunamadı');
        }

        $existingItemsCount = StockMovementItem::where('stock_movement_id', $movement->id)->count();
        if ($existingItemsCount > 0) {
            StockMovementItem::where('stock_movement_id', $movement->id)->delete();

        }

        foreach ($productsData as $productData) {
            $product = Product::find((int) $productData['product_id']);
            if (!$product) {
                throw new \Exception("Ürün bulunamadı: {$productData['product_id']}");
            }

            $quantity = (float) $productData['quantity'];
            $variantId = !empty($productData['variant_id']) ? (int) $productData['variant_id'] : null;

            $baseQuantity = $quantity;

            $stockId = $this->determineStockIdForProduct($movement, $product, $productData);

            // Otomatik fiyat hesaplama - product/variant alış fiyatından
            $priceInfo = $this->getPriceInfo($product->id, $variantId);
            $unitPrice = $priceInfo['price'];
            $currencyCode = $priceInfo['currency_code'];

            // Çıkış hareketi için stok maliyetini kontrol et
            if ($movement->stock_movement_type_id == 2 && $stockId) {
                $stock = Stock::find($stockId);
                if ($stock && $stock->unit_cost > 0) {
                    $unitPrice = $stock->unit_cost;
                    $currencyCode = $stock->currency_code ?? 'TRY';
                }
            }

            // Transfer hareketi için özel mantık
            if ($movement->stock_movement_type_id == 3) {
                $transferPrice = $this->getTransferPrice($stockId, $product);
                if ($transferPrice > 0) {
                    $unitPrice = $transferPrice;
                }
            }

            $totalPrice = $unitPrice * $quantity;

            // Döviz kuru hesapla (currencyCode zaten yukarıda belirlendi)
            $exchangeRate = 1;

            if ($currencyCode !== 'TRY') {
                $exchangeRateRecord = ExchangeRate::where('code', $currencyCode)
                    ->whereDate('rate_date', '<=', $movement->movement_date)
                    ->orderBy('rate_date', 'desc')
                    ->first();

                if ($exchangeRateRecord) {
                    $exchangeRate = $exchangeRateRecord->selling_rate;
                }
            }

            $item = StockMovementItem::create([
                'stock_movement_id' => $movement->id,
                'stock_id' => $stockId,
                'product_id' => $product->id,
                'variant_id' => $variantId,
                'unit_id' => $product->unit_id,
                'quantity' => $quantity,
                'base_quantity' => $baseQuantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'currency_code' => $currencyCode,
                'exchange_rate' => $exchangeRate,
                'status_id' => 1,
                'created_by' => Auth::id(),
            ]);
        }
    }

    private function getTransferPrice(?int $stockId, Product $product): float
    {
        if (!$stockId) {
            return 0;
        }

        $stock = Stock::find($stockId);
        if ($stock && $stock->unit_cost > 0) {
            return $stock->unit_cost;
        }

        $lastStockIn = StockMovementItem::whereHas('stockMovement', function ($query) {
            $query->whereHas('stockMovementReason', function ($subQuery) {
                $subQuery->where('movement_type_id', 1);
            });
        })
            ->where('product_id', $product->id)
            ->where('unit_price', '>', 0)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastStockIn) {
            return $lastStockIn->unit_price;
        }

        return 0;
    }

    private function determineStockIdForProduct(StockMovement $movement, Product $product, array $productData): ?int
    {
        $stockId = !empty($productData['stock_id']) ? (int) $productData['stock_id'] : null;

        if ($stockId) {
            return $stockId;
        }

        $variantId = !empty($productData['variant_id']) ? (int) $productData['variant_id'] : null;

        if ($movement->stock_movement_type_id == 3) {
            $stock = Stock::where('product_id', $product->id)
                ->where('warehouse_id', $movement->warehouse_id)
                ->when($movement->location_id, fn($q) => $q->where('warehouse_location_id', $movement->location_id))
                ->when(
                    $variantId,
                    fn($q) => $q->where('variant_id', $variantId),
                    fn($q) => $q->whereNull('variant_id')
                )
                ->where('quantity', '>', 0)
                ->where('is_active', 1)
                ->orderBy('created_at', 'asc')
                ->first();

            return $stock?->id;
        }

        if (!$movement->stock_movement_reason_id) {
            return null;
        }

        $reason = StockMovementReason::find($movement->stock_movement_reason_id);
        if (!$reason) {
            return null;
        }

        if ($reason->movement_type_id == 1) {
            $existingStock = Stock::where('product_id', $product->id)
                ->where('warehouse_id', $movement->warehouse_id)
                ->where('warehouse_location_id', $movement->location_id)
                ->where('variant_id', $variantId)
                ->whereNull('stock_batch_id')
                ->first();

            if ($existingStock) {
                return $existingStock->id;
            }

            $tempItem = new StockMovementItem([
                'product_id' => $product->id,
                'variant_id' => $variantId
            ]);

            $stockData = $this->getStockDataFromOrdersOrInvoices($tempItem, $movement);

            $newStock = Stock::create([
                'product_id' => $product->id,
                'variant_id' => $variantId,
                'warehouse_id' => $movement->warehouse_id,
                'warehouse_location_id' => $movement->location_id,
                'quantity' => 0,
                'unit_cost' => $stockData['unit_cost'],
                'total_cost' => 0,
                'currency_code' => $stockData['currency_code'],
                'is_active' => 1,
                'created_by' => Auth::id(),
            ]);

            return $newStock->id;
        }

        if ($reason->movement_type_id == 2) {
            $stock = Stock::where('product_id', $product->id)
                ->where('warehouse_id', $movement->warehouse_id)
                ->when($movement->location_id, fn($q) => $q->where('warehouse_location_id', $movement->location_id))
                ->when(
                    $variantId,
                    fn($q) => $q->where('variant_id', $variantId),
                    fn($q) => $q->whereNull('variant_id')
                )
                ->where('quantity', '>', 0)
                ->where('is_active', 1)
                ->orderBy('created_at', 'asc')
                ->first();

            return $stock?->id;
        }

        return null;
    }

    public function form(Request $request, $unique = null)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);

            if (is_null($item)) {
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
            }

            // Onaylanmış hareketleri düzenleme kontrolü
            if ($item->status_id == 2) {
                return redirect()
                    ->route('backend.stock_movement_detail', $unique)
                    ->with('error', 'Onaylanmış stok hareketleri düzenlenemez. Sadece detay görüntülenebilir.');
            }
        } else {
            $item = new $this->model;
        }

        return view("backend.$this->page.form", compact('item'));
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('stock_id', function ($item) {
            if ($item->stock) {
                $stockInfo = $item->stock->product->name;
                if ($item->stock->variant) {
                    $stockInfo .= ' - ' . $item->stock->variant->name;
                }
                return $stockInfo;
            } else if ($item->product) {
                $stockInfo = $item->product->name;
                if ($item->variant) {
                    $stockInfo .= ' - ' . $item->variant->name;
                }
                return $stockInfo;
            }
            return '-';
        })
            ->editColumn('movement_date', function ($item) {
                return (!is_null($item->movement_date) ? $item->movement_date->format('d.m.Y H:i') : '-');
            });
    }
}
