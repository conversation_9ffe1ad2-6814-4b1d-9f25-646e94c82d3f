<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use App\Models\Route as RouteModel;

class RouteSaveCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'route:save';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Route\'ları kaydeder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $routes = Route::getRoutes();
        $saveCount = 0;
        $totalRoutes = 0;

        foreach ($routes as $route) {
            $totalRoutes++;
            $routeName = $route->getName();

            // Route adı yoksa veya hariç tutulan bir route ise atla
            if (!$routeName || $this->shouldExcludeRoute($routeName)) {
                continue;
            }

            // Route'un desc değerini al
            $routeAction = $route->getAction();
            $routeDesc = '';

            // desc değerini güvenli şekilde al
            if (isset($routeAction['desc'])) {
                $desc = $routeAction['desc'];
                if (is_string($desc)) {
                    $routeDesc = $desc;
                } elseif (is_array($desc) && !empty($desc)) {
                    $routeDesc = $desc[0]; // İlk elemanı al
                }
            }

            $category_name = $routeDesc ?: $this->getCategoryName($routeName);

            RouteModel::updateOrCreate(
                ['route_name' => $routeName],
                [
                    'name' => $this->getRouteDescription($routeName),
                    'route_name' => $routeName,
                    'category_name' => $category_name,
                ]
            );

            $saveCount++;
        }

        $this->info('Toplam ' . $totalRoutes . ' route bulundu.');
        $this->info($saveCount . ' route başarıyla veritabanına kaydedildi!');
    }

    protected function getCategoryName($routeName)
    {
        // Route isminden kategori ismini çıkar
        $parts = explode('.', $routeName);
        if (count($parts) < 2)
            return '';

        $category = $parts[1]; // backend.project_type_list gibi bir isimden project_type kısmını al

        // Kategorileri daha spesifikten daha genele doğru sıralayalım
        $categories = [
            'role' => 'Roller',
            'user' => 'Kullanıcılar'
        ];

        // Tam eşleşmeleri önce deneyelim
        if (isset($categories[$category])) {
            return $categories[$category];
        }

        // Tam eşleşme yoksa, daha akıllı bir eşleştirme yapalım
        // Önce en uzun anahtarları kontrol edelim
        $keys = array_keys($categories);
        usort($keys, function ($a, $b) {
            return strlen($b) - strlen($a); // Uzunluğa göre azalan sıralama
        });

        foreach ($keys as $key) {
            // Kategori tam olarak bu anahtar ile başlıyorsa veya bu anahtarı içeriyorsa
            if (strpos($category, $key) === 0 || strpos($category, '_' . $key) !== false) {
                return $categories[$key];
            }
        }

        return ucfirst($category);
    }

    protected function shouldExcludeRoute($routeName)
    {
        // Profil, login ve livewire ile ilgili routeları hariç tut
        if (strpos($routeName, 'profile') !== false)
            return true;
        if (strpos($routeName, 'signin') !== false)
            return true;
        if (strpos($routeName, 'signup') !== false)
            return true;
        if (strpos($routeName, 'logout') !== false)
            return true;
        if (strpos($routeName, 'livewire') !== false)
            return true;
        if (strpos($routeName, 'backend.index') !== false)
            return true;
        if (strpos($routeName, 'forgot') !== false)
            return true;

        // Backend routeları dışındakileri hariç tut
        if (strpos($routeName, 'backend.') === false)
            return true;

        return false;
    }

    protected function getRouteDescription($routeName)
    {
        $patterns = [
            'list' => 'Listele',
            'form' => 'Form Görüntüle',
            'save' => 'Kaydet',
            'delete' => 'Sil',
            'detail' => 'Detay Görüntüle',
            'status' => 'Durum Değiştir',
            'excel' => 'Excel Dışa Aktar',
            'pdf' => 'PDF Dışa Aktar',
            'refresh' => 'Yenile',
            'external' => 'Harici',
            'profile' => 'Profil',
        ];

        foreach ($patterns as $key => $desc) {
            if (strpos($routeName, $key) !== false) {
                return $desc;
            }

        }

        return 'Bilinmeyen İşlem';
    }
}
