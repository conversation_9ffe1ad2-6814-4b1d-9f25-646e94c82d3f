<?php

namespace App\Http\Controllers\Backend;

use App\Models\PaymentTerm;

class PaymentTermController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Vade Tanımlamaları';
        $this->page = 'payment_term';
        $this->model = new PaymentTerm();
        $this->relation = [];

        $this->view = (object)[
            'breadcrumb' => [
                'Vade Tanımlamaları' => route('backend.payment_term_list'),
            ],
        ];
        $this->validation = array(
            [
                'day_count' => 'required|integer|min:0'
            ],
            [
                'day_count.required' => '<PERSON>ün sayısı boş bırakılamaz.',
                'day_count.integer' => '<PERSON>ün sayısı sayısal bir değer olmalıdır.',
                'day_count.min' => '<PERSON>ün sayısı 0\'dan küçük olamaz.',
            ]
        );
        parent::__construct();
    }
}
