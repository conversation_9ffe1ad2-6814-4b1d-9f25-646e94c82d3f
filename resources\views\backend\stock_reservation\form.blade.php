@extends('layout.layout')

@php
    $title = $item->id ? $container->title . ' Düzenle' : $container->title . ' Ekle';
    $subTitle = $item->id ? 'Rezervasyon Düzenle' : 'Yeni Rezervasyon Ekle';
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                @csrf

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Rezervasyon Tipi</label>
                        <select name="reservation_type_id" id="reservation_type_id" class="form-select select2">
                            <option value="">Seçiniz</option>
                            @foreach ($reservationTypes as $type)
                                <option value="{{ $type->id }}"
                                    {{ old('reservation_type_id', $item->reservation_type_id) == $type->id ? 'selected' : '' }}>
                                    {{ $type->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('reservation_type_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Rezervasyon Nedeni</label>
                        <select name="reservation_reason_id" id="reservation_reason_id" class="form-select select2">
                            <option value="">Seçiniz</option>
                            @foreach ($reservationReasons as $reason)
                                <option value="{{ $reason->id }}"
                                    {{ old('reservation_reason_id', $item->reservation_reason_id) == $reason->id ? 'selected' : '' }}>
                                    {{ $reason->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('reservation_reason_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Ürün <span class="text-danger">*</span></label>
                        <select name="product_id" class="form-select select2" required>
                            <option value="">Seçiniz</option>
                            @foreach ($products as $product)
                                <option value="{{ $product->id }}"
                                    {{ old('product_id', $item->product_id) == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }} ({{ $product->sku }})
                                </option>
                            @endforeach
                        </select>
                        @error('product_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Varyant</label>
                        <select name="variant_id" class="form-select select2">
                            <option value="">Seçiniz</option>
                            @if ($item->product_id)
                                @foreach ($variants->where('product_id', $item->product_id) as $variant)
                                    <option value="{{ $variant->id }}"
                                        {{ old('variant_id', $item->variant_id) == $variant->id ? 'selected' : '' }}>
                                        {{ $variant->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @error('variant_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Stok <span class="text-danger">*</span></label>
                        <select name="stock_id" class="form-select select2" required>
                            <option value="">Seçiniz</option>
                            @foreach ($stocks as $stock)
                                @if ($stock->product_id == $item->product_id && $stock->variant_id == $item->variant_id)
                                    <option value="{{ $stock->id }}"
                                        {{ old('stock_id', $item->stock_id) == $stock->id ? 'selected' : '' }}>
                                        {{ $stock->warehouse->name }} - {{ $stock->warehouseLocation->name ?? 'Ana' }}
                                        (Miktar: {{ $stock->quantity }})
                                    </option>
                                @endif
                            @endforeach
                        </select>
                        @error('stock_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Rezerve Miktarı <span class="text-danger">*</span></label>
                        <input type="number" step="0.01" name="quantity" class="form-control"
                            value="{{ old('quantity', $item->quantity) }}" required min="0.01">
                        @error('quantity')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Cari Hesap</label>
                        <select name="current_id" class="form-select select2">
                            <option value="">Seçiniz</option>
                            @foreach ($current as $account)
                                <option value="{{ $account->id }}"
                                    {{ old('current_id', $item->current_id) == $account->id ? 'selected' : '' }}>
                                    {{ $account->title }}
                                </option>
                            @endforeach
                        </select>
                        @error('current_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Öncelik</label>
                        <input type="number" name="priority_level" class="form-control"
                            value="{{ old('priority_level', $item->priority_level ?: 5) }}" min="1" max="10">
                        @error('priority_level')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">Sipariş No</label>
                        <input type="text" name="order_id" class="form-control"
                            value="{{ old('order_id', $item->order_id) }}">
                        @error('order_id')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Başlangıç Tarihi</label>
                        <input type="datetime-local" name="start_date" class="form-control"
                            value="{{ old('start_date', $item->start_date ? $item->start_date->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i')) }}">
                        @error('start_date')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Bitiş Tarihi</label>
                        <input type="datetime-local" name="end_date" class="form-control"
                            value="{{ old('end_date', $item->end_date ? $item->end_date->format('Y-m-d\TH:i') : '') }}">
                        @error('end_date')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label class="form-label">Notlar</label>
                        <textarea name="notes" class="form-control" rows="3">{{ old('notes', $item->notes) }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-12">
                        <label class="form-label">Durum</label>
                        <div class="icon-field">
                            <span class="icon">
                                <iconify-icon icon="carbon:badge"></iconify-icon>
                            </span>
                            <select class="form-control form-select" name="is_active">
                                <option value="1"
                                    {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                </option>
                                <option value="0"
                                    {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                </option>
                            </select>
                            <x-form-error field="is_active" />
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary-600">Kaydet</button>
                    </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var allReservationReasons = [
                @foreach ($reservationReasons as $reason)
                    {
                        id: {{ $reason->id }},
                        name: "{{ $reason->name }}",
                        stock_reservation_type_id: {{ $reason->stock_reservation_type_id ?? 'null' }}
                    },
                @endforeach
            ];

            var allVariants = [
                @foreach ($variants as $variant)
                    {
                        id: {{ $variant->id }},
                        name: "{{ $variant->name }}",
                        product_id: {{ $variant->product_id }}
                    },
                @endforeach
            ];

            var allStocks = [
                @foreach ($stocks as $stock)
                    {
                        id: {{ $stock->id }},
                        warehouse_name: "{{ $stock->warehouse->name }}",
                        location_name: "{{ $stock->warehouseLocation->name ?? 'Ana' }}",
                        quantity: {{ $stock->quantity }},
                        product_id: {{ $stock->product_id }},
                        variant_id: {{ $stock->variant_id ?? 'null' }}
                    },
                @endforeach
            ];

            $('#reservation_type_id').on('change', function() {
                var typeId = $(this).val();
                var reasonSelect = $('#reservation_reason_id');

                reasonSelect.html('<option value="">Seçiniz</option>');

                if (typeId) {
                    var filteredReasons = allReservationReasons.filter(function(reason) {
                        return reason.stock_reservation_type_id == typeId;
                    });

                    filteredReasons.forEach(function(reason) {
                        reasonSelect.append('<option value="' + reason.id + '">' + reason.name +
                            '</option>');
                    });
                }
            });

            $('select[name="product_id"]').on('change', function() {
                var productId = $(this).val();
                var variantSelect = $('select[name="variant_id"]');
                var stockSelect = $('select[name="stock_id"]');

                variantSelect.html('<option value="">Seçiniz</option>');
                stockSelect.html('<option value="">Seçiniz</option>');

                if (productId) {
                    var filteredVariants = allVariants.filter(function(variant) {
                        return variant.product_id == productId;
                    });

                    filteredVariants.forEach(function(variant) {
                        variantSelect.append('<option value="' + variant.id + '">' + variant.name +
                            '</option>');
                    });

                    updateStocks();
                }
            });

            $('select[name="variant_id"]').on('change', function() {
                updateStocks();
            });

            function updateStocks() {
                var productId = $('select[name="product_id"]').val();
                var variantId = $('select[name="variant_id"]').val();
                var stockSelect = $('select[name="stock_id"]');

                stockSelect.html('<option value="">Seçiniz</option>');

                if (productId) {
                    var filteredStocks = allStocks.filter(function(stock) {
                        if (variantId) {
                            return stock.product_id == productId && stock.variant_id == variantId;
                        } else {
                            return stock.product_id == productId && !stock.variant_id;
                        }
                    });

                    filteredStocks.forEach(function(stock) {
                        var option = '<option value="' + stock.id + '">';
                        option += stock.warehouse_name + ' - ' + stock.location_name;
                        option += ' (Miktar: ' + stock.quantity + ')';
                        option += '</option>';
                        stockSelect.append(option);
                    });
                }
            }

            var initialReservationTypeId = $('#reservation_type_id').val();
            if (initialReservationTypeId) {
                var currentReasonId = $('#reservation_reason_id').val();

                $('#reservation_type_id').trigger('change');

                if (currentReasonId) {
                    $('#reservation_reason_id option[value="' + currentReasonId + '"]').prop(
                        'selected', true);
                }
            }

            var initialProductId = $('select[name="product_id"]').val();
            if (initialProductId) {
                var currentVariantId = $('select[name="variant_id"]').val();
                var currentStockId = $('select[name="stock_id"]').val();

                $('select[name="product_id"]').trigger('change');

                if (currentVariantId) {
                    $('select[name="variant_id"] option[value="' + currentVariantId + '"]').prop('selected', true);

                    updateStocks();

                    if (currentStockId) {
                        $('select[name="stock_id"] option[value="' + currentStockId + '"]').prop('selected', true);
                    }
                }
            }
        });
    </script>
@endsection
