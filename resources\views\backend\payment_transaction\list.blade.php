@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.' . $container->page . '_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    Ekle
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Filtreler -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">Cari</label>
                    <select class="form-select select2" id="filter-current" filter-name="filter-current">
                        <option value="">Cari <PERSON></option>
                        @foreach($currents as $cari)
                            <option value="{{ $cari->id }}">{{ $cari->title }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date" filter-name="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date" filter-name="filter-end-date">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Fatura Tipi</label>
                    <select class="form-control select2" id="filter-invoice-type">
                        <option value="">Lütfen Fatura Tipi Seçiniz</option>
                        @foreach ($allInvoices as $invoice)
                            <option value="{{ $invoice->invoice_no }}">{{ $invoice->invoice_no }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">İşlem Tarihi</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center">Fatura No</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Veriler AJAX ile yüklenecek -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {

            BaseCRUD.selector = "[datatable]";

            const table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        return $.extend({}, d, {
                            start_date: $('[filter-name="filter-start-date"]').val(),
                            end_date: $('[filter-name="filter-end-date"]').val(),
                            current_id: $('#filter-current').val(), // Carii ID'sini doğru parametre ile gönder
                            invoice_no: $('#filter-invoice-no').val() // Fatura no filtresini ekle
                        });
                    }
                },
                columns: [{
                        data: 'transaction_date',
                        name: 'transaction_date',
                        className: 'text-center'
                    },
                    {
                        data: 'current.title',
                        name: 'current.title',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_no',
                        name: 'invoice_no',
                        className: 'text-center'
                    },
                    {
                        data: 'amount_formatted',
                        name: 'amount_formatted',
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    },
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            });

            // Cari, işlem tipi veya tarih değiştiğinde anlık filtreleme yap
            $('#filter-current, #filter-invoice-type, #filter-start-date, #filter-end-date').on('change',
            function() {
                    table.ajax.reload();
                });

            $('#filter-invoice-no').on('change', function() {
                table.ajax.reload();
            });
            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

            $('[filter-name]').change(function() {
                $("[datatable] tbody").empty();
                $("[datatable]").DataTable().ajax.reload();
            });
        });
    </script>
@endsection
