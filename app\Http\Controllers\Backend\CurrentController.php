<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Requests\Backend\CurrentRequest;
use App\Models\Balance;
use Illuminate\Http\Request;
use App\Models\City;
use App\Models\Country;
use App\Models\CurrentType;
use App\Models\Current;
use App\Models\District;
use App\Models\InsutationType;
use App\Models\TaxOffice;

class CurrentController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Hesap';
        $this->page = 'current';
        $this->model = new Current();
        $this->relation = ['instutaion', 'currentType'];

        $this->view = (object)[
            'breadcrumb' => [
                'Cari Hesaplar' => route('backend.current_list'),
            ],
        ];
        $this->validation = array(
            [
                'title' => 'required|string|max:255',
                'short_description' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'instutation_id' => 'required|integer|exists:institution_types,id',
                'current_type_id' => 'required|integer|exists:current_types,id',
                'tax_number' => 'required|string|max:20',
                'country_id' => 'required|integer|exists:countries,id',
                'city_id' => 'required|integer|exists:cities,id',
                'district_id' => 'required|integer|exists:districts,id',
                'address' => 'required|string|max:255|min:3',
                'tax_offices_id' => 'required|integer|exists:tax_offices,id',
            ],
            [
                'title.required' => 'Ad alanı zorunludur.',
                'short_description.required' => 'Kısa açıklama zorunludur',
                'instutation_id.required' => 'Lütfen bir kurum tipi seçiniz.',
                'instutation_id.exists' => 'Seçilen kurum tipi bulunamadı.',
                'current_type_id.required' => 'Lütfen bir cari tipi seçiniz.',
                'current_type_id.exists' => 'Seçilen cari tipi bulunamadı.',
                'email.email' => 'Geçerli bir e-posta adresi giriniz.',
                'email.required' => 'E-posta adresi zorunludur.',
                'phone.required' => ' Telefon numarası zorunludur.',
                'tax_number.required' => 'Vergi numarası  zorunludur.',
                'tax_number.max' => 'Vergi numarası en fazla 20 karakter olabilir.',
                'country_id.required' => 'Lütfen bir ülke seçiniz.',
                'country_id.exists' => 'Seçilen ülke bulunamadı.',
                'city_id.required' => 'Lütfen bir şehir seçiniz.',
                'city_id.exists' => 'Seçilen şehir bulunamadı.',
                'district_id.required' => 'Lütfen bir ilçe seçiniz.',
                'district_id.exists' => 'Seçilen ilçe bulunamadı.',
                'address.required' => 'Lütfen adresi yazınız.',
                'address.max' => 'Adres en fazla 255 karakter olabilir.',
                'address.min' => 'Adres en az 3 karakter olmalıdır.',
                'tax_offices_id.required' => 'Seçilen vergi dairesi bulunamadı.',
                'tax_offices_id.exists' => 'Seçilen vergi dairesi bulunamadı.',
            ]);

        view()->share('cities', City::get());
        view()->share('countrys', Country::get());
        view()->share('districts', District::get());
        view()->share('insutations', InsutationType::get());
        view()->share('taxoffices', TaxOffice::orderBy('code')->get());
        view()->share('currenttypes', CurrentType::get());

        parent::__construct();
    }
    public function saveBack(Current $current)
    {
        $balance = Balance::where('current_id', $current->id)->first();

        if ($balance) {
            // Mevcut cari, güncelleme işlemi yapılıyor
            $balance->increment('credit_balance', 0); // Sıfırla bile tetiklenmiş olur
            $balance->increment('debit_balance', 0);
        } else {
            // Yeni cari, sıfır değerlerle oluşturuluyor
            Balance::create([
                'current_id' => $current->id,
                'credit_balance' => 0,
                'debit_balance' => 0,
            ]);
        }

        return redirect()
            ->route("backend." . $this->page . "_list")
            ->with('success', 'Kayıt başarılı şekilde işlendi');
    }
}
