<?php

namespace App\Http\Controllers\Backend;

use App\Models\Warehouse;
use Illuminate\Http\Request;

class WarehouseController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Depolar';
        $this->page = 'warehouse';
        $this->model = new Warehouse();
        $this->relation = ['locations'];
        $this->listQuery = Warehouse::select('warehouses.*');

        $this->view = (object)array(
            'breadcrumb' => array(
                'Depolar' => route('backend.warehouse_list'),
            ),
        );
        $this->validation = array(
            [
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:50',
                'description' => 'nullable|string|max:255',
                'address' => 'required|string|max:255',
                'phone' => 'nullable|string|max:255',
                'email' => 'nullable|email|max:255',
                'max_weight_capacity' => 'nullable|numeric|min:0',
                'max_volume_capacity' => 'nullable|numeric|min:0',
                'temperature_min' => 'nullable|numeric|min:0',
                'temperature_max' => 'nullable|numeric|min:0',
                'is_active' => 'required|boolean',
            ],
            [
                'name.required' => 'Depo adı gereklidir',
                'name.string' => 'Depo adı metin olmalıdır',
                'name.max' => 'Depo adı en fazla 255 karakter olmalıdır',
                'code.required' => 'Depo kodu gereklidir',
                'code.string' => 'Depo kodu metin olmalıdır',
                'code.max' => 'Depo kodu en fazla 50 karakter olmalıdır',
                'description.string' => 'Depo açıklaması metin olmalıdır',
                'description.max' => 'Depo açıklaması en fazla 255 karakter olmalıdır',
                'address.required' => 'Depo adresi gereklidir',
                'address.string' => 'Depo adresi metin olmalıdır',
                'address.max' => 'Depo adresi en fazla 255 karakter olmalıdır',
                'phone.string' => 'Depo telefonu metin olmalıdır',
                'phone.max' => 'Depo telefonu en fazla 255 karakter olmalıdır',
                'email.email' => 'Depo e-postası geçerli bir e-posta adresi olmalıdır',
                'email.max' => 'Depo e-postası en fazla 255 karakter olmalıdır',
                'max_weight_capacity.numeric' => 'Maksimum ağırlık kapasitesi sayı olmalıdır',
                'max_weight_capacity.min' => 'Maksimum ağırlık kapasitesi 0\'dan büyük olmalıdır',
                'max_volume_capacity.numeric' => 'Maksimum hacim kapasitesi sayı olmalıdır',
                'max_volume_capacity.min' => 'Maksimum hacim kapasitesi 0\'dan büyük olmalıdır',
                'temperature_min.numeric' => 'Minimum sıcaklık sayı olmalıdır',
                'temperature_min.min' => 'Minimum sıcaklık 0\'dan büyük olmalıdır',
                'temperature_max.numeric' => 'Maksimum sıcaklık sayı olmalıdır',
                'temperature_max.min' => 'Maksimum sıcaklık 0\'dan büyük olmalıdır',
                'is_active.required' => 'Aktiflik durumu seçilmedi',
                'is_active.boolean' => 'Aktiflik durumu metin formatında olmalıdır',
            ],
        );

        parent::__construct();
    }

    public function detail(Request $request, $warehouse_id = null, $unique = null)
    {
        $warehouse = Warehouse::with(['locations', 'stocks.product.unit', 'stocks.variant', 'stocks.warehouseLocation'])->find($warehouse_id);

        if (!$warehouse) {
            return redirect()->route('backend.warehouse_list')->with('error', 'Depo bulunamadı');
        }

        if ($request->has('datatable')) {
            $type = $request->get('datatable');

            if ($type == 'locations') {
                $query = $warehouse->locations()->with('parent');
                return app('datatables')->of($query)
                    ->addIndexColumn()
                    ->editColumn('is_active', function ($row) {
                        return $row->is_active ? 'Aktif' : 'Pasif';
                    })
                    ->rawColumns([])
                    ->make(true);
            }

            if ($type == 'stocks') {
                $query = $warehouse->stocks()
                    ->select('stocks.*')
                    ->with(['product.unit', 'variant', 'warehouseLocation']);
                return app('datatables')->of($query)
                    ->editColumn('quantity', function ($item) {
                        $unit = $item->product ? $item->product->unit : null;
                        return formatQuantityWithUnit($item->quantity, $unit);
                    })
                    ->addColumn('product_name', function ($item) {
                        return $item->product ? $item->product->name : '-';
                    })
                    ->addColumn('variant_name', function ($item) {
                        return $item->variant ? $item->variant->name : '-';
                    })
                    ->addColumn('location_name', function ($item) {
                        return $item->warehouseLocation ? $item->warehouseLocation->name : 'Ana Lokasyon';
                    })
                    ->addIndexColumn()
                    ->rawColumns([])
                    ->make(true);
            }
        }

        return view("backend.$this->page.detail", compact('warehouse'));
    }
}
