@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.stock_movement_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Stok Kodu</th>
                            <th scope="col">Ürün</th>
                            <th scope="col">Varyant</th>
                            <th scope="col">Depo</th>
                            <th scope="col">Lokasyon</th>
                            <th scope="col" class="text-center">Mevcut Stok</th>
                            <th scope="col" class="text-center">Kullanılabilir Stok</th>
                            <th scope="col" class="text-center">Net Rezerve</th>
                            <th scope="col" class="text-center">Rezervasyon Detayı</th>
                            <th scope="col" class="text-center">Durum</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'sku',
                        name: 'sku',
                        className: 'text-center',
                        orderable: false,
                        searchable: true,
                    },
                    {
                        data: 'product.name',
                        name: 'product.name',
                        className: 'text-center',
                    },
                    {
                        data: 'variant.name',
                        name: 'variant.name',
                        className: 'text-center',
                    },
                    {
                        data: 'warehouse.name',
                        name: 'warehouse.name',
                        className: 'text-center',
                    },
                    {
                        render: function(data, type, row) {
                            return row.warehouse_location ? row.warehouse_location.name : '-';
                        },
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: 'quantity',
                        name: 'quantity',
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: 'available_quantity',
                        name: 'available_quantity',
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: 'reserved_quantity',
                        name: 'reserved_quantity',
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: 'reserved_info',
                        name: 'reserved_info',
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                    },
                    {
                        render: function(data, type, row) {
                            return row.is_active == 'Aktif' ?
                                '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm"> Aktif </span>' :
                                '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm"> Pasif </span>';
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                    },
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 10,
            });
            $('[filter-name]').change(function() {
                $("[datatable]").DataTable().ajax.reload();
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");
        });
    </script>
@endsection
