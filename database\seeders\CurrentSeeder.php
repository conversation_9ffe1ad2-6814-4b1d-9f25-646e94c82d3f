<?php

namespace Database\Seeders;

use App\Models\Current;
use App\Models\CurrentType;
use App\Models\Insutationtype;
use App\Models\Country;
use App\Models\City;
use App\Models\District;
use App\Models\TaxOffice;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $currentTypes = CurrentType::all()->keyBy('name');
            $institutionTypes = Insutationtype::all();
            $countries = Country::all();
            $cities = City::all();
            $districts = District::all();
            $taxOffices = TaxOffice::all();

            if ($currentTypes->isEmpty() || $cities->isEmpty()) {
                $this->command->warn('Gerekli veriler bulunamadı! Önce CurrentTypeSeeder ve diğer seeder\'ları çalıştırın.');
                return;
            }

            $defaultCountry = $countries->where('name', 'Türkiye')->first() ?? $countries->first();
            $defaultCity = $cities->where('name', 'İstanbul')->first() ?? $cities->first();
            $defaultDistrict = $districts->where('city_id', $defaultCity->id)->first() ?? $districts->first();
            $defaultTaxOffice = $taxOffices->where('city_id', $defaultCity->id)->first() ?? $taxOffices->first();
            $defaultInstitutionType = $institutionTypes->first();

            $suppliers = [
                [
                    'title' => 'ABC Teknoloji Ltd. Şti.',
                    'short_description' => 'Elektronik ürün tedarikçisi',
                    'current_type_id' => $currentTypes->get('Satıcı')->id ?? 1,
                    'address' => 'Maslak Mah. Teknoloji Cad. No:123 Sarıyer',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 123 45 67',
                    'tax_number' => '1234567890',
                ],
                [
                    'title' => 'Mega Dağıtım A.Ş.',
                    'short_description' => 'Toptan gıda dağıtım',
                    'current_type_id' => $currentTypes->get('Satıcı')->id ?? 1,
                    'address' => 'Atatürk Mah. Sanayi Cad. No:45 Esenyurt',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 234 56 78',
                    'tax_number' => '2345678901',
                ],
                [
                    'title' => 'Tekstil Dünyası Ltd. Şti.',
                    'short_description' => 'Giyim ve tekstil ürünleri',
                    'current_type_id' => $currentTypes->get('Satıcı')->id ?? 1,
                    'address' => 'Organize Sanayi Bölgesi 1. Cad. No:78 Başakşehir',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 345 67 89',
                    'tax_number' => '3456789012',
                ],
            ];

            $customers = [
                [
                    'title' => 'XYZ Market Zincirleri A.Ş.',
                    'short_description' => 'Perakende market zinciri',
                    'current_type_id' => $currentTypes->get('Alıcı')->id ?? 2,
                    'address' => 'Levent Mah. Büyükdere Cad. No:199 Şişli',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 456 78 90',
                    'tax_number' => '4567890123',
                ],
                [
                    'title' => 'Online Satış Platformu Ltd. Şti.',
                    'short_description' => 'E-ticaret platformu',
                    'current_type_id' => $currentTypes->get('Alıcı')->id ?? 2,
                    'address' => 'Maslak Mah. Digital Plaza No:88 Sarıyer',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 567 89 01',
                    'tax_number' => '5678901234',
                ],
                [
                    'title' => 'Kurumsal Çözümler A.Ş.',
                    'short_description' => 'B2B kurumsal müşteri',
                    'current_type_id' => $currentTypes->get('Alıcı')->id ?? 2,
                    'address' => 'Ümraniye Mah. Kurumsal Cad. No:55 Ümraniye',
                    'email' => '<EMAIL>',
                    'phone' => '+90 216 123 45 67',
                    'tax_number' => '6789012345',
                ],
                [
                    'title' => 'Ahmet Yılmaz',
                    'short_description' => 'Bireysel müşteri',
                    'current_type_id' => $currentTypes->get('Alıcı')->id ?? 2,
                    'address' => 'Kadıköy Mah. Bağdat Cad. No:123/5 Kadıköy',
                    'email' => '<EMAIL>',
                    'phone' => '+90 532 111 22 33',
                    'tax_number' => '11111111111',
                ],
            ];

            $both = [
                [
                    'title' => 'Çok Yönlü Ticaret Ltd. Şti.',
                    'short_description' => 'Hem alıcı hem satıcı firma',
                    'current_type_id' => $currentTypes->get('Hem Alıcı Hem Satıcı')->id ?? 3,
                    'address' => 'Mecidiyeköy Mah. Ticaret Cad. No:99 Şişli',
                    'email' => '<EMAIL>',
                    'phone' => '+90 212 789 01 23',
                    'tax_number' => '7890123456',
                ],
            ];

            $allCurrents = array_merge($suppliers, $customers, $both);

            foreach ($allCurrents as $currentData) {
                Current::create([
                    'title' => $currentData['title'],
                    'short_description' => $currentData['short_description'],
                    'instutation_id' => $defaultInstitutionType->id ?? 1,
                    'current_type_id' => $currentData['current_type_id'],
                    'country_id' => $defaultCountry->id ?? 1,
                    'city_id' => $defaultCity->id ?? 1,
                    'tax_offices_id' => $defaultTaxOffice->id ?? 1,
                    'address' => $currentData['address'],
                    'district_id' => $defaultDistrict->id ?? 1,
                    'email' => $currentData['email'],
                    'phone' => $currentData['phone'],
                    'tax_number' => $currentData['tax_number'],
                    'is_active' => 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ]);
            }

            $this->command->info('Current (Cari Hesap) verileri başarıyla oluşturuldu.');
        });
    }
}
