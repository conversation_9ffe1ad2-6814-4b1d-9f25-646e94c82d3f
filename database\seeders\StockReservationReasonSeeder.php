<?php

namespace Database\Seeders;

use App\Models\StockReservationReason;
use App\Models\StockReservationType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StockReservationReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createReasons();
        });

        $this->command->info('StockReservationReason verileri başarıyla oluşturuldu.');
    }

    /**
     * Rezervasyon nedenlerini oluştur
     */
    private function createReasons(): void
    {
        // Rezervasyon tiplerini al
        $salesOrderType = StockReservationType::where('name', 'Satış')->first();
        $purchaseType = StockReservationType::where('name', 'Alış')->first();
        $salesReturnType = StockReservationType::where('name', 'Satış İade')->first();
        $purchaseReturnType = StockReservationType::where('name', '<PERSON><PERSON>ş İade')->first();
        $productionType = StockReservationType::where('name', 'Üretim')->first();
        $transferType = StockReservationType::where('name', 'Transfer')->first();
        $otherType = StockReservationType::where('name', 'Diğer')->first();

        if (!$salesOrderType || !$purchaseType || !$salesReturnType || !$purchaseReturnType || !$productionType || !$transferType || !$otherType) {
            $this->command->warn('StockReservationType kayıtları bulunamadı! Önce StockReservationTypeSeeder\'ı çalıştırın.');
            return;
        }

        $reasons = [
            [
                'name' => 'Müşteri Siparişi',
                'description' => 'Müşteri siparişi için rezerve',
                'stock_reservation_type_id' => $salesOrderType->id
            ],
            [
                'name' => 'Öncelikli Sipariş',
                'description' => 'Öncelikli sipariş için rezerve',
                'stock_reservation_type_id' => $salesOrderType->id
            ],
            [
                'name' => 'Ürün Tedarik',
                'description' => 'Alış siparişi için rezerve',
                'stock_reservation_type_id' => $purchaseType->id
            ],
            [
                'name' => 'Üretim İhtiyacı',
                'description' => 'Üretim ihtiyacı için rezerve',
                'stock_reservation_type_id' => $productionType->id
            ],
            [
                'name' => 'Transfer',
                'description' => 'Transfer için rezerve',
                'stock_reservation_type_id' => $transferType->id
            ],
            [
                'name' => 'Dahili Kullanım',
                'description' => 'Dahili kullanım için rezerve',
                'stock_reservation_type_id' => $otherType->id
            ],
            [
                'name' => 'Diğer',
                'description' => 'Diğer rezervasyonlar',
                'stock_reservation_type_id' => $otherType->id
            ],
            [
                'name' => 'Satış İade',
                'description' => 'Satış iade için rezerve',
                'stock_reservation_type_id' => $salesReturnType->id
            ],
            [
                'name' => 'Alış İade',
                'description' => 'Alış iade için rezerve',
                'stock_reservation_type_id' => $purchaseReturnType->id
            ],
        ];

        foreach ($reasons as $reason) {
            StockReservationReason::updateOrCreate(
                ['name' => $reason['name']],
                $reason
            );
        }
    }
}
