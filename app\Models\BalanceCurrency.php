<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class BalanceCurrency extends BaseModel
{
    use SoftDeletes;

    protected $table = 'balance_currencies';

    protected $guarded = [];

    protected $casts = [
        'debit_balance' => 'float',
        'credit_balance' => 'float'
    ];
    public function balance()
    {
        return $this->belongsTo(Balance::class);
    }
}
