<aside class="sidebar">
    <button type="button" class="sidebar-close-btn">
        <iconify-icon icon="radix-icons:cross-2"></iconify-icon>
    </button>
    <div>
        <a href="{{ route('backend.index') }}" class="sidebar-logo">
            <img src="{{ asset('assets/images/logo/ic_kodlio.png') }}" alt="site logo" class="light-logo">
            <img src="{{ asset('assets/images/logo/ic_white.png') }}" alt="site logo" class="dark-logo">
            <img src="{{ asset('assets/images/logo/ic_kodlio.png') }}" alt="site logo" class="logo-icon">
        </a>
    </div>
    <div class="sidebar-menu-area">
        <ul class="sidebar-menu" id="sidebar-menu">
            <li>
                <a href="{{ route('backend.index') }}">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="menu-icon"></iconify-icon>
                    <span>Anasayfa</span>
                </a>
            </li>
            <!-- Cariler Başlığı -->
            @if (hasPermission([
                    'current',
                    'balance',
                    'account_vouchers',
                    'current_financial_transactions',
                    'current_stock_movements',
                ]))
                <li
                    class="dropdown {{ activeMenu(['current', 'balance', 'account_vouchers', 'current_financial_transactions', 'current_stock_movements']) }}">
                    <a class="nav-link d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <iconify-icon icon="mdi:account-group" class="menu-icon"></iconify-icon>
                            <span>Cariler</span>
                        </div>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['current']))
                            <li class="{{ activeMenuItem(['current']) }}">
                                <a href="{{ route('backend.current_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['current']) }}">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Cari
                                        Hesaplar</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['balance']))
                            <li class="{{ activeMenuItem(['balance']) }}">
                                <a href="{{ route('backend.balance_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['balance']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Cari
                                        Bakiye Tanımlama</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['current']))
                            <li class="{{ activeMenuItem(['account_vouchers']) }}">
                                <a href="{{ route('backend.account_vouchers_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['account_vouchers']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Cari Hesap
                                        Fişleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['current']))
                            <li class="{{ activeMenuItem(['current_financial_transactions']) }}">
                                <a href="{{ route('backend.current_financial_transactions_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['current_financial_transactions']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Cari Finansal
                                        Hareketler</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['current']))
                            <li class="{{ activeMenuItem(['current_stock_movements']) }}">
                                <a href="{{ route('backend.current_stock_movements_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['current_stock_movements']) }}">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Cari Stok
                                        Hareketleri</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Finans Başlığı -->
            @if (hasPermission(['expense_voucher', 'payment_transaction', 'receipt_transaction', 'invoice', 'return_purchase_invoice', 'return_sale_invoice']))
                <li
                    class="dropdown {{ activeMenu(['invoices', 'purchase_invoices', 'return_purchase_invoice', 'return_sale_invoice', 'receipt_transaction', 'payment_transaction', 'sale_waybill', 'purchase_waybills', 'expense_voucher']) }}">
                    <a class="nav-link d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <iconify-icon icon="mdi:finance" class="menu-icon"></iconify-icon>
                            <span>Fatura</span>
                        </div>
                        <i class="fas fa-chevron-down menu-arrow"></i>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['invoices']) }}">
                                <a href="{{ route('backend.invoices_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['invoices']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Satış
                                        Faturaları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['purchase_invoices']) }}">
                                <a href="{{ route('backend.purchase_invoices_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['purchase_invoices']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        Faturaları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['return_purchase_invoice']) }}">
                                <a href="{{ route('backend.return_purchase_invoice_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['return_purchase_invoice']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        İade Faturaları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['return_sale_invoice']) }}">
                                <a href="{{ route('backend.return_sale_invoice_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['return_sale_invoice']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Satış
                                        İade Faturaları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['receipt_transaction']))
                            <li class="{{ activeMenuItem(['receipt_transaction']) }}">
                                <a href="{{ route('backend.receipt_transaction_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['receipt_transaction']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Tahsilat
                                        İşlemleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['payment_transaction']))
                            <li class="{{ activeMenuItem(['payment_transaction']) }}">
                                <a href="{{ route('backend.payment_transaction_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['payment_transaction']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Ödeme
                                        İşlemleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['sale_waybill']) }}">
                                <a href="{{ route('backend.sale_waybill_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['sale_waybill']) }}">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Satış
                                        İrsaliyeleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['invoice']))
                            <li class="{{ activeMenuItem(['purchase_waybills']) }}">
                                <a href="{{ route('backend.purchase_waybills_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['purchase_waybills']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış
                                        İrsaliyeleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['expense_voucher']))
                            <li class="{{ activeMenuItem(['expense_voucher']) }}">
                                <a href="{{ route('backend.expense_voucher_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['expense_voucher']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Masraf
                                        Fişleri</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Sipariş Yönetimi -->
            @if (hasPermission(['order_received', 'order_given']))
                <li class="dropdown {{ activeMenu(['order_received', 'order_given']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="material-symbols:order-approve-outline" class="menu-icon"></iconify-icon>
                        <span>Sipariş</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['order_received']))
                            <li class="{{ activeMenuItem(['order_received']) }}">
                                <a href="{{ route('backend.order_received_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['order_received']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Alınan
                                        Sipariş</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['order_given']))
                            <li class="{{ activeMenuItem(['order_given']) }}">
                                <a href="{{ route('backend.order_given_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['order_given']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Verilen
                                        Sipariş</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Teklif Yönetimi -->
            @if (hasPermission(['offer_received', 'offer_given']))
                <li class="dropdown {{ activeMenu(['offer_received', 'offer_given']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:file-document-outline" class="menu-icon"></iconify-icon>
                        <span>Teklif</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['offer_received']))
                            <li class="{{ activeMenuItem(['offer_received']) }}">
                                <a href="{{ route('backend.offer_received_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['offer_received']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Alınan
                                        Teklif</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['offer_given']))
                            <li class="{{ activeMenuItem(['offer_given']) }}">
                                <a href="{{ route('backend.offer_given_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['offer_given']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Verilen
                                        Teklif</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Marka ve Kategori Yönetimi -->
            @if (hasPermission(['brand', 'category']))
                <li class="dropdown {{ activeMenu(['brand', 'category']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:tag-multiple" class="menu-icon"></iconify-icon>
                        <span>Marka & Kategori</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['brand']))
                            <li class="{{ activeMenuItem(['brand']) }}">
                                <a href="{{ route('backend.brand_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['brand']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Markalar</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['category']))
                            <li class="{{ activeMenuItem(['category']) }}">
                                <a href="{{ route('backend.category_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['category']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Kategoriler</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Ürün Yönetimi -->
            @if (hasPermission(['product', 'product_variant']))
                <li class="dropdown {{ activeMenu(['product', 'product_variant']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:package" class="menu-icon"></iconify-icon>
                        <span>Ürün Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['product']))
                            <li class="{{ activeMenuItem(['product']) }}">
                                <a href="{{ route('backend.product_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['product']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Ürünler</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['product_variant']))
                            <li class="{{ activeMenuItem(['product_variant']) }}">
                                <a href="{{ route('backend.product_variant_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['product_variant']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Ürün
                                        Varyantları</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif


            <!-- Depo Yönetimi -->
            @if (hasPermission(['warehouse', 'warehouse_location']))
                <li class="dropdown {{ activeMenu(['warehouse', 'warehouse_location']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:warehouse" class="menu-icon"></iconify-icon>
                        <span>Depo Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['warehouse']))
                            <li class="{{ activeMenuItem(['warehouse']) }}">
                                <a href="{{ route('backend.warehouse_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['warehouse']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Depolar</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['warehouse_location']))
                            <li class="{{ activeMenuItem(['warehouse_location']) }}">
                                <a href="{{ route('backend.warehouse_location_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['warehouse_location']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Depo
                                        Lokasyonları</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Stok Yönetimi -->
            @if (hasPermission(['stock', 'stock_movement', 'stock_reservation', 'physical_count', 'stock_period']))
                <li
                    class="dropdown {{ activeMenu(['stock', 'stock_movement', 'stock_reservation', 'physical_count', 'stock_period']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:archive" class="menu-icon"></iconify-icon>
                        <span>Stok Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['stock']))
                            <li class="{{ activeMenuItem(['stock']) }}">
                                <a href="{{ route('backend.stock_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['stock']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Stoklar</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['stock_movement']))
                            <li class="{{ activeMenuItem(['stock_movement']) }}">
                                <a href="{{ route('backend.stock_movement_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['stock_movement']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Stok
                                        Hareketleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['stock_reservation']))
                            <li class="{{ activeMenuItem(['stock_reservation']) }}">
                                <a href="{{ route('backend.stock_reservation_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['stock_reservation']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Stok
                                        Rezervasyonları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['physical_count']))
                            <li class="{{ activeMenuItem(['physical_count']) }}">
                                <a href="{{ route('backend.physical_count_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['physical_count']) }}">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Fiziksel
                                        Sayım</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['stock_period']))
                            <li class="{{ activeMenuItem(['stock_period']) }}">
                                <a href="{{ route('backend.stock_period_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['stock_period']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Stok
                                        Dönemleri</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            <!-- Sistem Yönetimi -->
            @if (hasPermission(['user', 'role']))
                <li class="dropdown {{ activeMenu(['user', 'role']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:cog" class="menu-icon"></iconify-icon>
                        <span>Sistem Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['user']))
                            <li class="{{ activeMenuItem(['user']) }}">
                                <a href="{{ route('backend.user_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['user']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Kullanıcılar</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['role']))
                            <li class="{{ activeMenuItem(['role']) }}">
                                <a href="{{ route('backend.role_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['role']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i>
                                    <span>Roller</span>
                                </a>
                            </li>
                        @endif
                        <li class="{{ activeMenuItem(['setting']) }}">
                            <a href="{{ route('backend.setting_form') }}"
                                class="d-flex align-items-center {{ activeMenuItem(['setting']) }}">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i>
                                <span>Ayarlar</span>
                            </a>
                        </li>
                    </ul>
                </li>
            @endif
            <!-- Tanımlamalar -->
            @if (hasPermission(['unit', 'unit_type', 'currency_type', 'vat_rate', 'exchange_rate', 'payment_term']))
                <li class="dropdown {{ activeMenu(['unit', 'unit_type', 'currency_type', 'vat_rate', 'exchange_rate', 'payment_term']) }}">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:ruler" class="menu-icon"></iconify-icon>
                        <span>Tanımlamalar</span>
                    </a>
                    <ul class="sidebar-submenu">
                        @if (hasPermission(['unit']))
                            <li class="{{ activeMenuItem(['unit']) }}">
                                <a href="{{ route('backend.unit_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['unit']) }}">
                                    <i
                                        class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Birimler</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['unit_type']))
                            <li class="{{ activeMenuItem(['unit_type']) }}">
                                <a href="{{ route('backend.unit_type_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['unit_type']) }}">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Birim
                                        Türleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['currency_type']))
                            <li class="{{ activeMenuItem(['currency_type']) }}">
                                <a href="{{ route('backend.currency_type_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['currency_type']) }}">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Para
                                        Birimleri</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['exchange_rate']))
                            <li class="{{ activeMenuItem(['exchange_rate']) }}">
                                <a href="{{ route('backend.exchange_rate_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['exchange_rate']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Döviz
                                        Kurları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['vat_rate']))
                            <li class="{{ activeMenuItem(['vat_rate']) }}">
                                <a href="{{ route('backend.vat_rate_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['vat_rate']) }}">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>KDV
                                        Oranları</span>
                                </a>
                            </li>
                        @endif
                        @if (hasPermission(['payment_term']))
                            <li class="{{ activeMenuItem(['payment_term']) }}">
                                <a href="{{ route('backend.payment_term_list') }}"
                                    class="d-flex align-items-center {{ activeMenuItem(['payment_term']) }}">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Vade
                                        Tanımlamaları</span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
        </ul>
    </div>
</aside>
