@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 header-title">
                            <iconify-icon icon="f7:user-shield"></iconify-icon>
                            {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                        </h5>
                        <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn back-list-btn btn-sm">
                            <iconify-icon icon="f7:arrow-left"></iconify-icon> <PERSON>eye Dön
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-md-6">
                                <label class="form-label">Rol Adı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" id="name" placeholder="Rol Adı"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <select class="form-select form-control" id="is_active" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>
                                            Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>
                                            Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="d-flex justify-content-end mb-3">
                                    <button type="button" class="btn btn-primary btn-sm select-all-btn"
                                        style="background-color: #05374f; border-color: #05374f;">
                                        <iconify-icon icon="f7:check-double"></iconify-icon> Tümünü Seç
                                    </button>
                                </div>
                                <div class="row gy-3">
                                    @foreach ($routes as $category => $categoryRoutes)
                                        <div class="col-md-4">
                                            <div class="card permission-group h-100 border-0 shadow-sm">
                                                <div class="card-header category-header py-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h6 class="mb-0 text-white fw-bold text-lg">{{ $category }}</h6>
                                                        <button type="button"
                                                            class="btn btn-light btn-sm category-select-btn"
                                                            data-category="{{ $category }}">
                                                            <iconify-icon icon="f7:check-double"></iconify-icon> <span class="text-xs">Tümünü Seç</span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    @foreach ($categoryRoutes as $route)
                                                        <div class="form-check mb-2">
                                                            <input type="checkbox"
                                                                class="form-check-input route-checkbox role-check"
                                                                data-category="{{ $category }}" name="permissions[]"
                                                                value="{{ $route->route_name }}"
                                                                id="route_{{ $route->route_name }}"
                                                                @if (isset($item) &&
                                                                        is_array(json_decode($item->permissions)) &&
                                                                        in_array($route->route_name, json_decode($item->permissions))) checked @endif>
                                                            <label class="form-check-label px-1"
                                                                for="route_{{ $route->route_name }}">
                                                                {{ $route->name }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('backend.' . $container->page . '_list') }}"
                                        class="btn btn-secondary-600">
                                        <iconify-icon icon="f7:times"></iconify-icon> İptal
                                    </a>
                                    <button type="submit" class="btn btn-primary-600 submit-btn">
                                        <iconify-icon icon="f7:save"></iconify-icon> Kaydet
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form doğrulama
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });

            // Genel tümünü seç/kaldır fonksiyonu
            const selectAllButton = document.querySelector('.select-all-btn');
            let allSelected = false;

            // Kategori bazlı tümünü seç/kaldır fonksiyonu
            const categoryButtons = document.querySelectorAll('.category-select-btn');
            const categoryStates = {};

            // Sayfa yüklendiğinde mevcut durumu kontrol et
            function initializeButtonStates() {
                const allCheckboxes = document.querySelectorAll('.route-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.route-checkbox:checked');
                
                // Genel buton durumunu ayarla
                if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                    allSelected = true;
                    selectAllButton.innerHTML = '<iconify-icon icon="f7:times"></iconify-icon> Tüm Yetkileri Kaldır';
                } else {
                    allSelected = false;
                    selectAllButton.innerHTML = '<iconify-icon icon="f7:check-double"></iconify-icon> Tüm Yetkileri Seç';
                }

                // Kategori butonlarının durumunu ayarla
                categoryButtons.forEach(button => {
                    const category = button.dataset.category;
                    const categoryCheckboxes = document.querySelectorAll(`.route-checkbox[data-category="${category}"]`);
                    const categoryCheckedBoxes = document.querySelectorAll(`.route-checkbox[data-category="${category}"]:checked`);
                    
                    if (categoryCheckedBoxes.length === categoryCheckboxes.length && categoryCheckboxes.length > 0) {
                        categoryStates[category] = true;
                        button.innerHTML = '<iconify-icon icon="f7:times"></iconify-icon> <span class="text-xs">Tümünü Kaldır</span>';
                    } else {
                        categoryStates[category] = false;
                        button.innerHTML = '<iconify-icon icon="f7:check-double"></iconify-icon> <span class="text-xs">Tümünü Seç</span>';
                    }
                });
            }

            // Sayfa yüklendiğinde durumu initialize et
            initializeButtonStates();

            selectAllButton.addEventListener('click', function() {
                const allCheckboxes = document.querySelectorAll('.route-checkbox');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = !allSelected;
                });

                // Kategori butonlarını güncelle
                categoryButtons.forEach(button => {
                    const category = button.dataset.category;
                    categoryStates[category] = !allSelected;
                    button.innerHTML = allSelected ?
                        '<iconify-icon icon="f7:check-double"></iconify-icon> <span class="text-xs">Tümünü Seç</span>' :
                        '<iconify-icon icon="f7:times"></iconify-icon> <span class="text-xs">Tümünü Kaldır</span>';
                });

                selectAllButton.innerHTML = allSelected ?
                    '<iconify-icon icon="f7:check-double"></iconify-icon> Tüm Yetkileri Seç' :
                    '<iconify-icon icon="f7:times"></iconify-icon> Tüm Yetkileri Kaldır';

                allSelected = !allSelected;
            });

            categoryButtons.forEach(button => {
                const category = button.dataset.category;

                button.addEventListener('click', function() {
                    const checkboxes = document.querySelectorAll(
                        `.route-checkbox[data-category="${category}"]`);
                    const currentState = categoryStates[category];

                    checkboxes.forEach(checkbox => {
                        checkbox.checked = !currentState;
                    });

                    button.innerHTML = currentState ?
                        '<iconify-icon icon="f7:check-double"></iconify-icon> <span class="text-xs">Tümünü Seç</span>' :
                        '<iconify-icon icon="f7:times"></iconify-icon> <span class="text-xs">Tümünü Kaldır</span>';

                    categoryStates[category] = !currentState;

                    // Tüm kategorilerin durumunu kontrol et
                    const allChecked = Array.from(document.querySelectorAll('.route-checkbox'))
                        .every(checkbox => checkbox.checked);
                    const allUnchecked = Array.from(document.querySelectorAll('.route-checkbox'))
                        .every(checkbox => !checkbox.checked);

                    if (allChecked) {
                        allSelected = true;
                        selectAllButton.innerHTML =
                            '<iconify-icon icon="f7:times"></iconify-icon> Tüm Yetkileri Kaldır';
                    } else if (allUnchecked) {
                        allSelected = false;
                        selectAllButton.innerHTML =
                            '<iconify-icon icon="f7:check-double"></iconify-icon> Tüm Yetkileri Seç';
                    }
                });
            });

            // Yetki gruplarına hover efekti
            const permissionGroups = document.querySelectorAll('.permission-group');
            permissionGroups.forEach(group => {
                group.addEventListener('mouseenter', () => {
                    group.classList.add('shadow');
                });
                group.addEventListener('mouseleave', () => {
                    group.classList.remove('shadow');
                });
            });
        });
    </script>

    <style>
        .permission-group {
            transition: all 0.3s ease;
        }

        .permission-group:hover {
            transform: translateY(-2px);
        }

        .category-header {
            background: linear-gradient(45deg, var(--category-color-start), var(--category-color-end));
        }

        .permission-group:nth-child(3n+1) .category-header {
            --category-color-start: #05374f;
            --category-color-end: #90a6b1;
        }

        .permission-group:nth-child(3n+2) .category-header {
            --category-color-start: #0093E9;
            --category-color-end: #80D0C7;
        }

        .permission-group:nth-child(3n+3) .category-header {
            --category-color-start: #8EC5FC;
            --category-color-end: #E0C3FC;
        }

        .form-floating>.form-control,
        .form-floating>.form-select {
            height: calc(3.5rem + 2px);
            line-height: 1.25;
        }

        .form-check-input:checked {
            background-color: #05374f;
            border-color: #05374f;
        }

        .category-select-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }

        .category-select-btn:hover {
            background-color: rgba(255, 255, 255, 0.9) !important;
        }
    </style>
@endsection