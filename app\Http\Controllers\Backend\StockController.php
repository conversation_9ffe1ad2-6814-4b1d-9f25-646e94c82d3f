<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Stock;
use App\Models\StockBatch;

use App\Models\StockReservation;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Stok Durumu';
        $this->page = 'stock';
        $this->model = new Stock();
        $this->relation = ['product.unit', 'variant', 'warehouse', 'warehouseLocation', 'stockBatch'];
        $this->listQuery = Stock::select('stocks.*');

        $this->view = (object) array(
            'breadcrumb' => array(
                'Stok Durumu' => route('backend.stock_list'),
            ),
        );

        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('batches', StockBatch::active()->get());
        view()->share('stockReservations', StockReservation::active()->get());

        parent::__construct();
    }

    public function form(Request $request, $unique = NULL)
    {
        return redirect()->route('backend.stock_movement_list')
            ->with('info', 'Stok işlemleri sadece stok hareketleri üzerinden yapılabilir');
    }

    public function datatableHook($obj)
    {
        return $obj->filterColumn('sku', function ($query, $keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->whereHas('product', function ($productQuery) use ($keyword) {
                    $productQuery->where('sku', 'like', "%{$keyword}%");
                })->orWhereHas('variant', function ($variantQuery) use ($keyword) {
                    $variantQuery->where('sku', 'like', "%{$keyword}%");
                });
            });
        })
            ->editColumn('sku', function ($item) {
                if ($item->variant && $item->variant->sku) {
                    return $item->variant->sku;
                } elseif ($item->product && $item->product->sku) {
                    return $item->product->sku;
                }
                return '-';
            })
            ->editColumn('quantity', function ($item) {
                $unit = $item->product ? $item->product->unit : null;
                $quantity = formatQuantityWithUnit($item->quantity, $unit);
                return '<span class="text-info fw-bold">' . $quantity . '</span>';
            })
            ->addColumn('available_quantity', function ($item) {
                $availableQuantity = $this->calculateAvailableQuantity($item);
                $unit = $item->product ? $item->product->unit : null;
                $formatted = formatQuantityWithUnit($availableQuantity, $unit);

                $textClass = 'text-success';
                if ($availableQuantity <= 0) {
                    $textClass = 'text-danger';
                } elseif ($availableQuantity < ($item->quantity * 0.2)) {
                    $textClass = 'text-warning';
                }

                return '<span class="' . $textClass . ' fw-bold">' . $formatted . '</span>';
            })
            ->addColumn('reserved_info', function ($item) {
                $salesReservations = $this->getSalesReservations($item);
                $purchaseReservations = $this->getPurchaseReservations($item);
                $itemUnit = $item->product ? $item->product->unit : null;

                $info = '';
                if ($salesReservations > 0) {
                    $formatted = formatQuantityWithUnit($salesReservations, $itemUnit);
                    $info .= '<div class="text-danger fw-medium">Satış: -' . $formatted . '</div>';
                }
                if ($purchaseReservations > 0) {
                    $formatted = formatQuantityWithUnit($purchaseReservations, $itemUnit);
                    $info .= '<div class="text-success fw-medium">Alış: +' . $formatted . '</div>';
                }

                return $info ?: '<div class="text-muted fw-medium">Rezervasyon yok</div>';
            })
            ->addColumn('reserved_quantity', function ($item) {
                $reservedQuantity = $this->calculateReservedQuantity($item);
                $productUnit = $item->product ? $item->product->unit : null;
                $formatted = formatQuantityWithUnit(abs($reservedQuantity), $productUnit);

                if ($reservedQuantity > 0) {
                    return '<span class="text-danger fw-bold">-' . $formatted . '</span>';
                } elseif ($reservedQuantity < 0) {
                    return '<span class="text-success fw-bold">+' . $formatted . '</span>';
                } else {
                    return '<span class="text-muted fw-bold">0</span>';
                }
            })
            ->editColumn('expiry_date', function ($item) {
                if ($item->stockBatch && $item->stockBatch->expiry_date) {
                    return $item->stockBatch->expiry_date->format('d.m.Y');
                }
                return '-';
            })
            ->editColumn('batch_info', function ($item) {
                if ($item->stockBatch) {
                    $info = 'Lot: ' . $item->stockBatch->lot_number;
                    if ($item->stockBatch->batch_number) {
                        $info .= ' / Batch: ' . $item->stockBatch->batch_number;
                    }
                    return $info;
                }
                return '-';
            })
            ->rawColumns(['quantity', 'available_quantity', 'reserved_quantity', 'reserved_info']);
    }

    private function calculateAvailableQuantity($stock, $excludeReservationId = null)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        if ($excludeReservationId) {
            $baseQuery->where('id', '!=', $excludeReservationId);
        }

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');

        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $stock->quantity - $salesReservations + $purchaseReservations;
    }

    private function calculateReservedQuantity($stock)
    {
        $baseQuery = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        $salesReservations = (clone $baseQuery)->where('reservation_type_id', 1)->sum('quantity');
        $purchaseReservations = (clone $baseQuery)->where('reservation_type_id', 2)->sum('quantity');

        return $salesReservations - $purchaseReservations;
    }

    private function getSalesReservations($stock)
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('reservation_type_id', 1)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');
    }

    private function getPurchaseReservations($stock)
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('reservation_type_id', 2)
            ->where('is_active', 1)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');
    }
}
