<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->createBrands();
        });

        $this->command->info('Brand verileri başarıyla oluşturuldu.');
    }

    /**
     * Markaları oluştur
     */
    private function createBrands(): void
    {
        $brands = $this->getBrandData();

        foreach ($brands as $brandData) {
            // Marka kodunu oluştur
            $code = $this->generateBrandCode($brandData['name']);

            // Markayı oluştur veya güncelle
            Brand::updateOrCreate(
                ['name' => $brandData['name']],
                [
                    'name' => $brandData['name'],
                    'code' => $code,
                    'description' => $brandData['description'] ?? null,
                    'is_active' => $brandData['is_active'] ?? 1,
                    'created_by' => 1,
                    'updated_by' => 1,
                ]
            );
        }
    }

    /**
     * Marka kodu oluştur
     */
    private function generateBrandCode(string $name): string
    {
        // Türkçe karakterleri değiştir
        $search = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
        $replace = ['c', 'g', 'i', 'o', 's', 'u', 'C', 'G', 'I', 'I', 'O', 'S', 'U'];
        $name = str_replace($search, $replace, $name);

        // Sadece harf ve rakamları al, boşlukları kaldır
        $code = preg_replace('/[^A-Za-z0-9]/', '', $name);

        // İlk 10 karakteri al ve büyük harfe çevir
        $code = strtoupper(substr($code, 0, 10));

        // Kod boşsa, varsayılan kod oluştur
        if (empty($code)) {
            $code = 'BRAND';
        }

        // Eğer kod zaten varsa, sayı ekle
        $originalCode = $code;
        $counter = 1;

        while (Brand::where('code', $code)->exists()) {
            $code = $originalCode . $counter;
            $counter++;

            // Kod çok uzarsa kırp
            if (strlen($code) > 50) {
                $code = substr($originalCode, 0, 47) . $counter;
            }
        }

        return $code;
    }

    /**
     * Marka verilerini döndür
     */
    private function getBrandData(): array
    {
        return [
            // Elektronik Markaları
            [
                'name' => 'Apple',
                'description' => 'Dünyanın en değerli teknoloji şirketi',
                'is_active' => 1,
            ],
            [
                'name' => 'Samsung',
                'description' => 'Güney Kore merkezli teknoloji devi',
                'is_active' => 1,
            ],
            [
                'name' => 'Sony',
                'is_active' => 1,
            ],
            [
                'name' => 'LG',
                'is_active' => 1,
            ],
            [
                'name' => 'Lenovo',
                'is_active' => 1,
            ],
            [
                'name' => 'HP',
                'is_active' => 1,
            ],
            [
                'name' => 'Dell',
                'is_active' => 1,
            ],
            [
                'name' => 'Asus',
                'is_active' => 1,
            ],
            [
                'name' => 'MSI',
                'is_active' => 1,
            ],
            [
                'name' => 'Intel',
                'is_active' => 1,
            ],
            [
                'name' => 'AMD',
                'is_active' => 1,
            ],
            [
                'name' => 'NVIDIA',
                'is_active' => 1,
            ],
            [
                'name' => 'Logitech',
                'is_active' => 1,
            ],
            [
                'name' => 'Canon',
                'is_active' => 1,
            ],
            [
                'name' => 'Nikon',
                'is_active' => 1,
            ],

            // Beyaz Eşya Markaları
            [
                'name' => 'Bosch',
                'is_active' => 1,
            ],
            [
                'name' => 'Siemens',
                'is_active' => 1,
            ],
            [
                'name' => 'Arçelik',
                'is_active' => 1,
            ],
            [
                'name' => 'Beko',
                'is_active' => 1,
            ],
            [
                'name' => 'Vestel',
                'is_active' => 1,
            ],

            // Giyim Markaları
            [
                'name' => 'Nike',
                'description' => 'Dünyanın en büyük spor giyim markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Adidas',
                'description' => 'Üç şeritli dünya markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Puma',
                'description' => 'Alman spor giyim markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Zara',
                'description' => 'Hızlı moda öncüsü İspanyol markası',
                'is_active' => 1,
            ],
            [
                'name' => 'H&M',
                'description' => 'İsveç merkezli hızlı moda markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Mango',
                'description' => 'İspanyol moda markası',
                'is_active' => 1,
            ],
            [
                'name' => "Levi's",
                'description' => 'Kot pantolonun mucidi',
                'is_active' => 1,
            ],
            [
                'name' => 'LC Waikiki',
                'is_active' => 1,
            ],

            // Kozmetik Markaları
            [
                'name' => "L'Oréal",
                'is_active' => 1,
            ],
            [
                'name' => 'Maybelline',
                'is_active' => 1,
            ],
            [
                'name' => 'MAC',
                'is_active' => 1,
            ],
            [
                'name' => 'Nivea',
                'is_active' => 1,
            ],
            [
                'name' => 'Dove',
                'is_active' => 1,
            ],

            // Mobilya Markaları
            [
                'name' => 'IKEA',
                'is_active' => 1,
            ],
            [
                'name' => 'Bellona',
                'is_active' => 1,
            ],
            [
                'name' => 'İstikbal',
                'is_active' => 1,
            ],
            [
                'name' => 'Koçtaş',
                'is_active' => 1,
            ],

            // Oyuncak Markaları
            [
                'name' => 'LEGO',
                'is_active' => 1,
            ],
            [
                'name' => 'Barbie',
                'is_active' => 1,
            ],
            [
                'name' => 'Hot Wheels',
                'is_active' => 1,
            ],
            [
                'name' => 'Fisher-Price',
                'is_active' => 1,
            ],

            // Bisiklet Markaları
            [
                'name' => 'Bianchi',
                'description' => 'İtalyan bisiklet üreticisi',
                'is_active' => 1,
            ],
            [
                'name' => 'Giant',
                'description' => 'Dünyanın en büyük bisiklet üreticisi',
                'is_active' => 1,
            ],
            [
                'name' => 'Trek',
                'description' => 'Amerikan bisiklet markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Shimano',
                'description' => 'Bisiklet parçaları üreticisi',
                'is_active' => 1,
            ],

            // Spor Markaları
            [
                'name' => 'Decathlon',
                'description' => 'Fransız spor mağazalar zinciri',
                'is_active' => 1,
            ],
            [
                'name' => 'The North Face',
                'description' => 'Outdoor giyim ve ekipman markası',
                'is_active' => 1,
            ],
            [
                'name' => 'Columbia',
                'description' => 'Amerikan outdoor giyim markası',
                'is_active' => 1,
            ],

            // Kitap Yayınevi
            [
                'name' => 'Can Yayınları',
                'is_active' => 1,
            ],
            [
                'name' => 'İş Bankası Kültür Yayınları',
                'is_active' => 1,
            ],
            [
                'name' => 'Yapı Kredi Yayınları',
                'is_active' => 1,
            ],

            // Gıda Markaları
            [
                'name' => 'Ülker',
                'is_active' => 1,
            ],
            [
                'name' => 'Eti',
                'is_active' => 1,
            ],
            [
                'name' => 'Nestle',
                'is_active' => 1,
            ],
            [
                'name' => 'Coca-Cola',
                'is_active' => 1,
            ],
            [
                'name' => 'Pepsi',
                'is_active' => 1,
            ],

            // Pet Shop Markaları
            [
                'name' => 'Royal Canin',
                'is_active' => 1,
            ],
            [
                'name' => 'Pro Plan',
                'is_active' => 1,
            ],
            [
                'name' => 'Whiskas',
                'is_active' => 1,
            ],
            [
                'name' => 'Pedigree',
                'is_active' => 1,
            ],
        ];
    }
}
