<?php

namespace App\Http\Middleware;

use App\Models\Role;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class Permission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $onuser = Auth::user();
        $permissions = null;
        if ($onuser->role_id != 1) {
            if (!is_null($onuser->role)) {
                if ($onuser->role['permissions'] != null) {
                    $permissions = $onuser->role['permissions'];
                } else {
                    $user_role = Role::find($onuser->role_id);


                    if (!$user_role) {
                        Auth::logout();
                        Session::flush();

                        return redirect()->route('signin')->withError('Yetkiniz bulunmamaktadır!');
                    }

                    if ($user_role->permissions == null) {
                        Auth::logout();
                        Session::flush();

                        return redirect()->route('signin')->withError('Yetkiniz bulunmamaktadır!');
                    }

                    $permissions = $user_role->permissions;
                }

                if (!in_array(Route::currentRouteName(), json_decode($permissions, true))) {
                    return redirect()->route('backend.index')->withError('Sayfaya yetkiniz bulunmamaktadır!');
                }
            } else {
                Auth::logout();
                Session::flush();

                return redirect()->route('signin')->withError('Yetkiniz bulunmamaktadır!');
            }
        }

        view()->share('onuser', $onuser);

        return $next($request);
    }
}
