@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Ayarları';
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }}</h5>
                </div>

                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save') }}" method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Para Gösterimi - Ondalık Basamak Sayısı</label>
                                    <div class="icon-field">
                                        <span class="icon">
                                            <iconify-icon icon="mdi:decimal"></iconify-icon>
                                        </span>
                                        <select class="form-control form-select" name="decimal_places">
                                            @foreach($item->options as $value => $label)
                                                <option value="{{ $value }}" 
                                                    {{ old('decimal_places', $item->value) == $value ? 'selected' : '' }}>
                                                    {{ $label }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <x-form-error field="decimal_places" />
                                    <small class="form-text text-muted">
                                        {{ $item->description }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><iconify-icon icon="mdi:information"></iconify-icon> Bilgi</h6>
                                    <p class="mb-0">
                                        Bu ayar, teklif ve teklif ürünlerindeki para değerlerinin kullanıcı arayüzünde nasıl gösterileceğini belirler. 
                                        Veriler veritabanında her zaman 8 basamaklı ondalık olarak saklanır, sadece gösterim bu ayara göre yapılır.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3 mt-4">
                            <button type="submit" class="btn btn-primary d-flex align-items-center gap-2">
                                <iconify-icon icon="iconamoon:check-bold"></iconify-icon>
                                Kaydet
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
