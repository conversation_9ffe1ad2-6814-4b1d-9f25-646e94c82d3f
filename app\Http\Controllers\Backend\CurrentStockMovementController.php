<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportCurrentStockMovement;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CurrentStockMovementController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Stok Hareketleri';
        $this->page = 'current_stock_movements';

        $this->view = (object)[
            'breadcrumb' => [
                'Stok' => '#',
                'Cari Stok Hareketleri' => route('backend.current_stock_movements_list'),
            ],
        ];

        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('products', Product::where('is_active', 1)->orderBy('name')->get());

        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('invoice_date', function ($row) {
                if (!$row['invoice_date']) {
                    return '';
                }
                try {
                    return Carbon::parse($row['invoice_date'])->format('d.m.Y');
                } catch (\Exception $e) {
                    return '';
                }
            })
            ->addColumn('quantity_formatted', function ($row) {
                if (!isset($row['quantity']) || $row['quantity'] === null) {
                    return '0';
                }

                try {
                    $quantity = number_format((float)$row['quantity'], 2, ',', '.');
                    if (isset($row['transaction_type'])) {
                        if ($row['transaction_type'] === 'sales') {
                            return '-' . $quantity;
                        } else if ($row['transaction_type'] === 'purchase') {
                            return '+' . $quantity;
                        }
                    }
                    return $quantity;
                } catch (\Exception $e) {
                    return '0';
                }
            })
            ->addColumn('unit_price_formatted', function ($row) {
                if (!isset($row['unit_price']) || $row['unit_price'] === null) {
                    return '0,00 ₺';
                }

                try {
                    return number_format((float)$row['unit_price'], 2, ',', '.') . ' ₺';
                } catch (\Exception $e) {
                    return '0,00 ₺';
                }
            })
            // ->addColumn('total_formatted', function ($row) {
            //     if (!isset($row['total']) || $row['total'] === null) {
            //         return '0,00 ₺';
            //     }

            //     try {
            //         return number_format((float)$row['total'], 2, ',', '.') . ' ₺';
            //     } catch (\Exception $e) {
            //         return '0,00 ₺';
            //     }
            // })
            ->rawColumns(['quantity_formatted', 'unit_price_formatted']);
    }

    public function list(Request $request)
    {
        if ($request->datatable) {
            // İşlem türü filtresine göre hangi verileri çekeceğimizi belirle
            $includeSales = !$request->transaction_type || $request->transaction_type === 'sales';
            $includePurchases = !$request->transaction_type || $request->transaction_type === 'purchase';
            $includePurchaseReturns = !$request->transaction_type || $request->transaction_type === 'purchase_return';
            $includeSalesReturns = !$request->transaction_type || $request->transaction_type === 'sales_return';

            $allStockMovements = collect();

            // Satış faturaları
            if ($includeSales) {
                $salesInvoices = Invoice::with(['current', 'invoiceType'])
                    ->where('is_active', 1)
                    ->where('invoice_type_id', 2) // Satış Faturası
                    ->stockMovementFilter($request)
                    ->get();

                $salesItems = $salesInvoices->flatMap(function ($invoice) use ($request) {
                    return $invoice->getStockMovementItems($request->product_id);
                });
                $allStockMovements = $allStockMovements->merge($salesItems);
            }

            // Alış faturaları
            if ($includePurchases) {
                $purchaseInvoices = Invoice::with(['current', 'invoiceType'])
                    ->where('is_active', 1)
                    ->where('invoice_type_id', 1) // Alış Faturası
                    ->stockMovementFilter($request)
                    ->get();

                $purchaseItems = $purchaseInvoices->flatMap(function ($invoice) use ($request) {
                    return $invoice->getStockMovementItems($request->product_id);
                });
                $allStockMovements = $allStockMovements->merge($purchaseItems);
            }

            // Alış İade Faturaları
            if ($includePurchaseReturns) {
                $purchaseReturnInvoices = Invoice::with(['current', 'invoiceType'])
                    ->where('is_active', 1)
                    ->where('invoice_type_id', 3) // Alış İade Faturası
                    ->stockMovementFilter($request)
                    ->get();

                $purchaseReturnItems = $purchaseReturnInvoices->flatMap(function ($invoice) use ($request) {
                    return $invoice->getStockMovementItems($request->product_id);
                });
                $allStockMovements = $allStockMovements->merge($purchaseReturnItems);
            }

            // Satış İade Faturaları
            if ($includeSalesReturns) {
                $salesReturnInvoices = Invoice::with(['current', 'invoiceType'])
                    ->where('is_active', 1)
                    ->where('invoice_type_id', 4) // Satış İade Faturası
                    ->stockMovementFilter($request)
                    ->get();

                $salesReturnItems = $salesReturnInvoices->flatMap(function ($invoice) use ($request) {
                    return $invoice->getStockMovementItems($request->product_id);
                });
                $allStockMovements = $allStockMovements->merge($salesReturnItems);
            }

            // Tarihe göre sırala (en yeni en üstte)
            $allStockMovements = $allStockMovements->sortByDesc(function ($item) {
                try {
                    return Carbon::parse($item['invoice_date'])->timestamp;
                } catch (\Exception $e) {
                    return 0;
                }
            });

            $obj = datatables()->of($allStockMovements);
            if (method_exists($this, 'datatableHook')) {
                $obj = $this->datatableHook($obj);
            }

            return $obj->make(true);
        }

        return view('backend.current_stock_movements.list');
    }
    public function exportExcel(Request $request, $unique = null)
    {
        // İşlem türü filtresine göre hangi verileri çekeceğimizi belirle
        $includeSales = !$request->transaction_type || $request->transaction_type === 'sales';
        $includePurchases = !$request->transaction_type || $request->transaction_type === 'purchase';
        $includePurchaseReturns = !$request->transaction_type || $request->transaction_type === 'purchase_return';
        $includeSalesReturns = !$request->transaction_type || $request->transaction_type === 'sales_return';

        $allStockMovements = collect();

        // Satış faturaları
        if ($includeSales) {
            $salesInvoices = Invoice::with(['current'])
                ->where('is_active', 1)
                ->where('invoice_type_id', 2) // Satış Faturası
                ->stockMovementFilter($request)
                ->get();

            $salesItems = $salesInvoices->flatMap(function ($invoice) use ($request) {
                return $invoice->getStockMovementItems($request->product_id);
            });
            $allStockMovements = $allStockMovements->merge($salesItems);
        }

        // Alış faturaları
        if ($includePurchases) {
            $purchaseInvoices = Invoice::with(['current'])
                ->where('is_active', 1)
                ->where('invoice_type_id', 1) // Alış Faturası
                ->stockMovementFilter($request)
                ->get();

            $purchaseItems = $purchaseInvoices->flatMap(function ($invoice) use ($request) {
                return $invoice->getStockMovementItems($request->product_id);
            });
            $allStockMovements = $allStockMovements->merge($purchaseItems);
        }

        // Alış İade Faturaları
        if ($includePurchaseReturns) {
            $purchaseReturnInvoices = Invoice::with(['current'])
                ->where('is_active', 1)
                ->where('invoice_type_id', 3) // Alış İade Faturası
                ->stockMovementFilter($request)
                ->get();

            $purchaseReturnItems = $purchaseReturnInvoices->flatMap(function ($invoice) use ($request) {
                return $invoice->getStockMovementItems($request->product_id);
            });
            $allStockMovements = $allStockMovements->merge($purchaseReturnItems);
        }

        // Satış İade Faturaları
        if ($includeSalesReturns) {
            $salesReturnInvoices = Invoice::with(['current'])
                ->where('is_active', 1)
                ->where('invoice_type_id', 4) // Satış İade Faturası
                ->stockMovementFilter($request)
                ->get();

            $salesReturnItems = $salesReturnInvoices->flatMap(function ($invoice) use ($request) {
                return $invoice->getStockMovementItems($request->product_id);
            });
            $allStockMovements = $allStockMovements->merge($salesReturnItems);
        }

        // Tarihe göre sırala (en yeni en üstte)
        $allStockMovements = $allStockMovements->sortByDesc(function ($item) {
            try {
                return Carbon::parse($item['invoice_date'])->timestamp;
            } catch (\Exception $e) {
                return 0;
            }
        });

        $exporter = new ExportCurrentStockMovement($allStockMovements);
        $spreadsheet = $exporter->export();

        // Excel çıktısı için response döndür
        $writer = new Xlsx($spreadsheet);
        $filename = 'cari_stok_hareketleri.xlsx';

        // Response ile dosya indirme
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        $writer->save('php://output');
        exit;
    }
}
