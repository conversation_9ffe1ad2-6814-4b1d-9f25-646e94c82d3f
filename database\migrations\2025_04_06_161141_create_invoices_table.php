<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_no', 50)->unique()->comment('Fatura numarası (otomatik veya manuel)');
            $table->dateTime('invoice_date')->nullable()->comment('Fatura düzenlenme tarihi');
            $table->dateTime('due_date')->nullable()->comment('Vade tarihi (opsiyonel)');
            $table->integer('payment_term_id')->nullable()->comment('Ödeme şartı');
            $table->integer('current_id')->comment('<PERSON><PERSON> hesaba (müşteri/tedarikçi) referans');
            $table->integer('warehouse_id');
            $table->integer('invoice_type_id');
            $table->integer('exchange_rate_id');
            $table->integer('payment_type_id');
            $table->string('shipping_address', 255)->nullable();
            $table->decimal('net_amount', 20, 2)->default(0)->comment('Vergisiz ara toplam');
            $table->decimal('tax_amount', 20, 2)->default(0)->comment('Toplam vergi tutarı');
            $table->decimal('total_amount', 20, 2)->default(0)->comment('Genel toplam (net + vergi)');
            $table->decimal('net_amount_fx', 20, 2)->nullable()->comment('Dövizli ara toplam');
            $table->decimal('tax_amount_fx', 20, 2)->nullable()->comment('Dövizli vergi tutarı');
            $table->decimal('total_amount_fx', 20, 2)->nullable()->comment('Dövizli genel toplam');
            $table->decimal('collected',20, 2)->default(0)->comment('tahsil edilen miktar');
            $table->decimal('collected_fx', 20, 2)->default(0)->comment('tahsil edilen miktar');
            $table->integer('invoice_status_id')->default(1)->comment('Fatura durumu');
            $table->text('reason_for_cancellation')->nullable();
            $table->longText('description')->nullable()->comment('Fatura ile ilgili açıklamalar');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
